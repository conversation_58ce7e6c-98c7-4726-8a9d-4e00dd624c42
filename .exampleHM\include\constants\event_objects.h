#ifndef GUARD_CONSTANTS_EVENT_OBJECTS_H
#define GUARD_CONSTANTS_EVENT_OBJECTS_H

#define OBJ_EVENT_GFX_RED_NORMAL 0
#define OBJ_EVENT_GFX_RED_BIKE 1
#define OBJ_EVENT_GFX_RED_SURF 2
#define OBJ_EVENT_GFX_RED_FIELD_MOVE 3
#define OBJ_EVENT_GFX_RED_FISH 4
#define OBJ_EVENT_GFX_RED_VS_SEEKER 5
#define OBJ_EVENT_GFX_RED_VS_SEEKER_BIKE 6
#define OBJ_EVENT_GFX_GREEN_NORMAL 7
#define OBJ_EVENT_GFX_GREEN_BIKE 8
#define OBJ_EVENT_GFX_GREEN_SURF 9
#define OBJ_EVENT_GFX_GREEN_FIELD_MOVE 10
#define OBJ_EVENT_GFX_GREEN_FISH 11
#define OBJ_EVENT_GFX_GREEN_VS_SEEKER 12
#define OBJ_EVENT_GFX_GREEN_VS_SEEKER_BIKE 13
#define OBJ_EVENT_GFX_RS_BRENDAN 14
#define OBJ_EVENT_GFX_RS_MAY 15
#define OBJ_EVENT_GFX_LITTLE_BOY 16
#define OBJ_EVENT_GFX_LITTLE_GIRL 17
#define OBJ_EVENT_GFX_YOUNGSTER 18
#define OBJ_EVENT_GFX_BOY 19
#define OBJ_EVENT_GFX_BUG_CATCHER 20
#define OBJ_EVENT_GFX_SITTING_BOY 21
#define OBJ_EVENT_GFX_LASS 22
#define OBJ_EVENT_GFX_WOMAN_1 23
#define OBJ_EVENT_GFX_BATTLE_GIRL 24
#define OBJ_EVENT_GFX_MAN 25
#define OBJ_EVENT_GFX_ROCKER 26
#define OBJ_EVENT_GFX_FAT_MAN 27
#define OBJ_EVENT_GFX_WOMAN_2 28
#define OBJ_EVENT_GFX_BEAUTY 29
#define OBJ_EVENT_GFX_BALDING_MAN 30
#define OBJ_EVENT_GFX_WOMAN_3 31
#define OBJ_EVENT_GFX_OLD_MAN_1 32
#define OBJ_EVENT_GFX_OLD_MAN_2 33
#define OBJ_EVENT_GFX_OLD_MAN_LYING_DOWN 34
#define OBJ_EVENT_GFX_OLD_WOMAN 35
#define OBJ_EVENT_GFX_TUBER_M_WATER 36
#define OBJ_EVENT_GFX_TUBER_F 37
#define OBJ_EVENT_GFX_TUBER_M_LAND 38
#define OBJ_EVENT_GFX_CAMPER 39
#define OBJ_EVENT_GFX_PICNICKER 40
#define OBJ_EVENT_GFX_COOLTRAINER_M 41
#define OBJ_EVENT_GFX_COOLTRAINER_F 42
#define OBJ_EVENT_GFX_SWIMMER_M_WATER 43
#define OBJ_EVENT_GFX_SWIMMER_F_WATER 44
#define OBJ_EVENT_GFX_SWIMMER_M_LAND 45
#define OBJ_EVENT_GFX_SWIMMER_F_LAND 46
#define OBJ_EVENT_GFX_WORKER_M 47
#define OBJ_EVENT_GFX_WORKER_F 48
#define OBJ_EVENT_GFX_ROCKET_M 49
#define OBJ_EVENT_GFX_ROCKET_F 50
#define OBJ_EVENT_GFX_GBA_KID 51
#define OBJ_EVENT_GFX_SUPER_NERD 52
#define OBJ_EVENT_GFX_BIKER 53
#define OBJ_EVENT_GFX_BLACKBELT 54
#define OBJ_EVENT_GFX_SCIENTIST 55
#define OBJ_EVENT_GFX_HIKER 56
#define OBJ_EVENT_GFX_FISHER 57
#define OBJ_EVENT_GFX_CHANNELER 58
#define OBJ_EVENT_GFX_CHEF 59
#define OBJ_EVENT_GFX_POLICEMAN 60
#define OBJ_EVENT_GFX_GENTLEMAN 61
#define OBJ_EVENT_GFX_SAILOR 62
#define OBJ_EVENT_GFX_CAPTAIN 63
#define OBJ_EVENT_GFX_NURSE 64
#define OBJ_EVENT_GFX_CABLE_CLUB_RECEPTIONIST 65
#define OBJ_EVENT_GFX_UNION_ROOM_RECEPTIONIST 66
#define OBJ_EVENT_GFX_UNUSED_MALE_RECEPTIONIST 67
#define OBJ_EVENT_GFX_CLERK 68
#define OBJ_EVENT_GFX_MG_DELIVERYMAN 69
#define OBJ_EVENT_GFX_TRAINER_TOWER_DUDE 70
#define OBJ_EVENT_GFX_PROF_OAK 71
#define OBJ_EVENT_GFX_BLUE 72
#define OBJ_EVENT_GFX_BILL 73
#define OBJ_EVENT_GFX_LANCE 74
#define OBJ_EVENT_GFX_AGATHA 75
#define OBJ_EVENT_GFX_DAISY 76
#define OBJ_EVENT_GFX_LORELEI 77
#define OBJ_EVENT_GFX_MR_FUJI 78
#define OBJ_EVENT_GFX_BRUNO 79
#define OBJ_EVENT_GFX_BROCK 80
#define OBJ_EVENT_GFX_MISTY 81
#define OBJ_EVENT_GFX_LT_SURGE 82
#define OBJ_EVENT_GFX_ERIKA 83
#define OBJ_EVENT_GFX_KOGA 84
#define OBJ_EVENT_GFX_SABRINA 85
#define OBJ_EVENT_GFX_BLAINE 86
#define OBJ_EVENT_GFX_GIOVANNI 87
#define OBJ_EVENT_GFX_MOM 88
#define OBJ_EVENT_GFX_CELIO 89
#define OBJ_EVENT_GFX_TEACHY_TV_HOST 90
#define OBJ_EVENT_GFX_GYM_GUY 91
#define OBJ_EVENT_GFX_ITEM_BALL 92
#define OBJ_EVENT_GFX_TOWN_MAP 93
#define OBJ_EVENT_GFX_POKEDEX 94
#define OBJ_EVENT_GFX_CUT_TREE 95
#define OBJ_EVENT_GFX_ROCK_SMASH_ROCK 96
#define OBJ_EVENT_GFX_PUSHABLE_BOULDER 97
#define OBJ_EVENT_GFX_FOSSIL 98
#define OBJ_EVENT_GFX_RUBY 99
#define OBJ_EVENT_GFX_SAPPHIRE 100
#define OBJ_EVENT_GFX_OLD_AMBER 101
#define OBJ_EVENT_GFX_GYM_SIGN 102
#define OBJ_EVENT_GFX_SIGN 103
#define OBJ_EVENT_GFX_TRAINER_TIPS 104
#define OBJ_EVENT_GFX_CLIPBOARD 105
#define OBJ_EVENT_GFX_METEORITE 106
#define OBJ_EVENT_GFX_LAPRAS_DOLL 107
#define OBJ_EVENT_GFX_SEAGALLOP 108
#define OBJ_EVENT_GFX_SNORLAX 109
#define OBJ_EVENT_GFX_SPEAROW 110
#define OBJ_EVENT_GFX_CUBONE 111
#define OBJ_EVENT_GFX_POLIWRATH 112
#define OBJ_EVENT_GFX_CLEFAIRY 113
#define OBJ_EVENT_GFX_PIDGEOT 114
#define OBJ_EVENT_GFX_JIGGLYPUFF 115
#define OBJ_EVENT_GFX_PIDGEY 116
#define OBJ_EVENT_GFX_CHANSEY 117
#define OBJ_EVENT_GFX_OMANYTE 118
#define OBJ_EVENT_GFX_KANGASKHAN 119
#define OBJ_EVENT_GFX_PIKACHU 120
#define OBJ_EVENT_GFX_PSYDUCK 121
#define OBJ_EVENT_GFX_NIDORAN_F 122
#define OBJ_EVENT_GFX_NIDORAN_M 123
#define OBJ_EVENT_GFX_NIDORINO 124
#define OBJ_EVENT_GFX_MEOWTH 125
#define OBJ_EVENT_GFX_SEEL 126
#define OBJ_EVENT_GFX_VOLTORB 127
#define OBJ_EVENT_GFX_SLOWPOKE 128
#define OBJ_EVENT_GFX_SLOWBRO 129
#define OBJ_EVENT_GFX_MACHOP 130
#define OBJ_EVENT_GFX_WIGGLYTUFF 131
#define OBJ_EVENT_GFX_DODUO 132
#define OBJ_EVENT_GFX_FEAROW 133
#define OBJ_EVENT_GFX_MACHOKE 134
#define OBJ_EVENT_GFX_LAPRAS 135
#define OBJ_EVENT_GFX_ZAPDOS 136
#define OBJ_EVENT_GFX_MOLTRES 137
#define OBJ_EVENT_GFX_ARTICUNO 138
#define OBJ_EVENT_GFX_MEWTWO 139
#define OBJ_EVENT_GFX_MEW 140
#define OBJ_EVENT_GFX_ENTEI 141
#define OBJ_EVENT_GFX_SUICUNE 142
#define OBJ_EVENT_GFX_RAIKOU 143
#define OBJ_EVENT_GFX_LUGIA 144
#define OBJ_EVENT_GFX_HO_OH 145
#define OBJ_EVENT_GFX_CELEBI 146
#define OBJ_EVENT_GFX_KABUTO 147
#define OBJ_EVENT_GFX_DEOXYS_D 148
#define OBJ_EVENT_GFX_DEOXYS_A 149
#define OBJ_EVENT_GFX_DEOXYS_N 150
#define OBJ_EVENT_GFX_SS_ANNE 151

#define NUM_OBJ_EVENT_GFX     152

// These are dynamic object gfx ids.
// They correspond with the values of the VAR_OBJ_GFX_ID_X vars.
// More info about them in include/constants/vars.h
#define OBJ_EVENT_GFX_VARS   240
#define OBJ_EVENT_GFX_VAR_0  (OBJ_EVENT_GFX_VARS + 0x0) // 240
#define OBJ_EVENT_GFX_VAR_1  (OBJ_EVENT_GFX_VARS + 0x1)
#define OBJ_EVENT_GFX_VAR_2  (OBJ_EVENT_GFX_VARS + 0x2)
#define OBJ_EVENT_GFX_VAR_3  (OBJ_EVENT_GFX_VARS + 0x3)
#define OBJ_EVENT_GFX_VAR_4  (OBJ_EVENT_GFX_VARS + 0x4)
#define OBJ_EVENT_GFX_VAR_5  (OBJ_EVENT_GFX_VARS + 0x5)
#define OBJ_EVENT_GFX_VAR_6  (OBJ_EVENT_GFX_VARS + 0x6)
#define OBJ_EVENT_GFX_VAR_7  (OBJ_EVENT_GFX_VARS + 0x7)
#define OBJ_EVENT_GFX_VAR_8  (OBJ_EVENT_GFX_VARS + 0x8)
#define OBJ_EVENT_GFX_VAR_9  (OBJ_EVENT_GFX_VARS + 0x9)
#define OBJ_EVENT_GFX_VAR_A  (OBJ_EVENT_GFX_VARS + 0xA)
#define OBJ_EVENT_GFX_VAR_B  (OBJ_EVENT_GFX_VARS + 0xB)
#define OBJ_EVENT_GFX_VAR_C  (OBJ_EVENT_GFX_VARS + 0xC)
#define OBJ_EVENT_GFX_VAR_D  (OBJ_EVENT_GFX_VARS + 0xD)
#define OBJ_EVENT_GFX_VAR_E  (OBJ_EVENT_GFX_VARS + 0xE)
#define OBJ_EVENT_GFX_VAR_F  (OBJ_EVENT_GFX_VARS + 0xF) // 255

#define SHADOW_SIZE_S   0
#define SHADOW_SIZE_M   1
#define SHADOW_SIZE_L   2
#define SHADOW_SIZE_XL  3

#define F_INANIMATE                        (1 << 6)
#define F_DISABLE_REFLECTION_PALETTE_LOAD  (1 << 7)

#define TRACKS_NONE       0
#define TRACKS_FOOT       1
#define TRACKS_BIKE_TIRE  2

#define OBJ_KIND_NORMAL 0
#define OBJ_KIND_CLONE  255

// Special object event local ids
#define OBJ_EVENT_ID_PLAYER  0xFF
#define OBJ_EVENT_ID_CAMERA  0x7F

// Object event local ids referenced in C files
#define LOCALID_UNION_ROOM_PLAYER_4 2
#define LOCALID_UNION_ROOM_PLAYER_8 3
#define LOCALID_UNION_ROOM_PLAYER_7 4
#define LOCALID_UNION_ROOM_PLAYER_6 5
#define LOCALID_UNION_ROOM_PLAYER_5 6
#define LOCALID_UNION_ROOM_PLAYER_3 7
#define LOCALID_UNION_ROOM_PLAYER_2 8
#define LOCALID_UNION_ROOM_PLAYER_1 9

#endif // GUARD_CONSTANTS_EVENT_OBJECTS_H
