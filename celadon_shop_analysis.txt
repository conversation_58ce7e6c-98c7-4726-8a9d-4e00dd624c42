# Celadon Department Store Shop Analysis
# Based on .example project for FireRed U 1.0

## Shop Layout

### 2F South (Shop ID 10) - General Items
- Offset: 0x16BB38
- Purpose: Basic healing items and supplies
- Expected items: Potions, Antidotes, status healers

### 2F North (Shop ID 11) - TM Shop  
- Offset: 0x16BB74
- Purpose: Technical Machines
- Expected items: Various TMs

### 4F (Shop ID 12) - Vitamins *** MAIN GAME ***
- Offset: 0x16BC30
- Purpose: Stat-boosting vitamins
- Expected items: HP Up, Protein, Iron, Carbos, Calcium, Zinc

### 5F South (Shop ID 13) - Special Items *** MAIN GAME ***
- Offset: 0x16BC84  
- Purpose: Battle items and special supplies
- Expected items: X items, Guard Spec, Dire Hit

### 5F North (Shop ID 14) - Premium Items *** MAIN GAME ***
- Offset: 0x16BCBC
- Purpose: High-end items and rare supplies
- Expected items: Premium healing items, rare battle items

## Notes
- Shops 12, 13, 14 are marked as "MAIN GAME" in .example project
- These are the primary shops that matter for gameplay
- Shop 11 (TM shop) is separate from regular item shops
- Shop 10 (2F South) provides basic supplies

## Usage
To extract actual items from ROM:
1. Have FireRed U 1.0 ROM file
2. Run: python scripts/celadon_shop_analyzer.py <rom_path>
3. Items will be displayed with names and IDs
