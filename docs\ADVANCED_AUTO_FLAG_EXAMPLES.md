# Exemplos de Configuração do Sistema Avançado de Auto-Inicialização

Este documento mostra como personalizar o sistema avançado de auto-inicialização de flags.

## Como Adicionar Novas Flags

### **1. Flags Simples (Sempre Ativar/Desativar):**

```c
// No arquivo src/auto_flag_init.c, adicione na tabela sAutoFlagConfigs:

// Para ATIVAR uma flag sempre:
{FLAG_NOVA_FUNCIONALIDADE, TRUE, FALSE, 0},

// Para DESATIVAR uma flag sempre:
{FLAG_FUNCIONALIDADE_INDESEJADA, FALSE, FALSE, 0},
```

### **2. Flags Condicionais:**

```c
// Para ativar apenas se uma condição for atendida:
{FLAG_FUNCIONALIDADE_AVANCADA, TRUE, TRUE, FLAG_SYS_GAME_CLEAR},

// Para desativar apenas se uma condição for atendida:
{FLAG_TUTORIAL_MODE, FALSE, TRUE, FLAG_SYS_GAME_CLEAR},
```

## Exemplos Práticos

### **Exemplo 1: Desativar Temporariamente Funcionalidades Problemáticas**

```c
// Flags to CLEAR (disabled features)
{FLAG_HARD_LEVEL_CAP, FALSE, FALSE, 0},              // Disable level cap system (temporarily)
{FLAG_TRAINER_EXP_BOOST, FALSE, FALSE, 0},           // Disable trainer XP boost (temporarily)
{FLAG_SHINY_CREATION, FALSE, FALSE, 0},               // Clear any stuck shiny flag
```

### **Exemplo 2: Ativar Funcionalidades Baseadas em Progresso**

```c
// Conditional flags (only set if condition is met)
{FLAG_SYS_DEXNAV, TRUE, TRUE, FLAG_SYS_POKEDEX_GET}, // Enable DexNav if player has Pokédex
{FLAG_ADVANCED_FEATURES, TRUE, TRUE, FLAG_SYS_GAME_CLEAR}, // Enable after game completion
{FLAG_POSTGAME_CONTENT, TRUE, TRUE, FLAG_BADGE08_GET}, // Enable after 8th badge
```

### **Exemplo 3: Sistema de Debug/Testing**

```c
// Debug flags (only in debug builds)
#ifdef DEBUG
{FLAG_DEBUG_MODE, TRUE, FALSE, 0},                   // Always enable debug mode
{FLAG_INFINITE_MONEY, TRUE, FALSE, 0},               // Always enable infinite money
{FLAG_ALL_ITEMS, TRUE, FALSE, 0},                    // Always enable all items
#endif
```

## Estrutura da Configuração

### **Campos da Estrutura AutoFlagConfig:**

```c
struct AutoFlagConfig
{
    u16 flag;           // ID da flag (ex: FLAG_EXP_SHARE)
    bool8 shouldSet;    // TRUE = ativar, FALSE = desativar
    bool8 conditional;  // TRUE = tem condição, FALSE = sempre aplicar
    u16 conditionFlag;  // Flag que deve estar ativa (se conditional = TRUE)
};
```

### **Valores dos Campos:**

- **flag**: Qualquer FLAG_* definida em config.h
- **shouldSet**: 
  - `TRUE` = FlagSet(flag) será chamado
  - `FALSE` = FlagClear(flag) será chamado
- **conditional**:
  - `TRUE` = só aplica se conditionFlag estiver ativa
  - `FALSE` = sempre aplica
- **conditionFlag**: 
  - Qualquer FLAG_* que deve estar ativa
  - Ignorado se conditional = FALSE

## Casos de Uso Avançados

### **1. Sistema de Progressão por Badges:**

```c
// Ativar funcionalidades baseadas em badges
{FLAG_ADVANCED_MOVES, TRUE, TRUE, FLAG_BADGE04_GET},     // 4+ badges
{FLAG_LEGENDARY_ENCOUNTERS, TRUE, TRUE, FLAG_BADGE08_GET}, // All badges
{FLAG_POSTGAME_AREAS, TRUE, TRUE, FLAG_SYS_GAME_CLEAR},  // Game completed
```

### **2. Sistema de Limpeza de Flags Problemáticas:**

```c
// Limpar flags que podem causar problemas
{FLAG_SHINY_CREATION, FALSE, FALSE, 0},               // Clear stuck shiny flag
{FLAG_TEMP_DISABLE_RANDOMIZER, FALSE, FALSE, 0},      // Clear temp randomizer disable
{FLAG_DEBUG_QUICK_BATTLES, FALSE, FALSE, 0},          // Clear debug battle flag
```

### **3. Sistema de Migração de Save Antigo:**

```c
// Para saves antigos que não tinham certas funcionalidades
{FLAG_NEW_FEATURE_V2, TRUE, FALSE, 0},                // Always enable new feature
{FLAG_OLD_DEPRECATED_SYSTEM, FALSE, FALSE, 0},        // Always disable old system
{FLAG_COMPATIBILITY_MODE, FALSE, TRUE, FLAG_NEW_FEATURE_V2}, // Disable compat if new feature active
```

## Vantagens do Sistema Avançado

### **✅ Flexibilidade Total:**
- Pode ativar OU desativar flags
- Suporte a condições complexas
- Fácil de modificar sem recompilar

### **✅ Manutenção Simples:**
- Todas as configurações em um local
- Comentários explicativos
- Estrutura clara e organizada

### **✅ Debugging Facilitado:**
- Pode temporariamente desativar funcionalidades problemáticas
- Pode limpar flags que ficaram "presas"
- Pode testar diferentes configurações facilmente

### **✅ Compatibilidade:**
- Funciona com saves existentes
- Não quebra funcionalidades existentes
- Permite migração gradual de sistemas

## Como Testar Suas Configurações

### **1. Verificar se as Flags Estão Sendo Aplicadas:**

```c
// No debug menu ou via script:
if (FlagGet(FLAG_SUA_NOVA_FLAG))
    // Flag foi ativada corretamente
else
    // Flag não foi ativada (verificar configuração)
```

### **2. Testar Condições:**

```c
// Verificar se a condição está sendo respeitada:
if (FlagGet(FLAG_CONDICAO) && FlagGet(FLAG_RESULTADO))
    // Condição funcionando
else if (!FlagGet(FLAG_CONDICAO) && !FlagGet(FLAG_RESULTADO))
    // Condição funcionando (não ativou porque condição não foi atendida)
else
    // Problema na configuração
```

### **3. Reset para Testar:**

```c
// Para testar novamente:
FlagClear(FLAG_AUTO_INIT_FEATURES);  // Remove flag de controle
CheckAndInitializeAutoFlags();       // Força nova inicialização
```

## Notas Importantes

- **Ordem importa**: Flags são processadas na ordem da tabela
- **Condições são verificadas**: Se conditionFlag não estiver ativa, a flag não é aplicada
- **Sempre terminar com {0, FALSE, FALSE, 0}**: Marca o fim da tabela
- **Comentários são importantes**: Documente o propósito de cada flag
- **Testar thoroughly**: Sempre teste suas configurações em diferentes cenários
