# 🎯 **CFRU Move Relearner Guide**

## 📋 **Resumo**

Este guia explica como encontrar e implementar NPCs Move Relearner (Recordador de Movimentos) no projeto CFRU.

---

## 🔧 **Sistema Move Relearner no CFRU**

### **Funcionalidades Disponíveis**

✅ **Sistema completo implementado**
- Interface gráfica com menus nativos
- Suporte para até 50 movimentos reaprendíveis
- Compatível com randomização de movesets
- Opção para ignorar restrições de nível

✅ **Recursos especiais**
- `FLAG_MOVE_RELEARNER_IGNORE_LEVEL`: Permite reaprender qualquer movimento
- Integração com Heart Scale (opcional)
- Mensagens personalizáveis
- Sistema de validação de Pokémon

---

## 🎮 **Como Usar**

### **1️⃣ Script Individual**

O script `EventScript_MoveRelearner` está disponível em seu próprio arquivo:
```
assembly/overworld_scripts/Move_Relearner.s
```

### **2️⃣ Implementar NPC**

Adicione ao arquivo `eventscripts`:
```
# Move Relearner em Two Island
npc 23 0 1 EventScript_MoveRelearner

# Move Relearner em Saffron Pokemon Center  
npc 5 5 3 EventScript_MoveRelearner

# Move Relearner em Celadon Pokemon Center
npc 6 5 2 EventScript_MoveRelearner
```

### **3️⃣ Compilar**

```bash
python scripts/make.py
```

---

## ⚙️ **Configurações Opcionais**

### **Heart Scale Requirement**

Para exigir Heart Scale, descomente estas linhas no script:
```assembly
@ checkitem ITEM_HEART_SCALE 1
@ compare LASTRESULT FALSE  
@ if equal _goto EventScript_MoveRelearner_NoHeartScale

@ removeitem ITEM_HEART_SCALE 1
```

### **Ignorar Restrições de Nível**

Ative a flag no jogo:
```
FLAG_MOVE_RELEARNER_IGNORE_LEVEL
```

---

## 📍 **Localizações Sugeridas**

### **Pokémon Centers**
- Saffron City: `npc 5 5 3 EventScript_MoveRelearner`
- Celadon City: `npc 6 5 2 EventScript_MoveRelearner`
- Fuchsia City: `npc 7 5 2 EventScript_MoveRelearner`

### **Sevii Islands**
- Two Island: `npc 23 0 1 EventScript_MoveRelearner`
- Four Island: `npc 25 0 2 EventScript_MoveRelearner`

### **Special Locations**
- Indigo Plateau: `npc 9 0 3 EventScript_MoveRelearner`

---

## 🔍 **Encontrar Map Groups/IDs**

Use ferramentas como **AdvanceMap** para identificar:
- Map Group (grupo do mapa)
- Map Number (número do mapa)  
- NPC ID (ID do NPC a substituir)

---

## ✅ **Verificação**

### **Teste no Jogo**
1. Fale com o NPC
2. Confirme que o menu Move Relearner abre
3. Teste com Pokémon que esqueceram movimentos
4. Verifique se Heart Scale é consumida (se ativado)

### **Mensagens Esperadas**
- Boas-vindas do Move Relearner
- Pergunta se quer usar o serviço
- Confirmação após reaprender movimento
- Mensagens de erro apropriadas

---

## 🎯 **Função Principal**

O sistema usa a função correta do FireRed:
```c
CB2_InitLearnMove = 0x80E478C | 1;
```

Chamada via:
```assembly
closemessage
gotonative CB2_InitLearnMove
```

⚠️ **IMPORTANTE**: Não use `callnative Move_Relearner` - isso causa conflitos com o tutorial de captura!

---

## 📝 **Arquivos Envolvidos**

```
assembly/overworld_scripts/Move_Relearner.s  # Script principal (individual)
strings/Scripts/Move_Relearner.string        # Textos
eventscripts                                 # Integração NPC
```

---

## 🚀 **Pronto para Usar!**

O sistema Move Relearner está completamente implementado e pronto para uso no seu projeto CFRU!
