.text
.thumb
.align 2
/*
mega_battle_scripts.s
	handles mega evolution
*/

.include "../asm_defines.s"
.include "../battle_script_macros.s"

.global BattleScript_MegaEvolution
.global BattleScript_MegaWish
.global BattleScript_UltraBurst
.global BattleScript_Dynamax

BattleScript_MegaEvolution:
	setword BATTLE_STRING_LOADER MegaReactingString
	printstring 0x184
	waitmessage DELAY_HALFSECOND

MegaAnimBS:
	playanimation BANK_SCRIPTING ANIM_MEGA_EVOLUTION 0x0
	reloadhealthbar BANK_SCRIPTING
	setword BATTLE_STRING_LOADER MegaEvolutionCompleteString
	printstring 0x184
	waitmessage DELAY_1SECOND
	callasm TryRemovePrimalWeatherAfterTransformation
	end3

BattleScript_MegaWish:
	setword BATTLE_STRING_LOADER FerventWishString
	printstring 0x184
	waitmessage DELAY_HALFSECOND
	goto MegaAnimBS

BattleScript_UltraBurst:
	setword BATTLE_STRING_LOADER UltraBurstGlowingString
	printstring 0x184
	waitmessage DELAY_HALFSECOND
	playanimation BANK_SCRIPTING ANIM_ULTRA_BURST 0x0
	reloadhealthbar BANK_SCRIPTING
	setword BATTLE_STRING_LOADER UltraBurstCompleteString
	printstring 0x184
	waitmessage DELAY_1SECOND
	callasm TryRemovePrimalWeatherAfterTransformation
	end3

BattleScript_Dynamax:
	call BS_FLUSH_MESSAGE_BOX
	callasm UpdateHPForDynamax
	playanimation BANK_SCRIPTING ANIM_CALL_BACK_POKEMON
	waitanimation
	pause DELAY_1SECOND
	pause DELAY_HALFSECOND
	returntoball BANK_SCRIPTING
	call BattleScript_TryRevertCramorant

BattleScript_Dynamax_Rejoin:
	waitstateatk
	callasm TryDoDynamaxTrainerSlide
	callasm SetAndTransferDontRemoveTransformSpecies
	callasm BackupScriptingBankMoveSelectionCursor @;Prevents the move selection cursor from being reset by the switch-in anim
	switchinanim BANK_SCRIPTING 0x1 @;Play the switch-in animation
	waitanimation
	callasm RestoreScriptingBankMoveSelectionCursor
	callasm ClearAndTransferDontRemoveTransformSpecies
	playanimation BANK_SCRIPTING ANIM_DYNAMAX_START 0x0
	orword HIT_MARKER, HITMARKER_IGNORE_SUBSTITUTE
	graphicalhpupdate BANK_SCRIPTING
	datahpupdate BANK_SCRIPTING
	bicword HIT_MARKER, HITMARKER_IGNORE_SUBSTITUTE
	setword BATTLE_STRING_LOADER gText_MonDynamaxed
	printstring 0x184
	waitmessage DELAY_1SECOND
	end3

BattleScript_TryRevertCramorant:
	formchange BANK_SCRIPTING SPECIES_CRAMORANT_GULPING SPECIES_CRAMORANT TRUE TRUE FALSE BattleScript_TryRevertGorgingCramorant
BattleScript_TryRevertGorgingCramorant:
	formchange BANK_SCRIPTING SPECIES_CRAMORANT_GORGING SPECIES_CRAMORANT TRUE TRUE FALSE BattleScript_Dynamax_Rejoin
	goto BattleScript_Dynamax_Rejoin

.align 2
@;FD 00's FD 16 is reacting\nto FD 04's FD 01!
MegaReactingString: .byte 0xFD, 0x0, 0xB4, 0xE7, 0x00, 0xFD, 0x16, 0x00, 0xDD, 0xE7, 0x00, 0xE6, 0xD9, 0xD5, 0xD7, 0xE8, 0xDD, 0xE2, 0xDB, 0xFE, 0xE8, 0xE3, 0x00, 0xFD, 0x39, 0xB4, 0xE7, 0x00, 0xFD, 0x01, 0xAB, 0xFF
MegaEvolutionCompleteString: .byte 0xFD, 0x13, 0x00, 0xC7, 0xD9, 0xDB, 0xD5, 0x00, 0xBF, 0xEA, 0xE3, 0xE0, 0xEA, 0xD9, 0xD8, 0xFE, 0xDD, 0xE2, 0xE8, 0xE3, 0x00, 0xC7, 0xD9, 0xDB, 0xD5, 0x00, 0xFD, 0x34, 0xAB, 0xFF
FerventWishString: .byte 0xFD, 0x39, 0xB4, 0xE7, 0x00, 0xDA, 0xD9, 0xE6, 0xEA, 0xD9, 0xE2, 0xE8, 0x00, 0xEB, 0xDD, 0xE7, 0xDC, 0xFE, 0xDC, 0xD5, 0xE7, 0x00, 0xE6, 0xD9, 0xD5, 0xD7, 0xDC, 0xD9, 0xD8, 0x00, 0xFD, 0x00, 0xAB, 0xFF
UltraBurstGlowingString: .byte 0xBC, 0xE6, 0xDD, 0xDB, 0xDC, 0xE8, 0x00, 0xE0, 0xDD, 0xDB, 0xDC, 0xE8, 0x00, 0xDD, 0xE7, 0x00, 0xD5, 0xD6, 0xE3, 0xE9, 0xE8, 0x00, 0xE8, 0xE3, 0x00, 0xD6, 0xE9, 0xE6, 0xE7, 0xE8, 0x00, 0xE3, 0xE9, 0xE8, 0x00, 0xE3, 0xDA, 0xFE, 0xFD, 0x13, 0xAB, 0xFF
UltraBurstCompleteString: .byte 0xFD, 0x13, 0x00, 0xE6, 0xD9, 0xDB, 0xD5, 0xDD, 0xE2, 0xD9, 0xD8, 0x00, 0xDD, 0xE8, 0xE7, 0xFE, 0xE8, 0xE6, 0xE9, 0xD9, 0x00, 0xE4, 0xE3, 0xEB, 0xD9, 0xE6, 0x00, 0xE8, 0xDC, 0xE6, 0xE3, 0xE9, 0xDB, 0xDC, 0x00, 0xCF, 0xE0, 0xE8, 0xE6, 0xD5, 0x00, 0xBC, 0xE9, 0xE6, 0xE7, 0xE8, 0xAB, 0xFF
