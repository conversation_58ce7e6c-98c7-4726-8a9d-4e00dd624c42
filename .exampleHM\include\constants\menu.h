#ifndef GUARD_CONSTANTS_MENU_H
#define GUARD_CONSTANTS_MENU_H

#define SCR_MENU_CANCEL 127
#define SCR_MENU_UNSET  255

#define MULTICHOICE_YES_NO                                      0
#define MULTICHOICE_EEVEELUTIONS                                1
#define MULTICHOICE_TRAINER_CARD_ICON_TINT                      2
#define MULTICHOICE_HOF_QUIT                                    3
#define MULTICHOICE_EGGS_QUIT                                   4
#define MULTICHOICE_VICTORIES_QUIT                              5
#define MULTICHOICE_HOF_EGGS_QUIT                               6
#define MULTICHOICE_HOF_VICTORIES_QUIT                          7
#define MULTICHOICE_EGGS_VICTORIES_QUIT                         8
#define MULTICHOICE_HOF_EGGS_VICTORIES_QUIT                     9
#define MULTICHOICE_EXIT                                       10
#define MULTICHOICE_EXIT_2                                     11
#define MULTICHOICE_EXIT_3                                     12
#define MULTICHOICE_BIKE_SHOP                                  13
#define MULTICHOICE_GAME_CORNER_POKEMON_PRIZES                 14
#define MULTICHOICE_TRAINER_SCHOOL_WHITEBOARD                  15
#define MULTICHOICE_YES_NO_INFO                                16
#define MULTICHOICE_SINGLE_DOUBLE_MULTI_INFO_EXIT              17
#define MULTICHOICE_YES_NO_INFO_2                              18
#define MULTICHOICE_CHALLENGE_INFO_EXIT                        19
#define MULTICHOICE_ROOFTOP_B1F                                20
#define MULTICHOICE_HELIX                                      21
#define MULTICHOICE_DOME                                       22
#define MULTICHOICE_AMBER                                      23
#define MULTICHOICE_HELIX_AMBER                                24
#define MULTICHOICE_DOME_AMBER                                 25
#define MULTICHOICE_CELADON_VENDING_MACHINE                    26
#define MULTICHOICE_GAME_CORNER_COIN_PURCHASE_COUNTER          27
#define MULTICHOICE_EXCELLENT_NOT_SO_BAD                       28
#define MULTICHOICE_RIGHT_LEFT                                 29
#define MULTICHOICE_GAME_CORNER_TMPRIZES                       30
#define MULTICHOICE_DEPT_STORE_ELEVATOR                        31
#define MULTICHOICE_THIRSTY_GIRL_FRESH_WATER                   32
#define MULTICHOICE_THIRSTY_GIRL_SODA_POP                      33
#define MULTICHOICE_THIRSTY_GIRL_FRESH_WATER_SODA_POP          34
#define MULTICHOICE_THIRSTY_GIRL_LEMONADE                      35
#define MULTICHOICE_THIRSTY_GIRL_FRESH_WATER_LEMONADE          36
#define MULTICHOICE_THIRSTY_GIRL_SODA_POP_LEMONADE             37
#define MULTICHOICE_THIRSTY_GIRL_FRESH_WATER_SODA_POP_LEMONADE 38
#define MULTICHOICE_TRADE_CENTER_COLOSSEUM                     39
#define MULTICHOICE_LINK_WIRELESS                              40
#define MULTICHOICE_GAME_CORNER_BATTLE_ITEM_PRIZES             41
#define MULTICHOICE_ROCKET_HIDEOUT_ELEVATOR                    42
#define MULTICHOICE_LINKED_DIRECT_UNION                        43
#define MULTICHOICE_ISLAND_23                                  44
#define MULTICHOICE_ISLAND_13                                  45
#define MULTICHOICE_ISLAND_12                                  46
#define MULTICHOICE_TRADE_COLOSSEUM_CRUSH                      47
#define MULTICHOICE_48                                         48
#define MULTICHOICE_POKEJUMP_DODRIO                            49
#define MULTICHOICE_TRADE_COLOSSEUM_2                          50
#define MULTICHOICE_MUSHROOMS                                  51
#define MULTICHOICE_TRADE_COLOSSEUM_BLANK_CRUSH                52
#define MULTICHOICE_TRADE_COLOSSEUM_BLANK                      53
#define MULTICHOICE_SEVII_NAVEL                                54
#define MULTICHOICE_SEVII_BIRTH                                55
#define MULTICHOICE_SEVII_NAVEL_BIRTH                          56
#define MULTICHOICE_SEAGALLOP_123                              57
#define MULTICHOICE_SEAGALLOP_V23                              58
#define MULTICHOICE_SEAGALLOP_V13                              59
#define MULTICHOICE_SEAGALLOP_V12                              60
#define MULTICHOICE_SEAGALLOP_VERMILION                        61
#define MULTICHOICE_62                                         62
#define MULTICHOICE_JOIN_OR_LEAD                               63
#define MULTICHOICE_TRAINER_TOWER_MODE                         64

#define MULTICHOICE_NONE                                      255

#define LISTMENU_BADGES                    0
#define LISTMENU_SILPHCO_FLOORS            1
#define LISTMENU_ROCKET_HIDEOUT_FLOORS     2
#define LISTMENU_DEPT_STORE_FLOORS         3
#define LISTMENU_WIRELESS_LECTURE_HEADERS  4
#define LISTMENU_BERRY_POWDER              5
#define LISTMENU_TRAINER_TOWER_FLOORS      6

// Std String Ids
#define STDSTRING_COOL              0
#define STDSTRING_BEAUTY            1
#define STDSTRING_CUTE              2
#define STDSTRING_SMART             3
#define STDSTRING_TOUGH             4
#define STDSTRING_COOL2             5
#define STDSTRING_BEAUTY2           6
#define STDSTRING_CUTE2             7
#define STDSTRING_SMART2            8
#define STDSTRING_TOUGH2            9
#define STDSTRING_ITEMS             10
#define STDSTRING_KEY_ITEMS         11
#define STDSTRING_POKEBALLS         12
#define STDSTRING_TMHMS             13
#define STDSTRING_BERRIES           14
#define STDSTRING_BOULDER_BADGE     15
#define STDSTRING_CASCADE_BADGE     16
#define STDSTRING_THUNDER_BADGE     17
#define STDSTRING_RAINBOW_BADGE     18
#define STDSTRING_SOUL_BADGE        19
#define STDSTRING_MARSH_BADGE       20
#define STDSTRING_VOLCANO_BADGE     21
#define STDSTRING_EARTH_BADGE       22
#define STDSTRING_COINS             23
#define STDSTRING_ITEMS_POCKET      24
#define STDSTRING_KEY_ITEMS_POCKET  25
#define STDSTRING_POKEBALLS_POCKET  26
#define STDSTRING_TM_CASE           27
#define STDSTRING_BERRY_POUCH       28

#endif //GUARD_CONSTANTS_MENU_H
