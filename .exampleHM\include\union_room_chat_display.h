#ifndef GUARD_UNION_ROOM_CHAT_DISPLAY_H
#define GUARD_UNION_ROOM_CHAT_DISPLAY_H

#define CHATDISPLAYROUTINE_LOADGFX  0
#define CHATDISPLAYROUTINE_MOVEKBCURSOR  1
#define CHATDISPLAYROUTINE_CURSORBLINK  2
#define CHATDISPLAYROUTINE_SHOWKBSWAPMENU  3
#define CHATDISPLAYROUTINE_HIDEKBSWAPMENU  4
#define CHATDISPLAYROUTINE_SWITCHPAGES  5
#define CHATDISPLAYROUTINE_SHOWQUITCHATTINGDIALOG  6
#define CHATDISPLAYROUTINE_DESTROYSTDMSGANDYESNO  7
#define CHATDISPLAYROUTINE_PRINTMSG  8
#define CHATDISPLAYROUTINE_PRINTREGISTERWHERE  9
#define CHATDISPLAYROUTINE_CANCELREGISTER 10
#define CHATDISPLAYROUTINE_RETURNTOKB 11
#define CHATDISPLAYROUTINE_SCROLLCHAT 12
#define CHATDISPLAYROUTINE_PRINTINPUTTEXT 13
#define CHATDISPLAYROUTINE_ASKSAVE 14
#define CHATDISPLAYROUTINE_ASKOVERWRITESAVE 15
#define CHATDISPLAYROUTINE_PRINTSAVING 16
#define CHATDISPLAYROUTINE_PRINTSAVEDTHEGAME 17
#define CHATDISPLAYROUTINE_PRINTEXITINGCHAT 18
#define CHATDISPLAYROUTINE_PRINTLEADERLEFT 19
#define CHATDISPLAYROUTINE_SHOWCONFIRMLEADERLEAVEDIALOG 20

bool8 UnionRoomChat_TryAllocGraphicsWork(void);
bool32 UnionRoomChat_RunDisplaySubtask0(void);
void UnionRoomChat_FreeGraphicsWork(void);
void UnionRoomChat_RunDisplaySubtasks(void);
void UnionRoomChat_StartDisplaySubtask(u16 a0, u8 a1);
u8 RunDisplaySubtask(u8 a0);
s8 UnionRoomChat_ProcessInput(void);

#endif //GUARD_UNION_ROOM_CHAT_DISPLAY_H
