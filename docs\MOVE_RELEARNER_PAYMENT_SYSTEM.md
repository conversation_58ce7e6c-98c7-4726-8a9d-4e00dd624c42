# 💰 **CFRU Move Relearner - SISTEMA DE PAGAMENTO**

## 🎯 **Sistema Implementado: $1000 Pokédollars**

### **✅ Funcionalidades**
- **Verificação de dinheiro** antes do serviço
- **Cobrança automática** após sucesso
- **Mensagens informativas** sobre o custo
- **Tratamento de erro** para dinheiro insuficiente

---

## 💸 **Fluxo de Pagamento**

### **1. Verificação Inicial**
```assembly
@ Check if player has enough money (1000 Pokédollars)
checkmoney 1000 0
compare LASTRESULT FALSE
if equal _goto EventScript_MoveRelearner_NoMoney
```

### **2. Cobrança Após Sucesso**
```assembly
@ Charge the money (1000 Pokédollars)
removemoney 1000 0

@ Success message and end
msgbox gText_MoveRelearner_Success MSG_NORMAL
```

### **3. Tratamento de Erro**
```assembly
EventScript_MoveRelearner_NoMoney:
    msgbox gText_MoveRelearner_NoMoney MSG_NORMAL
    release
    end
```

---

## 📝 **Mensagens Implementadas**

### **💰 Informação de Preço**
```
"Would you like me to help one of
your Pokémon remember a move?

My service costs $1000."
```

### **❌ Dinheiro Insuficiente**
```
"I'm sorry, but you don't have
enough money for my service.

I need $1000 to help your
Pokémon remember moves."
```

### **✅ Sucesso e Cobrança**
```
"There! Your POKéMON learned
the move successfully!

That'll be $1000. Thank you!"
```

---

## 🎮 **Fluxo Completo do Serviço**

1. **Diálogo inicial** ✅
2. **Verificação de party** ✅ (`countpokemon`)
3. **Informação de preço** ✅ ("My service costs $1000")
4. **Confirmação Yes/No** ✅
5. **Verificação de dinheiro** ✅ (`checkmoney 1000 0`)
6. **Party selection** ✅ (`special 0xDB`)
7. **Verificação de movimentos** ✅ (`special 0x148`)
8. **Move selection** ✅ (`special 0xE0`)
9. **Cobrança do dinheiro** ✅ (`removemoney 1000 0`)
10. **Mensagem de sucesso** ✅

---

## 🔧 **Comandos XSE Utilizados**

| **Comando** | **Função** | **Parâmetros** |
|-------------|------------|----------------|
| `checkmoney` | Verifica se player tem dinheiro | `1000 0` |
| `removemoney` | Remove dinheiro do player | `1000 0` |
| `special 0xDB` | Party selection para Move Tutor | - |
| `special 0x148` | Verifica movimentos aprendíveis | - |
| `special 0xE0` | Interface de seleção de movimentos | - |

---

## 💡 **Vantagens do Sistema**

### **✅ Conveniente**
- Não requer itens específicos
- Dinheiro é fácil de obter no jogo
- Preço justo ($1000)

### **✅ Realista**
- Serviços custam dinheiro
- Simula economia do jogo
- Valor similar a outros serviços

### **✅ Flexível**
- Fácil de ajustar o preço
- Pode ser desabilitado facilmente
- Mensagens claras sobre custo

---

## 🎯 **Comparação com Outros Sistemas**

| **Sistema** | **Vantagens** | **Desvantagens** |
|-------------|---------------|------------------|
| **$1000 (Atual)** | Conveniente, realista | Pode ser caro no início |
| **Heart Scale** | Tradicional | Requer farming específico |
| **Mushrooms** | Como original FireRed | Itens raros |
| **Gratuito** | Sem barreiras | Menos realista |

---

## 🔧 **Customização Fácil**

### **Alterar Preço**
```assembly
@ Mudar de 1000 para outro valor
checkmoney 500 0    # $500
removemoney 500 0   # $500
```

### **Desabilitar Pagamento**
```assembly
@ Comentar ou remover estas linhas:
@ checkmoney 1000 0
@ compare LASTRESULT FALSE
@ if equal _goto EventScript_MoveRelearner_NoMoney
@ removemoney 1000 0
```

### **Usar Item em Vez de Dinheiro**
```assembly
@ Exemplo com Heart Scale
checkitem ITEM_HEART_SCALE 1
compare LASTRESULT FALSE
if equal _goto EventScript_MoveRelearner_NoHeartScale
removeitem ITEM_HEART_SCALE 1
```

---

## 📍 **NPCs Ativos com Sistema de Pagamento**

- **Two Island:** Map 23, 0, NPC ID 0 ✅
- **Celadon Game Corner:** Map 10, 12, NPC ID 1 ✅

---

## 🚀 **Status Final**

- ✅ **Sistema de pagamento implementado**
- ✅ **Verificação de dinheiro funcional**
- ✅ **Cobrança automática após sucesso**
- ✅ **Mensagens informativas**
- ✅ **Tratamento de erro para dinheiro insuficiente**
- ✅ **Compilação bem-sucedida**
- ✅ **Pronto para teste no jogo**

---

## 🎉 **Resultado Final**

**O Move Relearner agora:**
1. ✅ Informa o preço ($1000) antes do serviço
2. ✅ Verifica se o player tem dinheiro suficiente
3. ✅ Funciona com a sequência original do FireRed
4. ✅ Cobra automaticamente após o sucesso
5. ✅ Trata erros de dinheiro insuficiente
6. ✅ Mantém a experiência nativa do jogo

**SISTEMA DE PAGAMENTO COMPLETO E FUNCIONAL! 💰**
