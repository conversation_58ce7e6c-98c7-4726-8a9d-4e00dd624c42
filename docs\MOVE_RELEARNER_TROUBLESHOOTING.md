# 🚨 **CFRU Move Relearner Troubleshooting**

## 🔍 **Common Issue: Catching Tutorial Instead of Move Relearner**

### **Problem Description**
When talking to the Move Relearner NPC, the dialogue appears normally, but after the Yes/No confirmation, instead of opening the Move Relearner interface, it triggers the Pokémon catching tutorial.

### **Root Cause**
The issue is caused by using **TWO WRONG COMMANDS** in the script:

❌ **INCORRECT #1 (causes tutorial):**
```assembly
special2 LASTRESULT 0x9C  @ 0x9C = OldManBattleModifier, NOT GetPartyCount!
```

❌ **INCORRECT #2 (causes tutorial):**
```assembly
callnative Move_Relearner  @ 0x80E4634 - WRONG FUNCTION!
```

✅ **CORRECT (opens Move Relearner):**
```assembly
countpokemon  @ Correct command for party count
closemessage
gotonative CB2_InitLearnMove  @ 0x80E478C - CORRECT FUNCTION!
```

---

## 🛠️ **The Fix**

### **1. Function Analysis**

From `BPRE.ld`, there are multiple Move Relearner functions:
```c
Move_Relearner = 0x80E4634 | 1;           // ❌ Internal function (not for scripts)
CB2_InitLearnMove = 0x80E478C | 1;        // ✅ Script-callable function
CB2_MoveRelearnerMain = 0x80E4870 | 1;    // Internal function
```

### **2. Correct Script Implementation**

```assembly
EventScript_MoveRelearner:
    lock
    faceplayer
    msgbox gText_MoveRelearner_Welcome MSG_NORMAL

    @ FIXED: Check if player has Pokemon using correct command
    countpokemon
    compare LASTRESULT 0
    if equal _goto EventScript_MoveRelearner_NoMons

    @ Ask if player wants to use Move Relearner
    msgbox gText_MoveRelearner_AskUse MSG_YESNO
    compare LASTRESULT NO
    if equal _goto EventScript_MoveRelearner_Decline

    @ CRITICAL: Close message box before interface transition
    closemessage

    @ FIXED: Use the correct function
    gotonative CB2_InitLearnMove

    @ Script execution stops here - no need for release/end
```

---

## 🔧 **Key Differences**

### **callnative vs gotonative**

- **`callnative`**: Calls function and returns to script
- **`gotonative`**: Replaces script with function (no return)

### **Why gotonative is correct**

The Move Relearner interface is a **complete screen replacement**, not a function that returns to the script. Using `gotonative` properly transitions to the Move Relearner interface.

### **Why closemessage is needed**

The `closemessage` command ensures the message box is properly closed before transitioning to the Move Relearner interface, preventing display conflicts.

---

## ✅ **Verification Steps**

1. **Test the dialogue**: NPC should respond normally
2. **Test Yes/No**: Confirmation should work properly  
3. **Test interface**: Should open Move Relearner, NOT catching tutorial
4. **Test functionality**: Should be able to select Pokémon and relearn moves
5. **Test return**: Should return to overworld after Move Relearner

---

## 🎯 **Additional Notes**

### **Function Purpose Clarification**
- `Move_Relearner (0x80E4634)`: Internal game function, not meant for direct script calls
- `CB2_InitLearnMove (0x80E478C)`: Proper entry point for scripts to access Move Relearner

### **Why the Tutorial Triggers**

**Problem #1: Wrong Special Command**
- `special2 LASTRESULT 0x9C` calls `sp09C_OldManBattleModifier()`
- This function creates an Old Man tutorial battle with `BATTLE_TYPE_OLD_MAN`
- It's NOT a party count checker - it's a battle starter!

**Problem #2: Wrong Function Offset**
- The wrong function offset likely points to or interferes with tutorial-related code
- This causes the game to think a tutorial should be triggered instead of the Move Relearner

**The Fix:**
- Use `countpokemon` (XSE command 0x43) for party count checking
- Use `gotonative CB2_InitLearnMove` for Move Relearner interface

---

## 🚀 **Final Working Script**

The corrected script is available in its own individual file:
```
assembly/overworld_scripts/Move_Relearner.s
```

Use `EventScript_MoveRelearner` with the fixed implementation for proper Move Relearner functionality.

---

## 📞 **If Issues Persist**

If you're still experiencing problems:

1. **Verify compilation**: Ensure `python scripts/make.py` completes without errors
2. **Check function offsets**: Confirm `CB2_InitLearnMove = 0x80E478C | 1` in BPRE.ld
3. **Test with different NPCs**: Try the script on different NPC IDs
4. **Check for conflicts**: Ensure no other scripts are interfering

The fix provided should resolve the catching tutorial issue completely.
