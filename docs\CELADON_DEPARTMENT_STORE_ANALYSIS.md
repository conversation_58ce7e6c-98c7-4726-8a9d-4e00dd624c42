# 🏬 Celadon Department Store - Análise Completa dos Shops

## 📊 **Baseado no Projeto .example para FireRed U 1.0**

Esta análise identifica exatamente quais itens cada shop de Celadon vende, baseada nos offsets extraídos do projeto .example.

---

## 🏪 **SHOPS IDENTIFICADOS**

### **Shop ID 10: Celadon Department 2F South**
- **📍 Offset**: `0x16BB38`
- **🔗 ROM Address**: `0x0816BB38`
- **🎯 Propósito**: General Items
- **📂 Categoria**: Healing & Supplies
- **🛍️ Itens Esperados**: Potions, Antidotes, Status healers, Repels

### **Shop ID 11: Celadon Department 2F North (TMs)**
- **📍 Offset**: `0x16BB74`
- **🔗 ROM Address**: `0x0816BB74`
- **🎯 Propósito**: Technical Machines
- **📂 Categoria**: TM Shop
- **🛍️ Itens Esperados**: Various Technical Machines

### **Shop ID 12: Celadon Department 4F *** MAIN GAME *****
- **📍 Offset**: `0x16BC30`
- **🔗 ROM Address**: `0x0816BC30`
- **🎯 Propósito**: Vitamins
- **📂 Categoria**: Stat Enhancement
- **🛍️ Itens Esperados**: HP Up, Protein, Iron, Carbos, Calcium, Zinc

### **Shop ID 13: Celadon Department 5F South *** MAIN GAME *****
- **📍 Offset**: `0x16BC84`
- **🔗 ROM Address**: `0x0816BC84`
- **🎯 Propósito**: Special Items
- **📂 Categoria**: Battle Enhancement
- **🛍️ Itens Esperados**: X Attack, X Defense, X Speed, Guard Spec, Dire Hit

### **Shop ID 14: Celadon Department 5F North *** MAIN GAME *****
- **📍 Offset**: `0x16BC98`
- **🔗 ROM Address**: `0x0816BC98`
- **🎯 Propósito**: Evolution Items
- **📂 Categoria**: Evolution Items
- **🛍️ Itens Esperados**: Fire Stone, Water Stone, Thunder Stone, Leaf Stone

---

## 🔍 **ESPECIALIZAÇÃO POR ANDAR**

### **🏥 2º Andar Sul - HEALING & SUPPLIES**
- **Função**: Itens básicos de sobrevivência
- **Itens**: Poções, antídotos, curadores de status, repelentes
- **Importância**: Shop essencial para aventura

### **💿 2º Andar Norte - TM SHOP**
- **Função**: Aprendizado de movimentos
- **Itens**: Technical Machines variadas
- **Importância**: Expansão de movesets

### **💪 4º Andar - VITAMINS *** MAIN GAME *****
- **Função**: Melhoramento de stats
- **Itens**: HP Up, Protein, Iron, Carbos, Calcium, Zinc
- **Importância**: Otimização competitiva

### **⚔️ 5º Andar Sul - BATTLE ITEMS *** MAIN GAME *****
- **Função**: Melhoramento de batalha
- **Itens**: X Attack, X Defense, X Speed, Guard Spec, Dire Hit
- **Importância**: Vantagem tática

### **🔮 5º Andar Norte - EVOLUTION ITEMS *** MAIN GAME *****
- **Função**: Evolução de Pokémon
- **Itens**: Fire Stone, Water Stone, Thunder Stone, Leaf Stone
- **Importância**: Progressão de equipe

---

## 💡 **POTENCIAL DE MODIFICAÇÃO PARA CFRU**

### **🏪 Shop 10 (2F South) - General Items**
**Atual**: Itens básicos de cura
**Adições Potenciais**:
- Novos itens de cura do CFRU
- Berries especiais
- Itens de condição de status
- Repelentes melhorados

### **🏪 Shop 11 (2F North) - TM Shop**
**Atual**: Seleção limitada de TMs
**Adições Potenciais**:
- TM70 (confirmado funcionando)
- Novos TMs não obtíveis normalmente
- Itens de move tutoring
- Equivalentes de TR

### **🏪 Shop 12 (4F) - Vitamins**
**Atual**: Vitaminas básicas (HP Up, Protein, etc.)
**Adições Potenciais**:
- Ability Capsule
- Ability Patch
- Bottle Caps
- Nature mints

### **🏪 Shop 13 (5F South) - Battle Items**
**Atual**: X items e melhoradores de batalha
**Adições Potenciais**:
- Dynamax Candy
- Itens de Z-Move
- Itens de Mega Evolution
- Itens de batalha avançados

### **🏪 Shop 14 (5F North) - Evolution Items**
**Atual**: Pedras evolutivas básicas
**Adições Potenciais**:
- Shiny Stone
- Dusk Stone
- Dawn Stone
- Ice Stone
- Link Cable
- Itens de evolução especiais (Protector, Electirizer, etc.)

---

## 🛠️ **IMPLEMENTAÇÃO RECOMENDADA**

### **Metodologia**
- ✅ **Usar bytereplacement**: Mesma metodologia do shop de Viridian
- ✅ **Respeitar especialização**: Manter o tema original de cada shop
- ✅ **Adicionar itens complementares**: Que melhoram a categoria do shop
- ✅ **Baseado no .example**: Usar offsets corretos e comprovados

### **Prioridades de Implementação**
1. **Shop 14 (Evolution)**: Adicionar pedras evolutivas novas
2. **Shop 12 (Vitamins)**: Adicionar Ability Capsule/Patch
3. **Shop 11 (TMs)**: Adicionar TMs não obtíveis
4. **Shop 13 (Battle)**: Adicionar itens de batalha modernos
5. **Shop 10 (General)**: Adicionar itens de cura avançados

### **Exemplo de Implementação**
```python
# Shop 14 - Evolution Items
CELADON_EVOLUTION_ITEMS = [
    # Pedras originais
    93,    # ITEM_SUN_STONE
    94,    # ITEM_MOON_STONE
    95,    # ITEM_FIRE_STONE
    96,    # ITEM_THUNDER_STONE
    97,    # ITEM_WATER_STONE
    98,    # ITEM_LEAF_STONE
    # Pedras novas
    99,    # ITEM_SHINY_STONE
    100,   # ITEM_DUSK_STONE
    101,   # ITEM_DAWN_STONE
    102,   # ITEM_ICE_STONE
    # Itens de evolução
    87,    # ITEM_LINK_CABLE
    88,    # ITEM_PROTECTOR
    89,    # ITEM_ELECTIRIZER
    90,    # ITEM_MAGMARIZER
    91,    # ITEM_DUBIOUS_DISC
    92,    # ITEM_REAPER_CLOTH
    0x0000 # Terminator
]
```

---

## 🎯 **CONCLUSÃO**

### **✅ Identificação Completa**
- **5 shops especializados** no Celadon Department Store
- **Offsets corretos** extraídos do projeto .example
- **Especialização clara** de cada shop por categoria

### **✅ Potencial de Melhoria**
- **Cada shop pode ser melhorado** mantendo sua especialização
- **Itens do CFRU** se encaixam perfeitamente nas categorias existentes
- **Implementação simples** usando metodologia comprovada

### **✅ Benefícios para o Jogador**
- **Shop 10**: Melhor sobrevivência com itens de cura avançados
- **Shop 11**: Mais opções de moveset com TMs adicionais
- **Shop 12**: Otimização competitiva com itens de habilidade
- **Shop 13**: Vantagem tática com itens de batalha modernos
- **Shop 14**: Evolução completa com todas as pedras e itens

**O Celadon Department Store pode se tornar o verdadeiro centro comercial de Kanto, oferecendo todos os itens essenciais para treinadores avançados!** 🏆
