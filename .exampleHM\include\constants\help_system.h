#ifndef GUARD_CONSTANTS_HELP_SYSTEM_H
#define GUARD_CONSTANTS_HELP_SYSTEM_H

#define HELPCONTEXT_NONE                   0
#define HELPCONTEXT_TITLE_SCREEN           1
#define HELPCONTEXT_NEW_GAME               2
#define HELPCONTEXT_NAMING_SCREEN          3
#define HELPCONTEXT_POKEDEX                4
#define HELPCONTEXT_PARTY_MENU             5
#define HELPCONTEXT_POKEMON_INFO           6
#define HELPCONTEXT_POKEMON_SKILLS         7
#define HELPCONTEXT_POKEMON_MOVES          8
#define HELPCONTEXT_BAG                    9
#define HELPCONTEXT_TRAINER_CARD_FRONT     10
#define HELPCONTEXT_TRAINER_CARD_BACK      11
#define HELPCONTEXT_SAVE                   12
#define HELPCONTEXT_OPTIONS                13
#define HELPCONTEXT_PLAYERS_HOUSE          14
#define HELPCONTEXT_OAKS_LAB               15
#define HELPCONTEXT_POKECENTER             16
#define HEL<PERSON>ONTEXT_MART                   17
#define HEL<PERSON>ON<PERSON>XT_GYM                    18
#define HEL<PERSON>ONTEXT_INDOORS                19
#define HEL<PERSON>ONTEXT_OVERWORLD              20
#define HELPCONTEXT_DUNGEON                21 // Caves and Forests
#define HELPCONTEXT_SURFING                22
#define HELPCONTEXT_WILD_BATTLE            23
#define HELPCONTEXT_TRAINER_BATTLE_SINGLE  24
#define HELPCONTEXT_TRAINER_BATTLE_DOUBLE  25
#define HELPCONTEXT_SAFARI_BATTLE          26
#define HELPCONTEXT_PC                     27
#define HELPCONTEXT_BILLS_PC               28
#define HELPCONTEXT_PLAYERS_PC_ITEMS       29
#define HELPCONTEXT_PLAYERS_PC_MAILBOX     30
#define HELPCONTEXT_PC_MISC                31 // Prof Oaks PC and the HoF PC
#define HELPCONTEXT_BEDROOM_PC             32
#define HELPCONTEXT_BEDROOM_PC_ITEMS       33
#define HELPCONTEXT_BEDROOM_PC_MAILBOX     34
#define HELPCONTEXT_UNUSED                 35
#define HELPCONTEXT_COUNT                  36

#endif //GUARD_CONSTANTS_HELP_SYSTEM_H
