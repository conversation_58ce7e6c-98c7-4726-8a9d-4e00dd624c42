# CFRU Item Price Update Report
Generated: 2025-05-29 22:26:25

## Summary
- Total items processed: 186
- Items updated: 93
- Items failed: 0
- Average price change: -1045.45
- Maximum increase: 9000
- Maximum decrease: -59980

## Updated Items

| Item Name | Old Price | New Price | Change | Source | Game Version |
|-----------|-----------|-----------|--------|--------|--------------|
| ITEM_MASTER_BALL | 0 | 1 | +1 | Bulbapedia | bulbapedia |
| ITEM_SAFARI_BALL | 0 | 1 | +1 | Bulbapedia | bulbapedia |
| ITEM_DYNAMAX_CANDY | 10000 | 1 | -9999 | Bulbapedia | bulbapedia |
| ITEM_GUARD_SPEC | 700 | 1500 | +800 | PokeAPI | general |
| ITEM_DIRE_HIT | 650 | 1000 | +350 | PokeAPI | general |
| ITEM_X_ATTACK | 800 | 1000 | +200 | PokeAPI (cached) | general |
| ITEM_X_DEFEND | 550 | 2000 | +1450 | PokeAPI (cached) | general |
| ITEM_X_SPEED | 350 | 1000 | +650 | PokeAPI (cached) | general |
| ITEM_X_ACCURACY | 950 | 1000 | +50 | PokeAPI | general |
| ITEM_X_SPECIAL | 350 | 1000 | +650 | PokeAPI | general |
| ITEM_POKE_DOLL | 1000 | 300 | -700 | PokeAPI | general |
| ITEM_FLUFFY_TAIL | 1000 | 100 | -900 | PokeAPI | general |
| ITEM_ESCAPE_ROPE | 1000 | 300 | -700 | PokeAPI | general |
| ITEM_LINK_CABLE | 3000 | 1000 | -2000 | Fallback | default |
| ITEM_CHERI_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_CHESTO_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_PECHA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_RAWST_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_ASPEAR_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_LEPPA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_ORAN_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_PERSIM_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_LUM_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_SITRUS_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_FIGY_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_WIKI_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_MAGO_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_AGUAV_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_IAPAPA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_RAZZ_BERRY | 20 | 200 | +180 | PokeAPI | general |
| ITEM_NANAB_BERRY | 20 | 200 | +180 | PokeAPI | general |
| ITEM_PINAP_BERRY | 20 | 200 | +180 | PokeAPI | general |
| ITEM_POMEG_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_KELPSY_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_QUALOT_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_HONDEW_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_GREPA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_TAMATO_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_LIECHI_BERRY | 500 | 80 | -420 | PokeAPI | general |
| ITEM_GANLON_BERRY | 500 | 80 | -420 | PokeAPI | general |
| ITEM_SALAC_BERRY | 500 | 80 | -420 | PokeAPI | general |
| ITEM_PETAYA_BERRY | 500 | 80 | -420 | PokeAPI | general |
| ITEM_APICOT_BERRY | 500 | 80 | -420 | PokeAPI | general |
| ITEM_LANSAT_BERRY | 500 | 80 | -420 | PokeAPI | general |
| ITEM_STARF_BERRY | 500 | 80 | -420 | PokeAPI | general |
| ITEM_ENIGMA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_PRISM_SCALE | 500 | 2000 | +1500 | PokeAPI | general |
| ITEM_LAX_INCENSE | 2000 | 5000 | +3000 | PokeAPI | general |
| ITEM_OCCA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_PASSHO_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_WACAN_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_RINDO_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_YACHE_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_CHOPLE_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_KEBIA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_SHUCA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_COBA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_PAYAPA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_TANGA_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_CHARTI_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_KASIB_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_HABAN_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_COLBUR_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_BABIRI_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_CHILAN_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_ROSELI_BERRY | 20 | 80 | +60 | PokeAPI | general |
| ITEM_LUCK_INCENSE | 2000 | 11000 | +9000 | PokeAPI | general |
| ITEM_FULL_INCENSE | 2000 | 5000 | +3000 | PokeAPI | general |
| ITEM_PURE_INCENSE | 2000 | 6000 | +4000 | PokeAPI | general |
| ITEM_NORMAL_GEM | 1000 | 4000 | +3000 | PokeAPI | general |
| ITEM_FIGHTING_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_FLYING_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_POISON_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_GROUND_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_ROCK_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_BUG_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_GHOST_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_STEEL_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_FIRE_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_WATER_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_GRASS_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_ELECTRIC_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_PSYCHIC_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_ICE_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_DRAGON_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_DARK_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_FAIRY_GEM | 1000 | 200 | -800 | PokeAPI | general |
| ITEM_FOCUS_SASH | 2000 | 4000 | +2000 | PokeAPI | general |
| ITEM_BLACK_SLUDGE | 2000 | 4000 | +2000 | PokeAPI | general |
| ITEM_ROCKY_HELMET | 2000 | 4000 | +2000 | PokeAPI | general |
| ITEM_ASSAULT_VEST | 4000 | 1000 | -3000 | PokeAPI | general |
| ITEM_ABILITY_CAPSULE | 50000 | 10000 | -40000 | PokeAPI | general |
| ITEM_ABILITY_PATCH | 60000 | 20 | -59980 | PokeAPI | general |

## Failed Items

Items that could not be updated:

- ITEM_X_SP_DEF: No price data found
- ITEM_TM01: No price data found
- ITEM_TM02: No price data found
- ITEM_TM03: No price data found
- ITEM_TM04: No price data found
- ITEM_TM05: No price data found
- ITEM_TM06: No price data found
- ITEM_TM07: No price data found
- ITEM_TM08: No price data found
- ITEM_TM09: No price data found
- ITEM_TM10: No price data found