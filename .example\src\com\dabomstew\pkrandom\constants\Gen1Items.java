package com.dabomstew.pkrandom.constants;

/*----------------------------------------------------------------------------*/
/*--  Gen1Items.java - defines an index number constant for every item in   --*/
/*--                   the Generation 1 games.                              --*/
/*--                                                                        --*/
/*--  Part of "Universal Pokemon Randomizer ZX" by the UPR-ZX team          --*/
/*--  Pokemon and any associated names and the like are                     --*/
/*--  trademark and (C) Nintendo 1996-2020.                                 --*/
/*--                                                                        --*/
/*--  The custom code written here is licensed under the terms of the GPL:  --*/
/*--                                                                        --*/
/*--  This program is free software: you can redistribute it and/or modify  --*/
/*--  it under the terms of the GNU General Public License as published by  --*/
/*--  the Free Software Foundation, either version 3 of the License, or     --*/
/*--  (at your option) any later version.                                   --*/
/*--                                                                        --*/
/*--  This program is distributed in the hope that it will be useful,       --*/
/*--  but WITHOUT ANY WARRANTY; without even the implied warranty of        --*/
/*--  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the          --*/
/*--  GNU General Public License for more details.                          --*/
/*--                                                                        --*/
/*--  You should have received a copy of the GNU General Public License     --*/
/*--  along with this program. If not, see <http://www.gnu.org/licenses/>.  --*/
/*----------------------------------------------------------------------------*/

public class Gen1Items {
    // https://bulbapedia.bulbagarden.net/wiki/List_of_items_by_index_number_(Generation_I)
    public static final int nothing = 0;
    public static final int masterBall = 1;
    public static final int ultraBall = 2;
    public static final int greatBall = 3;
    public static final int pokeBall = 4;
    public static final int townMap = 5;
    public static final int bicycle = 6;
    public static final int questionMark7 = 7;
    public static final int safariBall = 8;
    public static final int pokedex = 9;
    public static final int moonStone = 10;
    public static final int antidote = 11;
    public static final int burnHeal = 12;
    public static final int iceHeal = 13;
    public static final int awakening = 14;
    public static final int parlyzHeal = 15;
    public static final int fullRestore = 16;
    public static final int maxPotion = 17;
    public static final int hyperPotion = 18;
    public static final int superPotion = 19;
    public static final int potion = 20;
    public static final int boulderBadge = 21;
    public static final int cascadeBadge = 22;
    public static final int thunderBadge = 23;
    public static final int rainbowBadge = 24;
    public static final int soulBadge = 25;
    public static final int marshBadge = 26;
    public static final int volcanoBadge = 27;
    public static final int earthBadge = 28;
    public static final int escapeRope = 29;
    public static final int repel = 30;
    public static final int oldAmber = 31;
    public static final int fireStone = 32;
    public static final int thunderstone = 33;
    public static final int waterStone = 34;
    public static final int hpUp = 35;
    public static final int protein = 36;
    public static final int iron = 37;
    public static final int carbos = 38;
    public static final int calcium = 39;
    public static final int rareCandy = 40;
    public static final int domeFossil = 41;
    public static final int helixFossil = 42;
    public static final int secretKey = 43;
    public static final int questionMark44 = 44;
    public static final int bikeVoucher = 45;
    public static final int xAccuracy = 46;
    public static final int leafStone = 47;
    public static final int cardKey = 48;
    public static final int nugget = 49;
    public static final int ppUpGlitch = 50;
    public static final int pokeDoll = 51;
    public static final int fullHeal = 52;
    public static final int revive = 53;
    public static final int maxRevive = 54;
    public static final int guardSpec = 55;
    public static final int superRepel = 56;
    public static final int maxRepel = 57;
    public static final int direHit = 58;
    public static final int coin = 59;
    public static final int freshWater = 60;
    public static final int sodaPop = 61;
    public static final int lemonade = 62;
    public static final int ssTicket = 63;
    public static final int goldTeeth = 64;
    public static final int xAttack = 65;
    public static final int xDefend = 66;
    public static final int xSpeed = 67;
    public static final int xSpecial = 68;
    public static final int coinCase = 69;
    public static final int oaksParcel = 70;
    public static final int itemfinder = 71;
    public static final int silphScope = 72;
    public static final int pokeFlute = 73;
    public static final int liftKey = 74;
    public static final int expAll = 75;
    public static final int oldRod = 76;
    public static final int goodRod = 77;
    public static final int superRod = 78;
    public static final int ppUp = 79;
    public static final int ether = 80;
    public static final int maxEther = 81;
    public static final int elixer = 82;
    public static final int maxElixer = 83;
    public static final int unused84 = 84;
    public static final int unused85 = 85;
    public static final int unused86 = 86;
    public static final int unused87 = 87;
    public static final int unused88 = 88;
    public static final int unused89 = 89;
    public static final int unused90 = 90;
    public static final int unused91 = 91;
    public static final int unused92 = 92;
    public static final int unused93 = 93;
    public static final int unused94 = 94;
    public static final int unused95 = 95;
    public static final int unused96 = 96;
    public static final int unused97 = 97;
    public static final int unused98 = 98;
    public static final int unused99 = 99;
    public static final int unused100 = 100;
    public static final int unused101 = 101;
    public static final int unused102 = 102;
    public static final int unused103 = 103;
    public static final int unused104 = 104;
    public static final int unused105 = 105;
    public static final int unused106 = 106;
    public static final int unused107 = 107;
    public static final int unused108 = 108;
    public static final int unused109 = 109;
    public static final int unused110 = 110;
    public static final int unused111 = 111;
    public static final int unused112 = 112;
    public static final int unused113 = 113;
    public static final int unused114 = 114;
    public static final int unused115 = 115;
    public static final int unused116 = 116;
    public static final int unused117 = 117;
    public static final int unused118 = 118;
    public static final int unused119 = 119;
    public static final int unused120 = 120;
    public static final int unused121 = 121;
    public static final int unused122 = 122;
    public static final int unused123 = 123;
    public static final int unused124 = 124;
    public static final int unused125 = 125;
    public static final int unused126 = 126;
    public static final int unused127 = 127;
    public static final int unused128 = 128;
    public static final int unused129 = 129;
    public static final int unused130 = 130;
    public static final int unused131 = 131;
    public static final int unused132 = 132;
    public static final int unused133 = 133;
    public static final int unused134 = 134;
    public static final int unused135 = 135;
    public static final int unused136 = 136;
    public static final int unused137 = 137;
    public static final int unused138 = 138;
    public static final int unused139 = 139;
    public static final int unused140 = 140;
    public static final int unused141 = 141;
    public static final int unused142 = 142;
    public static final int unused143 = 143;
    public static final int unused144 = 144;
    public static final int unused145 = 145;
    public static final int unused146 = 146;
    public static final int unused147 = 147;
    public static final int unused148 = 148;
    public static final int unused149 = 149;
    public static final int unused150 = 150;
    public static final int unused151 = 151;
    public static final int unused152 = 152;
    public static final int unused153 = 153;
    public static final int unused154 = 154;
    public static final int unused155 = 155;
    public static final int unused156 = 156;
    public static final int unused157 = 157;
    public static final int unused158 = 158;
    public static final int unused159 = 159;
    public static final int unused160 = 160;
    public static final int unused161 = 161;
    public static final int unused162 = 162;
    public static final int unused163 = 163;
    public static final int unused164 = 164;
    public static final int unused165 = 165;
    public static final int unused166 = 166;
    public static final int unused167 = 167;
    public static final int unused168 = 168;
    public static final int unused169 = 169;
    public static final int unused170 = 170;
    public static final int unused171 = 171;
    public static final int unused172 = 172;
    public static final int unused173 = 173;
    public static final int unused174 = 174;
    public static final int unused175 = 175;
    public static final int unused176 = 176;
    public static final int unused177 = 177;
    public static final int unused178 = 178;
    public static final int unused179 = 179;
    public static final int unused180 = 180;
    public static final int unused181 = 181;
    public static final int unused182 = 182;
    public static final int unused183 = 183;
    public static final int unused184 = 184;
    public static final int unused185 = 185;
    public static final int unused186 = 186;
    public static final int unused187 = 187;
    public static final int unused188 = 188;
    public static final int unused189 = 189;
    public static final int unused190 = 190;
    public static final int unused191 = 191;
    public static final int unused192 = 192;
    public static final int unused193 = 193;
    public static final int unused194 = 194;
    public static final int unused195 = 195;
    public static final int hm01 = 196;
    public static final int hm02 = 197;
    public static final int hm03 = 198;
    public static final int hm04 = 199;
    public static final int hm05 = 200;
    public static final int tm01 = 201;
    public static final int tm02 = 202;
    public static final int tm03 = 203;
    public static final int tm04 = 204;
    public static final int tm05 = 205;
    public static final int tm06 = 206;
    public static final int tm07 = 207;
    public static final int tm08 = 208;
    public static final int tm09 = 209;
    public static final int tm10 = 210;
    public static final int tm11 = 211;
    public static final int tm12 = 212;
    public static final int tm13 = 213;
    public static final int tm14 = 214;
    public static final int tm15 = 215;
    public static final int tm16 = 216;
    public static final int tm17 = 217;
    public static final int tm18 = 218;
    public static final int tm19 = 219;
    public static final int tm20 = 220;
    public static final int tm21 = 221;
    public static final int tm22 = 222;
    public static final int tm23 = 223;
    public static final int tm24 = 224;
    public static final int tm25 = 225;
    public static final int tm26 = 226;
    public static final int tm27 = 227;
    public static final int tm28 = 228;
    public static final int tm29 = 229;
    public static final int tm30 = 230;
    public static final int tm31 = 231;
    public static final int tm32 = 232;
    public static final int tm33 = 233;
    public static final int tm34 = 234;
    public static final int tm35 = 235;
    public static final int tm36 = 236;
    public static final int tm37 = 237;
    public static final int tm38 = 238;
    public static final int tm39 = 239;
    public static final int tm40 = 240;
    public static final int tm41 = 241;
    public static final int tm42 = 242;
    public static final int tm43 = 243;
    public static final int tm44 = 244;
    public static final int tm45 = 245;
    public static final int tm46 = 246;
    public static final int tm47 = 247;
    public static final int tm48 = 248;
    public static final int tm49 = 249;
    public static final int tm50 = 250;
    public static final int tm51 = 251;
    public static final int tm52 = 252;
    public static final int tm53 = 253;
    public static final int tm54 = 254;
    public static final int tm55 = 255;
}
