#ifndef GUARD_FIELD_FADETRANSITION_H
#define GUARD_FIELD_FADETRANSITION_H

#include "global.h"

void FieldCB_RushInjuredPokemonToCenter(void);
void DoWarp(void);
void DoDiveWarp(void);
void Do<PERSON>oor<PERSON>arp(void);

void DoFallWarp(void);
void DoTeleportWarp(void);
void DoTeleport2Warp(void);
void FieldCB_DefaultWarpExit(void);
void WarpFadeOutScreen(void);
void FieldCB_ContinueScriptHandleMusic(void);
void FadeInFromBlack(void);
void FadeTransition_FadeInOnReturnToStartMenu(void);
void WarpFadeInScreen(void);

void FieldCB_ContinueScriptUnionRoom(void);

bool32 FieldFadeTransitionBackgroundEffectIsFinished(void);
void palette_bg_faded_fill_black(void);
void DoStair<PERSON>arp(u16 metatileBehavior, u16 delay);
void DoEscalatorWarp(u8 a0);
void DoLavaridgeGymB1FWarp(void);
void DoLavaridgeGym1FWarp(void);
void DoTeleportWarp(void);
void DoUnionRoomWarp(void);
void FieldCB_ReturnToFieldWirelessLink(void);
void FieldCB_ReturnToFieldCableLink(void);
bool8 FieldCB_ReturnToFieldOpenStartMenu(void);
void FieldCB_ContinueScript(void);
void FieldCB_ContinueScriptHandleMusic(void);
void FieldCB_WarpExitFadeFromBlack(void);

#endif // GUARD_FIELD_FADETRANSITION_H
