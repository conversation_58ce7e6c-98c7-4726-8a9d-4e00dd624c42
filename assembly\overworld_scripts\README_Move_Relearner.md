# 🎯 **Move Relearner Script - CFRU**

## 📋 **Arquivo Individual**

Este arquivo contém o script completo e corrigido do Move Relearner para CFRU.

### **📁 Localização**
```
assembly/overworld_scripts/Move_Relearner.s
```

---

## ✅ **Características**

### **🔧 Correções Aplicadas**
- ✅ **Função correta**: `gotonative CB2_InitLearnMove` (não `callnative Move_Relearner`)
- ✅ **Sem conflitos**: Não aciona mais o tutorial de captura
- ✅ **Interface nativa**: Abre a interface oficial do Move Relearner
- ✅ **Mensagens fechadas**: `closemessage` antes da transição

### **🎮 Funcionalidades**
- ✅ **Validação de Pokémon**: Verifica se o jogador tem Pokémon
- ✅ **Confirmação Yes/No**: Pergunta antes de usar o serviço
- ✅ **Heart Scale opcional**: <PERSON><PERSON><PERSON> comentado (fácil de ativar)
- ✅ **Mensagens personalizadas**: Textos em arquivo separado

---

## 🚀 **Como Usar**

### **1️⃣ Implementar NPC**

Adicione ao arquivo `eventscripts`:
```
# Move Relearner em Two Island
npc 23 0 1 EventScript_MoveRelearner

# Move Relearner em Saffron Pokemon Center  
npc 5 5 3 EventScript_MoveRelearner

# Move Relearner em Celadon Pokemon Center
npc 6 5 2 EventScript_MoveRelearner
```

### **2️⃣ Compilar**
```bash
python scripts/make.py
```

### **3️⃣ Testar**
- Fale com o NPC
- Confirme que a interface do Move Relearner abre
- Teste com Pokémon que esqueceram movimentos

---

## ⚙️ **Configurações Opcionais**

### **Heart Scale Requirement**

Para exigir Heart Scale, descomente estas linhas:
```assembly
@ checkitem ITEM_HEART_SCALE 1
@ compare LASTRESULT FALSE  
@ if equal _goto EventScript_MoveRelearner_NoHeartScale

@ removeitem ITEM_HEART_SCALE 1
```

### **Ignorar Restrições de Nível**

Ative a flag no jogo:
```
FLAG_MOVE_RELEARNER_IGNORE_LEVEL
```

---

## 📍 **Localizações Sugeridas**

### **Pokémon Centers**
- Saffron City: `npc 5 5 3 EventScript_MoveRelearner`
- Celadon City: `npc 6 5 2 EventScript_MoveRelearner`
- Fuchsia City: `npc 7 5 2 EventScript_MoveRelearner`

### **Sevii Islands**
- Two Island: `npc 23 0 1 EventScript_MoveRelearner`
- Four Island: `npc 25 0 2 EventScript_MoveRelearner`

### **Special Locations**
- Indigo Plateau: `npc 9 0 3 EventScript_MoveRelearner`

---

## 📝 **Arquivos Relacionados**

```
assembly/overworld_scripts/Move_Relearner.s  # Este arquivo (script principal)
strings/Scripts/Move_Relearner.string        # Textos do Move Relearner
eventscripts                                 # Integração com NPCs
docs/MOVE_RELEARNER_GUIDE.md                # Guia completo
docs/MOVE_RELEARNER_TROUBLESHOOTING.md      # Solução de problemas
```

---

## 🎯 **Função Técnica**

### **Função Correta**
```c
CB2_InitLearnMove = 0x80E478C | 1;
```

### **Chamada no Script**
```assembly
closemessage
gotonative CB2_InitLearnMove
```

⚠️ **IMPORTANTE**: Nunca use `callnative Move_Relearner` - causa conflitos!

---

## ✅ **Status**

- ✅ **Testado**: Funciona corretamente
- ✅ **Corrigido**: Sem conflitos com tutorial
- ✅ **Documentado**: Guias completos disponíveis
- ✅ **Individual**: Arquivo separado e organizado

---

## 🚀 **Pronto para Uso!**

Este script está completamente funcional e pronto para ser usado em seu projeto CFRU!
