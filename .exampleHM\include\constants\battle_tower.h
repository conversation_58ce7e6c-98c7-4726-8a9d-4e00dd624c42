#ifndef GUARD_CONSTANTS_BATTLE_TOWER_H
#define GUARD_CONSTANTS_BATTLE_TOWER_H

#define F_EV_SPREAD_HP         1 << 0
#define F_EV_SPREAD_ATTACK     1 << 1
#define F_EV_SPREAD_DEFENSE    1 << 2
#define F_EV_SPREAD_SPEED      1 << 3
#define F_EV_SPREAD_SP_ATTACK  1 << 4
#define F_EV_SPREAD_SP_DEFENSE 1 << 5

#define BATTLE_TOWER_ITEM_NONE           0
#define BATTLE_TOWER_ITEM_KINGS_ROCK     1
#define BATTLE_TOWER_ITEM_SITRUS_BERRY   2
#define BATTLE_TOWER_ITEM_ORAN_BERRY     3
#define BATTLE_TOWER_ITEM_CHESTO_BERRY   4
#define BATTLE_TOWER_ITEM_HARD_STONE     5
#define BATTLE_TOWER_ITEM_FOCUS_BAND     6
#define BATTLE_TOWER_ITEM_PERSIM_BERRY   7
#define BATTLE_TOWER_ITEM_MIRACLE_SEED   8
#define BATTLE_TOWER_ITEM_BERRY_JUICE    9
#define BATTLE_TOWER_ITEM_MACHO_BRACE    10
#define BATTLE_TOWER_ITEM_SILVER_POWDER  11
#define BATTLE_TOWER_ITEM_CHERI_BERRY    12
#define BATTLE_TOWER_ITEM_BLACK_GLASSES  13
#define BATTLE_TOWER_ITEM_BLACK_BELT     14
#define BATTLE_TOWER_ITEM_SOUL_DEW       15
#define BATTLE_TOWER_ITEM_CHOICE_BAND    16
#define BATTLE_TOWER_ITEM_MAGNET         17
#define BATTLE_TOWER_ITEM_SILK_SCARF     18
#define BATTLE_TOWER_ITEM_WHITE_HERB     19
#define BATTLE_TOWER_ITEM_DEEP_SEA_SCALE 20
#define BATTLE_TOWER_ITEM_DEEP_SEA_TOOTH 21
#define BATTLE_TOWER_ITEM_MYSTIC_WATER   22
#define BATTLE_TOWER_ITEM_SHARP_BEAK     23
#define BATTLE_TOWER_ITEM_QUICK_CLAW     24
#define BATTLE_TOWER_ITEM_LEFTOVERS      25
#define BATTLE_TOWER_ITEM_RAWST_BERRY    26
#define BATTLE_TOWER_ITEM_LIGHT_BALL     27
#define BATTLE_TOWER_ITEM_POISON_BARB    28
#define BATTLE_TOWER_ITEM_NEVER_MELT_ICE 29
#define BATTLE_TOWER_ITEM_ASPEAR_BERRY   30
#define BATTLE_TOWER_ITEM_SPELL_TAG      31
#define BATTLE_TOWER_ITEM_BRIGHT_POWDER  32
#define BATTLE_TOWER_ITEM_LEPPA_BERRY    33
#define BATTLE_TOWER_ITEM_SCOPE_LENS     34
#define BATTLE_TOWER_ITEM_TWISTED_SPOON  35
#define BATTLE_TOWER_ITEM_METAL_COAT     36
#define BATTLE_TOWER_ITEM_MENTAL_HERB    37
#define BATTLE_TOWER_ITEM_CHARCOAL       38
#define BATTLE_TOWER_ITEM_PECHA_BERRY    39
#define BATTLE_TOWER_ITEM_SOFT_SAND      40
#define BATTLE_TOWER_ITEM_LUM_BERRY      41
#define BATTLE_TOWER_ITEM_DRAGON_SCALE   42
#define BATTLE_TOWER_ITEM_DRAGON_FANG    43
#define BATTLE_TOWER_ITEM_IAPAPA_BERRY   44
#define BATTLE_TOWER_ITEM_WIKI_BERRY     45
#define BATTLE_TOWER_ITEM_SEA_INCENSE    46
#define BATTLE_TOWER_ITEM_SHELL_BELL     47
#define BATTLE_TOWER_ITEM_SALAC_BERRY    48
#define BATTLE_TOWER_ITEM_LANSAT_BERRY   49
#define BATTLE_TOWER_ITEM_APICOT_BERRY   50
#define BATTLE_TOWER_ITEM_STARF_BERRY    51
#define BATTLE_TOWER_ITEM_LIECHI_BERRY   52
#define BATTLE_TOWER_ITEM_STICK          53
#define BATTLE_TOWER_ITEM_LAX_INCENSE    54
#define BATTLE_TOWER_ITEM_AGUAV_BERRY    55
#define BATTLE_TOWER_ITEM_FIGY_BERRY     56
#define BATTLE_TOWER_ITEM_THICK_CLUB     57
#define BATTLE_TOWER_ITEM_MAGO_BERRY     58
#define BATTLE_TOWER_ITEM_METAL_POWDER   59
#define BATTLE_TOWER_ITEM_PETAYA_BERRY   60
#define BATTLE_TOWER_ITEM_LUCKY_PUNCH    61
#define BATTLE_TOWER_ITEM_GANLON_BERRY   62

#define BTSPECIAL_TEST             0
#define BTSPECIAL_RESULT_SAVE_SCUM 1
#define BTSPECIAL_RESULT_WON7      2
#define BTSPECIAL_RESULT_LOST      3
#define BTSPECIAL_RESULT_QUICKSAVE 4
#define BTSPECIAL_RESULT_INACTIVE  5

#endif //GUARD_CONSTANTS_BATTLE_TOWER_H
