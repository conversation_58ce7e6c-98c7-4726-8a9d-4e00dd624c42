package com.dabomstew.pkrandom.constants;

/*----------------------------------------------------------------------------*/
/*--  Items.java - defines an index number constant for every item in the   --*/
/*--               game from Diamond/Pearl to Sword/Shield.                 --*/
/*--                                                                        --*/
/*--  Part of "Universal Pokemon Randomizer ZX" by the UPR-ZX team          --*/
/*--  Pokemon and any associated names and the like are                     --*/
/*--  trademark and (C) Nintendo 1996-2020.                                 --*/
/*--                                                                        --*/
/*--  The custom code written here is licensed under the terms of the GPL:  --*/
/*--                                                                        --*/
/*--  This program is free software: you can redistribute it and/or modify  --*/
/*--  it under the terms of the GNU General Public License as published by  --*/
/*--  the Free Software Foundation, either version 3 of the License, or     --*/
/*--  (at your option) any later version.                                   --*/
/*--                                                                        --*/
/*--  This program is distributed in the hope that it will be useful,       --*/
/*--  but WITHOUT ANY WARRANTY; without even the implied warranty of        --*/
/*--  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the          --*/
/*--  GNU General Public License for more details.                          --*/
/*--                                                                        --*/
/*--  You should have received a copy of the GNU General Public License     --*/
/*--  along with this program. If not, see <http://www.gnu.org/licenses/>.  --*/
/*----------------------------------------------------------------------------*/

public class Items {
    // https://bulbapedia.bulbagarden.net/wiki/List_of_items_by_index_number_(Generation_IV)
    // https://bulbapedia.bulbagarden.net/wiki/List_of_items_by_index_number_(Generation_V)
    // https://bulbapedia.bulbagarden.net/wiki/List_of_items_by_index_number_(Generation_VI)
    // https://bulbapedia.bulbagarden.net/wiki/List_of_items_by_index_number_(Generation_VII)
    // https://bulbapedia.bulbagarden.net/wiki/List_of_items_by_index_number_(Let%27s_Go,_Pikachu!_and_Let%27s_Go,_Eevee!)
    // https://bulbapedia.bulbagarden.net/wiki/List_of_items_by_index_number_(Generation_VIII)
    // Unlike in previous generations, GameFreak was *mostly* consistent with this item list starting with Diamond and
    // Pearl. Places where they were not consistent are specifically called out in comments.

    // These items are generally available in Diamond/Pearl and onwards.
    public static final int none = 0;
    public static final int masterBall = 1;
    public static final int ultraBall = 2;
    public static final int greatBall = 3;
    public static final int pokeBall = 4;
    public static final int safariBall = 5;
    public static final int netBall = 6;
    public static final int diveBall = 7;
    public static final int nestBall = 8;
    public static final int repeatBall = 9;
    public static final int timerBall = 10;
    public static final int luxuryBall = 11;
    public static final int premierBall = 12;
    public static final int duskBall = 13;
    public static final int healBall = 14;
    public static final int quickBall = 15;
    public static final int cherishBall = 16;
    public static final int potion = 17;
    public static final int antidote = 18;
    public static final int burnHeal = 19;
    public static final int iceHeal = 20;
    public static final int awakening = 21;
    public static final int paralyzeHeal = 22;
    public static final int fullRestore = 23;
    public static final int maxPotion = 24;
    public static final int hyperPotion = 25;
    public static final int superPotion = 26;
    public static final int fullHeal = 27;
    public static final int revive = 28;
    public static final int maxRevive = 29;
    public static final int freshWater = 30;
    public static final int sodaPop = 31;
    public static final int lemonade = 32;
    public static final int moomooMilk = 33;
    public static final int energyPowder = 34;
    public static final int energyRoot = 35;
    public static final int healPowder = 36;
    public static final int revivalHerb = 37;
    public static final int ether = 38;
    public static final int maxEther = 39;
    public static final int elixir = 40;
    public static final int maxElixir = 41;
    public static final int lavaCookie = 42;
    public static final int berryJuice = 43;
    public static final int sacredAsh = 44;
    public static final int hpUp = 45;
    public static final int protein = 46;
    public static final int iron = 47;
    public static final int carbos = 48;
    public static final int calcium = 49;
    public static final int rareCandy = 50;
    public static final int ppUp = 51;
    public static final int zinc = 52;
    public static final int ppMax = 53;
    public static final int oldGateau = 54;
    public static final int guardSpec = 55;
    public static final int direHit = 56;
    public static final int xAttack = 57;
    public static final int xDefense = 58;
    public static final int xSpeed = 59;
    public static final int xAccuracy = 60;
    public static final int xSpAtk = 61;
    public static final int xSpDef = 62;
    public static final int pokeDoll = 63;
    public static final int fluffyTail = 64;
    public static final int blueFlute = 65;
    public static final int yellowFlute = 66;
    public static final int redFlute = 67;
    public static final int blackFlute = 68;
    public static final int whiteFlute = 69;
    public static final int shoalSalt = 70;
    public static final int shoalShell = 71;
    public static final int redShard = 72;
    public static final int blueShard = 73;
    public static final int yellowShard = 74;
    public static final int greenShard = 75;
    public static final int superRepel = 76;
    public static final int maxRepel = 77;
    public static final int escapeRope = 78;
    public static final int repel = 79;
    public static final int sunStone = 80;
    public static final int moonStone = 81;
    public static final int fireStone = 82;
    public static final int thunderStone = 83;
    public static final int waterStone = 84;
    public static final int leafStone = 85;
    public static final int tinyMushroom = 86;
    public static final int bigMushroom = 87;
    public static final int pearl = 88;
    public static final int bigPearl = 89;
    public static final int stardust = 90;
    public static final int starPiece = 91;
    public static final int nugget = 92;
    public static final int heartScale = 93;
    public static final int honey = 94;
    public static final int growthMulch = 95;
    public static final int dampMulch = 96;
    public static final int stableMulch = 97;
    public static final int gooeyMulch = 98;
    public static final int rootFossil = 99;
    public static final int clawFossil = 100;
    public static final int helixFossil = 101;
    public static final int domeFossil = 102;
    public static final int oldAmber = 103;
    public static final int armorFossil = 104;
    public static final int skullFossil = 105;
    public static final int rareBone = 106;
    public static final int shinyStone = 107;
    public static final int duskStone = 108;
    public static final int dawnStone = 109;
    public static final int ovalStone = 110;
    public static final int oddKeystone = 111;
    public static final int griseousOrb = 112; // unused until Platinum
    public static final int tea = 113; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int unused114 = 114;
    public static final int autograph = 115; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int douseDrive = 116; // unused until Black and White
    public static final int shockDrive = 117; // unused until Black and White
    public static final int burnDrive = 118; // unused until Black and White
    public static final int chillDrive = 119; // unused until Black and White
    public static final int unused120 = 120;
    public static final int pokemonBox = 121; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int medicinePocket = 122; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int tmCase = 123; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int candyJar = 124; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int powerUpPocket = 125; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int clothingTrunk = 126; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int catchingPocket = 127; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int battlePocket = 128; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int unused129 = 129;
    public static final int unused130 = 130;
    public static final int unused131 = 131;
    public static final int unused132 = 132;
    public static final int unused133 = 133;
    public static final int sweetHeart = 134; // unused until Black and White
    public static final int adamantOrb = 135;
    public static final int lustrousOrb = 136;
    public static final int mail1 = 137; // Grass Mail in Gen 4, Greet Mail in Gen 5+
    public static final int mail2 = 138; // Flame Mail in Gen 4, Favored Mail in Gen 5+
    public static final int mail3 = 139; // Bubble Mail in Gen 4, RSVP Mail in Gen 5+
    public static final int mail4 = 140; // Bloom Mail in Gen 4, Thanks Mail in Gen 5+
    public static final int mail5 = 141; // Tunnel Mail in Gen 4, Inquiry Mail in Gen 5+
    public static final int mail6 = 142; // Steel Mail in Gen 4, Like Mail in Gen 5+
    public static final int mail7 = 143; // Heart Mail in Gen 4, Reply Mail in Gen 5+
    public static final int mail8 = 144; // Snow Mail in Gen 4, Bridge Mail S in Gen 5+
    public static final int mail9 = 145; // Space Mail in Gen 4, Bridge Mail D in Gen 5+
    public static final int mail10 = 146; // Air Mail in Gen 4, Bridge Mail T in Gen 5+
    public static final int mail11 = 147; // Mosaic Mail in Gen 4, Bridge Mail V in Gen 5+
    public static final int mail12 = 148; // Brick Mail in Gen 4, Bridge Mail W in Gen 5+
    public static final int cheriBerry = 149;
    public static final int chestoBerry = 150;
    public static final int pechaBerry = 151;
    public static final int rawstBerry = 152;
    public static final int aspearBerry = 153;
    public static final int leppaBerry = 154;
    public static final int oranBerry = 155;
    public static final int persimBerry = 156;
    public static final int lumBerry = 157;
    public static final int sitrusBerry = 158;
    public static final int figyBerry = 159;
    public static final int wikiBerry = 160;
    public static final int magoBerry = 161;
    public static final int aguavBerry = 162;
    public static final int iapapaBerry = 163;
    public static final int razzBerry = 164;
    public static final int blukBerry = 165;
    public static final int nanabBerry = 166;
    public static final int wepearBerry = 167;
    public static final int pinapBerry = 168;
    public static final int pomegBerry = 169;
    public static final int kelpsyBerry = 170;
    public static final int qualotBerry = 171;
    public static final int hondewBerry = 172;
    public static final int grepaBerry = 173;
    public static final int tamatoBerry = 174;
    public static final int cornnBerry = 175;
    public static final int magostBerry = 176;
    public static final int rabutaBerry = 177;
    public static final int nomelBerry = 178;
    public static final int spelonBerry = 179;
    public static final int pamtreBerry = 180;
    public static final int watmelBerry = 181;
    public static final int durinBerry = 182;
    public static final int belueBerry = 183;
    public static final int occaBerry = 184;
    public static final int passhoBerry = 185;
    public static final int wacanBerry = 186;
    public static final int rindoBerry = 187;
    public static final int yacheBerry = 188;
    public static final int chopleBerry = 189;
    public static final int kebiaBerry = 190;
    public static final int shucaBerry = 191;
    public static final int cobaBerry = 192;
    public static final int payapaBerry = 193;
    public static final int tangaBerry = 194;
    public static final int chartiBerry = 195;
    public static final int kasibBerry = 196;
    public static final int habanBerry = 197;
    public static final int colburBerry = 198;
    public static final int babiriBerry = 199;
    public static final int chilanBerry = 200;
    public static final int liechiBerry = 201;
    public static final int ganlonBerry = 202;
    public static final int salacBerry = 203;
    public static final int petayaBerry = 204;
    public static final int apicotBerry = 205;
    public static final int lansatBerry = 206;
    public static final int starfBerry = 207;
    public static final int enigmaBerry = 208;
    public static final int micleBerry = 209;
    public static final int custapBerry = 210;
    public static final int jabocaBerry = 211;
    public static final int rowapBerry = 212;
    public static final int brightPowder = 213;
    public static final int whiteHerb = 214;
    public static final int machoBrace = 215;
    public static final int expShare = 216; // Changed into a key item in X/Y
    public static final int quickClaw = 217;
    public static final int sootheBell = 218;
    public static final int mentalHerb = 219;
    public static final int choiceBand = 220;
    public static final int kingsRock = 221;
    public static final int silverPowder = 222;
    public static final int amuletCoin = 223;
    public static final int cleanseTag = 224;
    public static final int soulDew = 225;
    public static final int deepSeaTooth = 226;
    public static final int deepSeaScale = 227;
    public static final int smokeBall = 228;
    public static final int everstone = 229;
    public static final int focusBand = 230;
    public static final int luckyEgg = 231;
    public static final int scopeLens = 232;
    public static final int metalCoat = 233;
    public static final int leftovers = 234;
    public static final int dragonScale = 235;
    public static final int lightBall = 236;
    public static final int softSand = 237;
    public static final int hardStone = 238;
    public static final int miracleSeed = 239;
    public static final int blackGlasses = 240;
    public static final int blackBelt = 241;
    public static final int magnet = 242;
    public static final int mysticWater = 243;
    public static final int sharpBeak = 244;
    public static final int poisonBarb = 245;
    public static final int neverMeltIce = 246;
    public static final int spellTag = 247;
    public static final int twistedSpoon = 248;
    public static final int charcoal = 249;
    public static final int dragonFang = 250;
    public static final int silkScarf = 251;
    public static final int upgrade = 252;
    public static final int shellBell = 253;
    public static final int seaIncense = 254;
    public static final int laxIncense = 255;
    public static final int luckyPunch = 256;
    public static final int metalPowder = 257;
    public static final int thickClub = 258;
    public static final int leek = 259; // called "Stick" prior to SwSh
    public static final int redScarf = 260;
    public static final int blueScarf = 261;
    public static final int pinkScarf = 262;
    public static final int greenScarf = 263;
    public static final int yellowScarf = 264;
    public static final int wideLens = 265;
    public static final int muscleBand = 266;
    public static final int wiseGlasses = 267;
    public static final int expertBelt = 268;
    public static final int lightClay = 269;
    public static final int lifeOrb = 270;
    public static final int powerHerb = 271;
    public static final int toxicOrb = 272;
    public static final int flameOrb = 273;
    public static final int quickPowder = 274;
    public static final int focusSash = 275;
    public static final int zoomLens = 276;
    public static final int metronome = 277;
    public static final int ironBall = 278;
    public static final int laggingTail = 279;
    public static final int destinyKnot = 280;
    public static final int blackSludge = 281;
    public static final int icyRock = 282;
    public static final int smoothRock = 283;
    public static final int heatRock = 284;
    public static final int dampRock = 285;
    public static final int gripClaw = 286;
    public static final int choiceScarf = 287;
    public static final int stickyBarb = 288;
    public static final int powerBracer = 289;
    public static final int powerBelt = 290;
    public static final int powerLens = 291;
    public static final int powerBand = 292;
    public static final int powerAnklet = 293;
    public static final int powerWeight = 294;
    public static final int shedShell = 295;
    public static final int bigRoot = 296;
    public static final int choiceSpecs = 297;
    public static final int flamePlate = 298;
    public static final int splashPlate = 299;
    public static final int zapPlate = 300;
    public static final int meadowPlate = 301;
    public static final int iciclePlate = 302;
    public static final int fistPlate = 303;
    public static final int toxicPlate = 304;
    public static final int earthPlate = 305;
    public static final int skyPlate = 306;
    public static final int mindPlate = 307;
    public static final int insectPlate = 308;
    public static final int stonePlate = 309;
    public static final int spookyPlate = 310;
    public static final int dracoPlate = 311;
    public static final int dreadPlate = 312;
    public static final int ironPlate = 313;
    public static final int oddIncense = 314;
    public static final int rockIncense = 315;
    public static final int fullIncense = 316;
    public static final int waveIncense = 317;
    public static final int roseIncense = 318;
    public static final int luckIncense = 319;
    public static final int pureIncense = 320;
    public static final int protector = 321;
    public static final int electirizer = 322;
    public static final int magmarizer = 323;
    public static final int dubiousDisc = 324;
    public static final int reaperCloth = 325;
    public static final int razorClaw = 326;
    public static final int razorFang = 327;
    public static final int tm01 = 328;
    public static final int tm02 = 329;
    public static final int tm03 = 330;
    public static final int tm04 = 331;
    public static final int tm05 = 332;
    public static final int tm06 = 333;
    public static final int tm07 = 334;
    public static final int tm08 = 335;
    public static final int tm09 = 336;
    public static final int tm10 = 337;
    public static final int tm11 = 338;
    public static final int tm12 = 339;
    public static final int tm13 = 340;
    public static final int tm14 = 341;
    public static final int tm15 = 342;
    public static final int tm16 = 343;
    public static final int tm17 = 344;
    public static final int tm18 = 345;
    public static final int tm19 = 346;
    public static final int tm20 = 347;
    public static final int tm21 = 348;
    public static final int tm22 = 349;
    public static final int tm23 = 350;
    public static final int tm24 = 351;
    public static final int tm25 = 352;
    public static final int tm26 = 353;
    public static final int tm27 = 354;
    public static final int tm28 = 355;
    public static final int tm29 = 356;
    public static final int tm30 = 357;
    public static final int tm31 = 358;
    public static final int tm32 = 359;
    public static final int tm33 = 360;
    public static final int tm34 = 361;
    public static final int tm35 = 362;
    public static final int tm36 = 363;
    public static final int tm37 = 364;
    public static final int tm38 = 365;
    public static final int tm39 = 366;
    public static final int tm40 = 367;
    public static final int tm41 = 368;
    public static final int tm42 = 369;
    public static final int tm43 = 370;
    public static final int tm44 = 371;
    public static final int tm45 = 372;
    public static final int tm46 = 373;
    public static final int tm47 = 374;
    public static final int tm48 = 375;
    public static final int tm49 = 376;
    public static final int tm50 = 377;
    public static final int tm51 = 378;
    public static final int tm52 = 379;
    public static final int tm53 = 380;
    public static final int tm54 = 381;
    public static final int tm55 = 382;
    public static final int tm56 = 383;
    public static final int tm57 = 384;
    public static final int tm58 = 385;
    public static final int tm59 = 386;
    public static final int tm60 = 387;
    public static final int tm61 = 388;
    public static final int tm62 = 389;
    public static final int tm63 = 390;
    public static final int tm64 = 391;
    public static final int tm65 = 392;
    public static final int tm66 = 393;
    public static final int tm67 = 394;
    public static final int tm68 = 395;
    public static final int tm69 = 396;
    public static final int tm70 = 397;
    public static final int tm71 = 398;
    public static final int tm72 = 399;
    public static final int tm73 = 400;
    public static final int tm74 = 401;
    public static final int tm75 = 402;
    public static final int tm76 = 403;
    public static final int tm77 = 404;
    public static final int tm78 = 405;
    public static final int tm79 = 406;
    public static final int tm80 = 407;
    public static final int tm81 = 408;
    public static final int tm82 = 409;
    public static final int tm83 = 410;
    public static final int tm84 = 411;
    public static final int tm85 = 412;
    public static final int tm86 = 413;
    public static final int tm87 = 414;
    public static final int tm88 = 415;
    public static final int tm89 = 416;
    public static final int tm90 = 417;
    public static final int tm91 = 418;
    public static final int tm92 = 419;
    public static final int hm01 = 420;
    public static final int hm02 = 421;
    public static final int hm03 = 422;
    public static final int hm04 = 423;
    public static final int hm05 = 424;
    public static final int hm06 = 425;
    public static final int hm07 = 426; // unused after Gen 4
    public static final int hm08 = 427; // unused after Gen 4
    public static final int explorerKit = 428; // unused in HeartGold and SoulSilver
    public static final int lootSack = 429;
    public static final int ruleBook = 430;
    public static final int pokeRadar = 431;
    public static final int pointCard = 432;
    public static final int journal = 433;
    public static final int sealCase = 434;
    public static final int fashionCase = 435;
    public static final int sealBag = 436;
    public static final int palPad = 437;
    public static final int worksKey = 438;
    public static final int oldCharm = 439;
    public static final int galacticKey = 440;
    public static final int redChain = 441;
    public static final int townMap = 442;
    public static final int vsSeeker = 443;
    public static final int coinCase = 444;
    public static final int oldRod = 445;
    public static final int goodRod = 446;
    public static final int superRod = 447;
    public static final int sprayduck = 448;
    public static final int poffinCase = 449;
    public static final int bike = 450; // Green Bicycle in X/Y
    public static final int suiteKey = 451;
    public static final int oaksLetter = 452;
    public static final int lunarWing = 453;
    public static final int memberCard = 454;
    public static final int azureFlute = 455;
    public static final int ssTicketJohto = 456;
    public static final int contestPass = 457;
    public static final int magmaStone = 458;
    public static final int parcelSinnoh = 459;
    public static final int coupon1 = 460;
    public static final int coupon2 = 461;
    public static final int coupon3 = 462;
    public static final int storageKeySinnoh = 463;
    public static final int secretPotion = 464;

    // These items are generally available in Platinum and onwards.
    public static final int vsRecorder = 465;
    public static final int gracidea = 466;
    public static final int secretKeySinnoh = 467;

    // These items are generally available in HeartGold/SoulSilver and onwards.
    public static final int apricornBox = 468;
    public static final int unownReport = 469;
    public static final int berryPots = 470;
    public static final int dowsingMachine = 471;
    public static final int blueCard = 472;
    public static final int slowpokeTail = 473;
    public static final int clearBell = 474;
    public static final int cardKeyJohto = 475;
    public static final int basementKeyJohto = 476;
    public static final int squirtBottle = 477;
    public static final int redScale = 478;
    public static final int lostItem = 479;
    public static final int pass = 480;
    public static final int machinePart = 481;
    public static final int silverWing = 482;
    public static final int rainbowWing = 483;
    public static final int mysteryEgg = 484;
    public static final int redApricorn = 485;
    public static final int blueApricorn = 486;
    public static final int yellowApricorn = 487;
    public static final int greenApricorn = 488;
    public static final int pinkApricorn = 489;
    public static final int whiteApricorn = 490;
    public static final int blackApricorn = 491;
    public static final int fastBall = 492;
    public static final int levelBall = 493;
    public static final int lureBall = 494;
    public static final int heavyBall = 495;
    public static final int loveBall = 496;
    public static final int friendBall = 497;
    public static final int moonBall = 498;
    public static final int sportBall = 499;
    public static final int parkBall = 500;
    public static final int photoAlbum = 501;
    public static final int gbSounds = 502;
    public static final int tidalBell = 503;
    public static final int rageCandyBar = 504;
    public static final int dataCard01 = 505;
    public static final int dataCard02 = 506;
    public static final int dataCard03 = 507;
    public static final int dataCard04 = 508;
    public static final int dataCard05 = 509;
    public static final int dataCard06 = 510;
    public static final int dataCard07 = 511;
    public static final int dataCard08 = 512;
    public static final int dataCard09 = 513;
    public static final int dataCard10 = 514;
    public static final int dataCard11 = 515;
    public static final int dataCard12 = 516;
    public static final int dataCard13 = 517;
    public static final int dataCard14 = 518;
    public static final int dataCard15 = 519;
    public static final int dataCard16 = 520;
    public static final int dataCard17 = 521;
    public static final int dataCard18 = 522;
    public static final int dataCard19 = 523;
    public static final int dataCard20 = 524;
    public static final int dataCard21 = 525;
    public static final int dataCard22 = 526;
    public static final int dataCard23 = 527;
    public static final int dataCard24 = 528;
    public static final int dataCard25 = 529;
    public static final int dataCard26 = 530;
    public static final int dataCard27 = 531;
    public static final int jadeOrb = 532;
    public static final int lockCapsule = 533;
    public static final int redOrb = 534; // Changed into a held item in Omega Ruby/Alpha Sapphire
    public static final int blueOrb = 535; // Changed into a held item in Omega Ruby/Alpha Sapphire
    public static final int enigmaStone = 536;

    // These items are generally available in Black/White and onwards
    public static final int prismScale = 537;
    public static final int eviolite = 538;
    public static final int floatStone = 539;
    public static final int rockyHelmet = 540;
    public static final int airBalloon = 541;
    public static final int redCard = 542;
    public static final int ringTarget = 543;
    public static final int bindingBand = 544;
    public static final int absorbBulb = 545;
    public static final int cellBattery = 546;
    public static final int ejectButton = 547;
    public static final int fireGem = 548;
    public static final int waterGem = 549;
    public static final int electricGem = 550;
    public static final int grassGem = 551;
    public static final int iceGem = 552;
    public static final int fightingGem = 553;
    public static final int poisonGem = 554;
    public static final int groundGem = 555;
    public static final int flyingGem = 556;
    public static final int psychicGem = 557;
    public static final int bugGem = 558;
    public static final int rockGem = 559;
    public static final int ghostGem = 560;
    public static final int dragonGem = 561;
    public static final int darkGem = 562;
    public static final int steelGem = 563;
    public static final int normalGem = 564;
    public static final int healthFeather = 565;
    public static final int muscleFeather = 566;
    public static final int resistFeather = 567;
    public static final int geniusFeather = 568;
    public static final int cleverFeather = 569;
    public static final int swiftFeather = 570;
    public static final int prettyFeather = 571;
    public static final int coverFossil = 572;
    public static final int plumeFossil = 573;
    public static final int libertyPass = 574;
    public static final int passOrb = 575;
    public static final int dreamBall = 576;
    public static final int pokeToy = 577;
    public static final int propCase = 578;
    public static final int dragonSkull = 579;
    public static final int balmMushroom = 580;
    public static final int bigNugget = 581;
    public static final int pearlString = 582;
    public static final int cometShard = 583;
    public static final int relicCopper = 584;
    public static final int relicSilver = 585;
    public static final int relicGold = 586;
    public static final int relicVase = 587;
    public static final int relicBand = 588;
    public static final int relicStatue = 589;
    public static final int relicCrown = 590;
    public static final int casteliacone = 591;
    public static final int direHit2 = 592;
    public static final int xSpeed2 = 593;
    public static final int xSpAtk2 = 594;
    public static final int xSpDef2 = 595;
    public static final int xDefense2 = 596;
    public static final int xAttack2 = 597;
    public static final int xAccuracy2 = 598;
    public static final int xSpeed3 = 599;
    public static final int xSpAtk3 = 600;
    public static final int xSpDef3 = 601;
    public static final int xDefense3 = 602;
    public static final int xAttack3 = 603;
    public static final int xAccuracy3 = 604;
    public static final int xSpeed6 = 605;
    public static final int xSpAtk6 = 606;
    public static final int xSpDef6 = 607;
    public static final int xDefense6 = 608;
    public static final int xAttack6 = 609;
    public static final int xAccuracy6 = 610;
    public static final int abilityUrge = 611;
    public static final int itemDrop = 612;
    public static final int itemUrge = 613;
    public static final int resetUrge = 614;
    public static final int direHit3 = 615;
    public static final int lightStone = 616;
    public static final int darkStone = 617;
    public static final int tm93 = 618;
    public static final int tm94 = 619;
    public static final int tm95 = 620;
    public static final int xtransceiverMale = 621;
    public static final int unused622 = 622;
    public static final int gram1 = 623;
    public static final int gram2 = 624;
    public static final int gram3 = 625;
    public static final int xtransceiverFemale = 626;

    // These items are generally available in Black 2/White 2 and onwards.
    public static final int medalBox = 627;
    public static final int dNASplicersFuse = 628;
    public static final int dNASplicersSeparate = 629;
    public static final int permit = 630;
    public static final int ovalCharm = 631;
    public static final int shinyCharm = 632;
    public static final int plasmaCard = 633;
    public static final int grubbyHanky = 634;
    public static final int colressMachine = 635;
    public static final int droppedItemCurtis = 636;
    public static final int droppedItemYancy = 637;
    public static final int revealGlass = 638;

    // These items are generally available in X/Y and onwards.
    public static final int weaknessPolicy = 639;
    public static final int assaultVest = 640;
    public static final int holoCasterMale = 641;
    public static final int profsLetter = 642;
    public static final int rollerSkates = 643;
    public static final int pixiePlate = 644;
    public static final int abilityCapsule = 645;
    public static final int whippedDream = 646;
    public static final int sachet = 647;
    public static final int luminousMoss = 648;
    public static final int snowball = 649;
    public static final int safetyGoggles = 650;
    public static final int pokeFlute = 651;
    public static final int richMulch = 652;
    public static final int surpriseMulch = 653;
    public static final int boostMulch = 654;
    public static final int amazeMulch = 655;
    public static final int gengarite = 656;
    public static final int gardevoirite = 657;
    public static final int ampharosite = 658;
    public static final int venusaurite = 659;
    public static final int charizarditeX = 660;
    public static final int blastoisinite = 661;
    public static final int mewtwoniteX = 662;
    public static final int mewtwoniteY = 663;
    public static final int blazikenite = 664;
    public static final int medichamite = 665;
    public static final int houndoominite = 666;
    public static final int aggronite = 667;
    public static final int banettite = 668;
    public static final int tyranitarite = 669;
    public static final int scizorite = 670;
    public static final int pinsirite = 671;
    public static final int aerodactylite = 672;
    public static final int lucarionite = 673;
    public static final int abomasite = 674;
    public static final int kangaskhanite = 675;
    public static final int gyaradosite = 676;
    public static final int absolite = 677;
    public static final int charizarditeY = 678;
    public static final int alakazite = 679;
    public static final int heracronite = 680;
    public static final int mawilite = 681;
    public static final int manectite = 682;
    public static final int garchompite = 683;
    public static final int latiasite = 684;
    public static final int latiosite = 685;
    public static final int roseliBerry = 686;
    public static final int keeBerry = 687;
    public static final int marangaBerry = 688;
    public static final int sprinklotad = 689;
    public static final int tm96 = 690;
    public static final int tm97 = 691;
    public static final int tm98 = 692;
    public static final int tm99 = 693;
    public static final int tm100 = 694;
    public static final int powerPlantPass = 695;
    public static final int megaRing = 696;
    public static final int intriguingStone = 697;
    public static final int commonStone = 698;
    public static final int discountCoupon = 699;
    public static final int elevatorKey = 700;
    public static final int tmvPass = 701;
    public static final int honorofKalos = 702;
    public static final int adventureGuide = 703;
    public static final int strangeSouvenir = 704;
    public static final int lensCase = 705;
    public static final int makeupBag = 706;
    public static final int travelTrunk = 707;
    public static final int lumioseGalette = 708;
    public static final int shalourSable = 709;
    public static final int jawFossil = 710;
    public static final int sailFossil = 711;
    public static final int lookerTicket = 712;
    public static final int bikeYellow = 713;
    public static final int holoCasterFemale = 714;
    public static final int fairyGem = 715;
    public static final int megaCharm = 716;
    public static final int megaGlove = 717;

    // These items are generally available in Omega Ruby/Alpha Sapphire and onwards.
    public static final int machBike = 718;
    public static final int acroBike = 719;
    public static final int wailmerPail = 720;
    public static final int devonParts = 721;
    public static final int sootSack = 722;
    public static final int basementKeyHoenn = 723;
    public static final int pokeblockKit = 724;
    public static final int letter = 725;
    public static final int eonTicket = 726;
    public static final int scanner = 727;
    public static final int goGoggles = 728;
    public static final int meteoriteFirstForm = 729;
    public static final int keytoRoom1 = 730;
    public static final int keytoRoom2 = 731;
    public static final int keytoRoom4 = 732;
    public static final int keytoRoom6 = 733;
    public static final int storageKeyHoenn = 734;
    public static final int devonScope = 735;
    public static final int ssTicketHoenn = 736;
    public static final int hm07ORAS = 737;
    public static final int devonScubaGear = 738;
    public static final int contestCostumeMale = 739;
    public static final int contestCostumeFemale = 740;
    public static final int magmaSuit = 741;
    public static final int aquaSuit = 742;
    public static final int pairOfTickets = 743;
    public static final int megaBracelet = 744;
    public static final int megaPendant = 745;
    public static final int megaGlasses = 746;
    public static final int megaAnchor = 747;
    public static final int megaStickpin = 748;
    public static final int megaTiara = 749;
    public static final int megaAnklet = 750;
    public static final int meteoriteSecondForm = 751;
    public static final int swampertite = 752;
    public static final int sceptilite = 753;
    public static final int sablenite = 754;
    public static final int altarianite = 755;
    public static final int galladite = 756;
    public static final int audinite = 757;
    public static final int metagrossite = 758;
    public static final int sharpedonite = 759;
    public static final int slowbronite = 760;
    public static final int steelixite = 761;
    public static final int pidgeotite = 762;
    public static final int glalitite = 763;
    public static final int diancite = 764;
    public static final int prisonBottle = 765;
    public static final int megaCuff = 766;
    public static final int cameruptite = 767;
    public static final int lopunnite = 768;
    public static final int salamencite = 769;
    public static final int beedrillite = 770;
    public static final int meteoriteThirdForm = 771;
    public static final int meteoriteFinalForm = 772;
    public static final int keyStone = 773;
    public static final int meteoriteShard = 774;
    public static final int eonFlute = 775;

    // These items are generally available in Sun/Moon and onwards.
    public static final int normaliumZHeld = 776;
    public static final int firiumZHeld = 777;
    public static final int wateriumZHeld = 778;
    public static final int electriumZHeld = 779;
    public static final int grassiumZHeld = 780;
    public static final int iciumZHeld = 781;
    public static final int fightiniumZHeld = 782;
    public static final int poisoniumZHeld = 783;
    public static final int groundiumZHeld = 784;
    public static final int flyiniumZHeld = 785;
    public static final int psychiumZHeld = 786;
    public static final int buginiumZHeld = 787;
    public static final int rockiumZHeld = 788;
    public static final int ghostiumZHeld = 789;
    public static final int dragoniumZHeld = 790;
    public static final int darkiniumZHeld = 791;
    public static final int steeliumZHeld = 792;
    public static final int fairiumZHeld = 793;
    public static final int pikaniumZHeld = 794;
    public static final int bottleCap = 795;
    public static final int goldBottleCap = 796;
    public static final int zRing = 797;
    public static final int decidiumZHeld = 798;
    public static final int inciniumZHeld = 799;
    public static final int primariumZHeld = 800;
    public static final int tapuniumZHeld = 801;
    public static final int marshadiumZHeld = 802;
    public static final int aloraichiumZHeld = 803;
    public static final int snorliumZHeld = 804;
    public static final int eeviumZHeld = 805;
    public static final int mewniumZHeld = 806;
    public static final int normaliumZBag = 807;
    public static final int firiumZBag = 808;
    public static final int wateriumZBag = 809;
    public static final int electriumZBag = 810;
    public static final int grassiumZBag = 811;
    public static final int iciumZBag = 812;
    public static final int fightiniumZBag = 813;
    public static final int poisoniumZBag = 814;
    public static final int groundiumZBag = 815;
    public static final int flyiniumZBag = 816;
    public static final int psychiumZBag = 817;
    public static final int buginiumZBag = 818;
    public static final int rockiumZBag = 819;
    public static final int ghostiumZBag = 820;
    public static final int dragoniumZBag = 821;
    public static final int darkiniumZBag = 822;
    public static final int steeliumZBag = 823;
    public static final int fairiumZBag = 824;
    public static final int pikaniumZBag = 825;
    public static final int decidiumZBag = 826;
    public static final int inciniumZBag = 827;
    public static final int primariumZBag = 828;
    public static final int tapuniumZBag = 829;
    public static final int marshadiumZBag = 830;
    public static final int aloraichiumZBag = 831;
    public static final int snorliumZBag = 832;
    public static final int eeviumZBag = 833;
    public static final int mewniumZBag = 834;
    public static final int pikashuniumZHeld = 835;
    public static final int pikashuniumZBag = 836;
    public static final int unused837 = 837;
    public static final int unused838 = 838;
    public static final int unused839 = 839;
    public static final int unused840 = 840;
    public static final int forageBag = 841;
    public static final int fishingRod = 842;
    public static final int professorsMask = 843;
    public static final int festivalTicket = 844;
    public static final int sparklingStone = 845;
    public static final int adrenalineOrb = 846;
    public static final int zygardeCube = 847;
    public static final int unused848 = 848;
    public static final int iceStone = 849;
    public static final int ridePager = 850;
    public static final int beastBall = 851;
    public static final int bigMalasada = 852;
    public static final int redNectar = 853;
    public static final int yellowNectar = 854;
    public static final int pinkNectar = 855;
    public static final int purpleNectar = 856;
    public static final int sunFlute = 857;
    public static final int moonFlute = 858;
    public static final int unused859 = 859;
    public static final int enigmaticCard = 860;
    public static final int silverRazzBerry = 861; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int goldenRazzBerry = 862; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int silverNanabBerry = 863; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int goldenNanabBerry = 864; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int silverPinapBerry = 865; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int goldenPinapBerry = 866; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int unused867 = 867;
    public static final int unused868 = 868;
    public static final int unused869 = 869;
    public static final int unused870 = 870;
    public static final int unused871 = 871;
    public static final int secretKeyKanto = 872; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int ssTicketKanto = 873; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int silphScope = 874; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int parcelKanto = 875; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int cardKeyKanto = 876; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int goldTeeth = 877; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int liftKey = 878; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int terrainExtender = 879;
    public static final int protectivePads = 880;
    public static final int electricSeed = 881;
    public static final int psychicSeed = 882;
    public static final int mistySeed = 883;
    public static final int grassySeed = 884;
    public static final int stretchySpring = 885; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int chalkyStone = 886; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int marble = 887; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int loneEarring = 888; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int beachGlass = 889; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int goldLeaf = 890; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int silverLeaf = 891; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int polishedMudBall = 892; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int tropicalShell = 893; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int leafLetterPikachu = 894; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int leafLetterEevee = 895; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int smallBouquet = 896; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int unused897 = 897;
    public static final int unused898 = 898;
    public static final int unused899 = 899;
    public static final int lure = 900; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int superLure = 901; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int maxLure = 902; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int pewterCrunchies = 903; // unused until Let's Go Pikachu/Let's Go Eevee
    public static final int fightingMemory = 904;
    public static final int flyingMemory = 905;
    public static final int poisonMemory = 906;
    public static final int groundMemory = 907;
    public static final int rockMemory = 908;
    public static final int bugMemory = 909;
    public static final int ghostMemory = 910;
    public static final int steelMemory = 911;
    public static final int fireMemory = 912;
    public static final int waterMemory = 913;
    public static final int grassMemory = 914;
    public static final int electricMemory = 915;
    public static final int psychicMemory = 916;
    public static final int iceMemory = 917;
    public static final int dragonMemory = 918;
    public static final int darkMemory = 919;
    public static final int fairyMemory = 920;

    // These items are generally available in Ultra Sun/Ultra Moon and onwards.
    public static final int solganiumZBag = 921;
    public static final int lunaliumZBag = 922;
    public static final int ultranecroziumZBag = 923;
    public static final int mimikiumZHeld = 924;
    public static final int lycaniumZHeld = 925;
    public static final int kommoniumZHeld = 926;
    public static final int solganiumZHeld = 927;
    public static final int lunaliumZHeld = 928;
    public static final int ultranecroziumZHeld = 929;
    public static final int mimikiumZBag = 930;
    public static final int lycaniumZBag = 931;
    public static final int kommoniumZBag = 932;
    public static final int zPowerRing = 933;
    public static final int pinkPetal = 934;
    public static final int orangePetal = 935;
    public static final int bluePetal = 936;
    public static final int redPetal = 937;
    public static final int greenPetal = 938;
    public static final int yellowPetal = 939;
    public static final int purplePetal = 940;
    public static final int rainbowFlower = 941;
    public static final int surgeBadge = 942;
    public static final int nSolarizerFuse = 943;
    public static final int nLunarizerFuse = 944;
    public static final int nSolarizerSeparate = 945;
    public static final int nLunarizerSeparate = 946;
    public static final int ilimaNormaliumZ = 947;
    public static final int leftPokeBall = 948;
    public static final int rotoHatch = 949;
    public static final int rotoBargain = 950;
    public static final int rotoPrizeMoney = 951;
    public static final int rotoExpPoints = 952;
    public static final int rotoFriendship = 953;
    public static final int rotoEncounter = 954;
    public static final int rotoStealth = 955;
    public static final int rotoHPRestore = 956;
    public static final int rotoPPRestore = 957;
    public static final int rotoBoost = 958;
    public static final int rotoCatch = 959;

    // These items are generally available in Let's Go Pikachu/Let's Go Eevee and onwards.
    public static final int healthCandy = 960;
    public static final int mightyCandy = 961;
    public static final int toughCandy = 962;
    public static final int smartCandy = 963;
    public static final int courageCandy = 964;
    public static final int quickCandy = 965;
    public static final int healthCandyL = 966;
    public static final int mightyCandyL = 967;
    public static final int toughCandyL = 968;
    public static final int smartCandyL = 969;
    public static final int courageCandyL = 970;
    public static final int quickCandyL = 971;
    public static final int healthCandyXL = 972;
    public static final int mightyCandyXL = 973;
    public static final int toughCandyXL = 974;
    public static final int smartCandyXL = 975;
    public static final int courageCandyXL = 976;
    public static final int quickCandyXL = 977;
    public static final int bulbasaurCandy = 978;
    public static final int charmanderCandy = 979;
    public static final int squirtleCandy = 980;
    public static final int caterpieCandy = 981;
    public static final int weedleCandy = 982;
    public static final int pidgeyCandy = 983;
    public static final int rattataCandy = 984;
    public static final int spearowCandy = 985;
    public static final int ekansCandy = 986;
    public static final int pikachuCandy = 987;
    public static final int sandshrewCandy = 988;
    public static final int nidoranFemaleCandy = 989;
    public static final int nidoranMaleCandy = 990;
    public static final int clefairyCandy = 991;
    public static final int vulpixCandy = 992;
    public static final int jigglypuffCandy = 993;
    public static final int zubatCandy = 994;
    public static final int oddishCandy = 995;
    public static final int parasCandy = 996;
    public static final int venonatCandy = 997;
    public static final int diglettCandy = 998;
    public static final int meowthCandy = 999;
    public static final int psyduckCandy = 1000;
    public static final int mankeyCandy = 1001;
    public static final int growlitheCandy = 1002;
    public static final int poliwagCandy = 1003;
    public static final int abraCandy = 1004;
    public static final int machopCandy = 1005;
    public static final int bellsproutCandy = 1006;
    public static final int tentacoolCandy = 1007;
    public static final int geodudeCandy = 1008;
    public static final int ponytaCandy = 1009;
    public static final int slowpokeCandy = 1010;
    public static final int magnemiteCandy = 1011;
    public static final int farfetchdCandy = 1012;
    public static final int doduoCandy = 1013;
    public static final int seelCandy = 1014;
    public static final int grimerCandy = 1015;
    public static final int shellderCandy = 1016;
    public static final int gastlyCandy = 1017;
    public static final int onixCandy = 1018;
    public static final int drowzeeCandy = 1019;
    public static final int krabbyCandy = 1020;
    public static final int voltorbCandy = 1021;
    public static final int exeggcuteCandy = 1022;
    public static final int cuboneCandy = 1023;
    public static final int hitmonleeCandy = 1024;
    public static final int hitmonchanCandy = 1025;
    public static final int lickitungCandy = 1026;
    public static final int koffingCandy = 1027;
    public static final int rhyhornCandy = 1028;
    public static final int chanseyCandy = 1029;
    public static final int tangelaCandy = 1030;
    public static final int kangaskhanCandy = 1031;
    public static final int horseaCandy = 1032;
    public static final int goldeenCandy = 1033;
    public static final int staryuCandy = 1034;
    public static final int mrMimeCandy = 1035;
    public static final int scytherCandy = 1036;
    public static final int jynxCandy = 1037;
    public static final int electabuzzCandy = 1038;
    public static final int magmarCandy = 1039; // Is item 1057 in Let's Go Pikachu/Let's Go Eevee, shifting all below items up.
    public static final int pinsirCandy = 1040;
    public static final int taurosCandy = 1041;
    public static final int magikarpCandy = 1042;
    public static final int laprasCandy = 1043;
    public static final int dittoCandy = 1044;
    public static final int eeveeCandy = 1045;
    public static final int porygonCandy = 1046;
    public static final int omanyteCandy = 1047;
    public static final int kabutoCandy = 1048;
    public static final int aerodactylCandy = 1049;
    public static final int snorlaxCandy = 1050;
    public static final int articunoCandy = 1051;
    public static final int zapdosCandy = 1052;
    public static final int moltresCandy = 1053;
    public static final int dratiniCandy = 1054;
    public static final int mewtwoCandy = 1055;
    public static final int mewCandy = 1056;
    public static final int meltanCandy = 1057;

    // These items are generally available in Sword/Shield v1.0.0 and onwards.
    public static final int unused1058 = 1058;
    public static final int unused1059 = 1059;
    public static final int unused1060 = 1060;
    public static final int unused1061 = 1061;
    public static final int unused1062 = 1062;
    public static final int unused1063 = 1063;
    public static final int unused1064 = 1064;
    public static final int unused1065 = 1065;
    public static final int unused1066 = 1066;
    public static final int unused1067 = 1067;
    public static final int unused1068 = 1068;
    public static final int unused1069 = 1069;
    public static final int unused1070 = 1070;
    public static final int unused1071 = 1071;
    public static final int unused1072 = 1072;
    public static final int unused1073 = 1073;
    public static final int endorsement = 1074;
    public static final int pokemonBoxLink = 1075;
    public static final int wishingStar = 1076;
    public static final int dynamaxBand = 1077;
    public static final int unused1078 = 1078;
    public static final int unused1079 = 1079;
    public static final int fishingRodGalar = 1080;
    public static final int rotomBike = 1081;
    public static final int unused1082 = 1082;
    public static final int unused1083 = 1083;
    public static final int sausages = 1084;
    public static final int bobsFoodTin = 1085;
    public static final int bachsFoodTin = 1086;
    public static final int tinOfBeans = 1087;
    public static final int bread = 1088;
    public static final int pasta = 1089;
    public static final int mixedMushrooms = 1090;
    public static final int smokePokeTail = 1091;
    public static final int largeLeek = 1092;
    public static final int fancyApple = 1093;
    public static final int brittleBones = 1094;
    public static final int packOfPotatoes = 1095;
    public static final int pungentRoot = 1096;
    public static final int saladMix = 1097;
    public static final int friedFood = 1098;
    public static final int boiledEgg = 1099;
    public static final int campingGear = 1100;
    public static final int unused1101 = 1101;
    public static final int unused1102 = 1102;
    public static final int rustedSword = 1103;
    public static final int rustedShield = 1104;
    public static final int fossilizedBird = 1105;
    public static final int fossilizedFish = 1106;
    public static final int fossilizedDrake = 1107;
    public static final int fossilizedDino = 1108;
    public static final int strawberrySweet = 1109;
    public static final int loveSweet = 1110;
    public static final int berrySweet = 1111;
    public static final int cloverSweet = 1112;
    public static final int flowerSweet = 1113;
    public static final int starSweet = 1114;
    public static final int ribbonSweet = 1115;
    public static final int sweetApple = 1116;
    public static final int tartApple = 1117;
    public static final int throatSpray = 1118;
    public static final int ejectPack = 1119;
    public static final int heavyDutyBoots = 1120;
    public static final int blunderPolicy = 1121;
    public static final int roomService = 1122;
    public static final int utilityUmbrella = 1123;
    public static final int expCandyXS = 1124;
    public static final int expCandyS = 1125;
    public static final int expCandyM = 1126;
    public static final int expCandyL = 1127;
    public static final int expCandyXL = 1128;
    public static final int dynamaxCandy = 1129;
    public static final int tr00 = 1130;
    public static final int tr01 = 1131;
    public static final int tr02 = 1132;
    public static final int tr03 = 1133;
    public static final int tr04 = 1134;
    public static final int tr05 = 1135;
    public static final int tr06 = 1136;
    public static final int tr07 = 1137;
    public static final int tr08 = 1138;
    public static final int tr09 = 1139;
    public static final int tr10 = 1140;
    public static final int tr11 = 1141;
    public static final int tr12 = 1142;
    public static final int tr13 = 1143;
    public static final int tr14 = 1144;
    public static final int tr15 = 1145;
    public static final int tr16 = 1146;
    public static final int tr17 = 1147;
    public static final int tr18 = 1148;
    public static final int tr19 = 1149;
    public static final int tr20 = 1150;
    public static final int tr21 = 1151;
    public static final int tr22 = 1152;
    public static final int tr23 = 1153;
    public static final int tr24 = 1154;
    public static final int tr25 = 1155;
    public static final int tr26 = 1156;
    public static final int tr27 = 1157;
    public static final int tr28 = 1158;
    public static final int tr29 = 1159;
    public static final int tr30 = 1160;
    public static final int tr31 = 1161;
    public static final int tr32 = 1162;
    public static final int tr33 = 1163;
    public static final int tr34 = 1164;
    public static final int tr35 = 1165;
    public static final int tr36 = 1166;
    public static final int tr37 = 1167;
    public static final int tr38 = 1168;
    public static final int tr39 = 1169;
    public static final int tr40 = 1170;
    public static final int tr41 = 1171;
    public static final int tr42 = 1172;
    public static final int tr43 = 1173;
    public static final int tr44 = 1174;
    public static final int tr45 = 1175;
    public static final int tr46 = 1176;
    public static final int tr47 = 1177;
    public static final int tr48 = 1178;
    public static final int tr49 = 1179;
    public static final int tr50 = 1180;
    public static final int tr51 = 1181;
    public static final int tr52 = 1182;
    public static final int tr53 = 1183;
    public static final int tr54 = 1184;
    public static final int tr55 = 1185;
    public static final int tr56 = 1186;
    public static final int tr57 = 1187;
    public static final int tr58 = 1188;
    public static final int tr59 = 1189;
    public static final int tr60 = 1190;
    public static final int tr61 = 1191;
    public static final int tr62 = 1192;
    public static final int tr63 = 1193;
    public static final int tr64 = 1194;
    public static final int tr65 = 1195;
    public static final int tr66 = 1196;
    public static final int tr67 = 1197;
    public static final int tr68 = 1198;
    public static final int tr69 = 1199;
    public static final int tr70 = 1200;
    public static final int tr71 = 1201;
    public static final int tr72 = 1202;
    public static final int tr73 = 1203;
    public static final int tr74 = 1204;
    public static final int tr75 = 1205;
    public static final int tr76 = 1206;
    public static final int tr77 = 1207;
    public static final int tr78 = 1208;
    public static final int tr79 = 1209;
    public static final int tr80 = 1210;
    public static final int tr81 = 1211;
    public static final int tr82 = 1212;
    public static final int tr83 = 1213;
    public static final int tr84 = 1214;
    public static final int tr85 = 1215;
    public static final int tr86 = 1216;
    public static final int tr87 = 1217;
    public static final int tr88 = 1218;
    public static final int tr89 = 1219;
    public static final int tr90 = 1220;
    public static final int tr91 = 1221;
    public static final int tr92 = 1222;
    public static final int tr93 = 1223;
    public static final int tr94 = 1224;
    public static final int tr95 = 1225;
    public static final int tr96 = 1226;
    public static final int tr97 = 1227;
    public static final int tr98 = 1228;
    public static final int tr99 = 1229;
    public static final int tm00 = 1230;
    public static final int lonelyMint = 1231;
    public static final int adamantMint = 1232;
    public static final int naughtyMint = 1233;
    public static final int braveMint = 1234;
    public static final int boldMint = 1235;
    public static final int impishMint = 1236;
    public static final int laxMint = 1237;
    public static final int relaxedMint = 1238;
    public static final int modestMint = 1239;
    public static final int mildMint = 1240;
    public static final int rashMint = 1241;
    public static final int quietMint = 1242;
    public static final int calmMint = 1243;
    public static final int gentleMint = 1244;
    public static final int carefulMint = 1245;
    public static final int sassyMint = 1246;
    public static final int timidMint = 1247;
    public static final int hastyMint = 1248;
    public static final int jollyMint = 1249;
    public static final int naiveMint = 1250;
    public static final int seriousMint = 1251;
    public static final int wishingPiece = 1252;
    public static final int crackedPot = 1253;
    public static final int chippedPot = 1254;
    public static final int hiTechEarbuds = 1255;
    public static final int fruitBunch = 1256;
    public static final int moomooCheese = 1257;
    public static final int spiceMix = 1258;
    public static final int freshCream = 1259;
    public static final int packagedCurry = 1260;
    public static final int coconutMilk = 1261;
    public static final int instantNoodles = 1262;
    public static final int precookedBurger = 1263;
    public static final int gigantamix = 1264;
    public static final int wishingChip = 1265;
    public static final int rotomBikeWaterMode = 1266;
    public static final int catchingCharm = 1267;
    public static final int unused1268 = 1268;
    public static final int oldLetter = 1269;
    public static final int bandAutograph = 1270;
    public static final int soniasBook = 1271;
    public static final int unused1272 = 1272;
    public static final int unused1273 = 1273;
    public static final int unused1274 = 1274;
    public static final int unused1275 = 1275;
    public static final int unused1276 = 1276;
    public static final int unused1277 = 1277;
    public static final int rotomCatalog = 1278;
    public static final int starAnd458 = 1279;
    public static final int starAnd15 = 1280;
    public static final int starAnd337 = 1281;
    public static final int starAnd603 = 1282;
    public static final int starAnd390 = 1283;
    public static final int starSgr6879 = 1284;
    public static final int starSgr6859 = 1285;
    public static final int starSgr6913 = 1286;
    public static final int starSgr7348 = 1287;
    public static final int starSgr7121 = 1288;
    public static final int starSgr6746 = 1289;
    public static final int starSgr7194 = 1290;
    public static final int starSgr7337 = 1291;
    public static final int starSgr7343 = 1292;
    public static final int starSgr6812 = 1293;
    public static final int starSgr7116 = 1294;
    public static final int starSgr7264 = 1295;
    public static final int starSgr7597 = 1296;
    public static final int starDel7882 = 1297;
    public static final int starDel7906 = 1298;
    public static final int starDel7852 = 1299;
    public static final int starPsc596 = 1300;
    public static final int starPsc361 = 1301;
    public static final int starPsc510 = 1302;
    public static final int starPsc437 = 1303;
    public static final int starPsc8773 = 1304;
    public static final int starLep1865 = 1305;
    public static final int starLep1829 = 1306;
    public static final int starBoo5340 = 1307;
    public static final int starBoo5506 = 1308;
    public static final int starBoo5435 = 1309;
    public static final int starBoo5602 = 1310;
    public static final int starBoo5733 = 1311;
    public static final int starBoo5235 = 1312;
    public static final int starBoo5351 = 1313;
    public static final int starHya3748 = 1314;
    public static final int starHya3903 = 1315;
    public static final int starHya3418 = 1316;
    public static final int starHya3482 = 1317;
    public static final int starHya3845 = 1318;
    public static final int starEri1084 = 1319;
    public static final int starEri472 = 1320;
    public static final int starEri1666 = 1321;
    public static final int starEri897 = 1322;
    public static final int starEri1231 = 1323;
    public static final int starEri874 = 1324;
    public static final int starEri1298 = 1325;
    public static final int starEri1325 = 1326;
    public static final int starEri984 = 1327;
    public static final int starEri1464 = 1328;
    public static final int starEri1393 = 1329;
    public static final int starEri850 = 1330;
    public static final int starTau1409 = 1331;
    public static final int starTau1457 = 1332;
    public static final int starTau1165 = 1333;
    public static final int starTau1791 = 1334;
    public static final int starTau1910 = 1335;
    public static final int starTau1346 = 1336;
    public static final int starTau1373 = 1337;
    public static final int starTau1412 = 1338;
    public static final int starCMa2491 = 1339;
    public static final int starCMa2693 = 1340;
    public static final int starCMa2294 = 1341;
    public static final int starCMa2827 = 1342;
    public static final int starCMa2282 = 1343;
    public static final int starCMa2618 = 1344;
    public static final int starCMa2657 = 1345;
    public static final int starCMa2646 = 1346;
    public static final int starUMa4905 = 1347;
    public static final int starUMa4301 = 1348;
    public static final int starUMa5191 = 1349;
    public static final int starUMa5054 = 1350;
    public static final int starUMa4295 = 1351;
    public static final int starUMa4660 = 1352;
    public static final int starUMa4554 = 1353;
    public static final int starUMa4069 = 1354;
    public static final int starUMa3569 = 1355;
    public static final int starUMa3323 = 1356;
    public static final int starUMa4033 = 1357;
    public static final int starUMa4377 = 1358;
    public static final int starUMa4375 = 1359;
    public static final int starUMa4518 = 1360;
    public static final int starUMa3594 = 1361;
    public static final int starVir5056 = 1362;
    public static final int starVir4825 = 1363;
    public static final int starVir4932 = 1364;
    public static final int starVir4540 = 1365;
    public static final int starVir4689 = 1366;
    public static final int starVir5338 = 1367;
    public static final int starVir4910 = 1368;
    public static final int starVir5315 = 1369;
    public static final int starVir5359 = 1370;
    public static final int starVir5409 = 1371;
    public static final int starVir5107 = 1372;
    public static final int starAri617 = 1373;
    public static final int starAri553 = 1374;
    public static final int starAri546 = 1375;
    public static final int starAri951 = 1376;
    public static final int starOri1713 = 1377;
    public static final int starOri2061 = 1378;
    public static final int starOri1790 = 1379;
    public static final int starOri1903 = 1380;
    public static final int starOri1948 = 1381;
    public static final int starOri2004 = 1382;
    public static final int starOri1852 = 1383;
    public static final int starOri1879 = 1384;
    public static final int starOri1899 = 1385;
    public static final int starOri1543 = 1386;
    public static final int starCas21 = 1387;
    public static final int starCas168 = 1388;
    public static final int starCas403 = 1389;
    public static final int starCas153 = 1390;
    public static final int starCas542 = 1391;
    public static final int starCas219 = 1392;
    public static final int starCas265 = 1393;
    public static final int starCnc3572 = 1394;
    public static final int starCnc3208 = 1395;
    public static final int starCnc3461 = 1396;
    public static final int starCnc3449 = 1397;
    public static final int starCnc3429 = 1398;
    public static final int starCnc3627 = 1399;
    public static final int starCnc3268 = 1400;
    public static final int starCnc3249 = 1401;
    public static final int starCom4968 = 1402;
    public static final int starCrv4757 = 1403;
    public static final int starCrv4623 = 1404;
    public static final int starCrv4662 = 1405;
    public static final int starCrv4786 = 1406;
    public static final int starAur1708 = 1407;
    public static final int starAur2088 = 1408;
    public static final int starAur1605 = 1409;
    public static final int starAur2095 = 1410;
    public static final int starAur1577 = 1411;
    public static final int starAur1641 = 1412;
    public static final int starAur1612 = 1413;
    public static final int starPav7790 = 1414;
    public static final int starCet911 = 1415;
    public static final int starCet681 = 1416;
    public static final int starCet188 = 1417;
    public static final int starCet539 = 1418;
    public static final int starCet804 = 1419;
    public static final int starCep8974 = 1420;
    public static final int starCep8162 = 1421;
    public static final int starCep8238 = 1422;
    public static final int starCep8417 = 1423;
    public static final int starCen5267 = 1424;
    public static final int starCen5288 = 1425;
    public static final int starCen551 = 1426;
    public static final int starCen5459 = 1427;
    public static final int starCen5460 = 1428;
    public static final int starCMi2943 = 1429;
    public static final int starCMi2845 = 1430;
    public static final int starEqu8131 = 1431;
    public static final int starVul7405 = 1432;
    public static final int starUMi424 = 1433;
    public static final int starUMi5563 = 1434;
    public static final int starUMi5735 = 1435;
    public static final int starUMi6789 = 1436;
    public static final int starCrt4287 = 1437;
    public static final int starLyr7001 = 1438;
    public static final int starLyr7178 = 1439;
    public static final int starLyr7106 = 1440;
    public static final int starLyr7298 = 1441;
    public static final int starAra6585 = 1442;
    public static final int starSco6134 = 1443;
    public static final int starSco6527 = 1444;
    public static final int starSco6553 = 1445;
    public static final int starSco5953 = 1446;
    public static final int starSco5984 = 1447;
    public static final int starSco6508 = 1448;
    public static final int starSco6084 = 1449;
    public static final int starSco5944 = 1450;
    public static final int starSco6630 = 1451;
    public static final int starSco6027 = 1452;
    public static final int starSco6247 = 1453;
    public static final int starSco6252 = 1454;
    public static final int starSco5928 = 1455;
    public static final int starSco6241 = 1456;
    public static final int starSco6165 = 1457;
    public static final int starTri544 = 1458;
    public static final int starLeo3982 = 1459;
    public static final int starLeo4534 = 1460;
    public static final int starLeo4357 = 1461;
    public static final int starLeo4057 = 1462;
    public static final int starLeo4359 = 1463;
    public static final int starLeo4031 = 1464;
    public static final int starLeo3852 = 1465;
    public static final int starLeo3905 = 1466;
    public static final int starLeo3773 = 1467;
    public static final int starGru8425 = 1468;
    public static final int starGru8636 = 1469;
    public static final int starGru8353 = 1470;
    public static final int starLib5685 = 1471;
    public static final int starLib5531 = 1472;
    public static final int starLib5787 = 1473;
    public static final int starLib5603 = 1474;
    public static final int starPup3165 = 1475;
    public static final int starPup3185 = 1476;
    public static final int starPup3045 = 1477;
    public static final int starCyg7924 = 1478;
    public static final int starCyg7417 = 1479;
    public static final int starCyg7796 = 1480;
    public static final int starCyg8301 = 1481;
    public static final int starCyg7949 = 1482;
    public static final int starCyg7528 = 1483;
    public static final int starOct7228 = 1484;
    public static final int starCol1956 = 1485;
    public static final int starCol2040 = 1486;
    public static final int starCol2177 = 1487;
    public static final int starGem2990 = 1488;
    public static final int starGem2891 = 1489;
    public static final int starGem2421 = 1490;
    public static final int starGem2473 = 1491;
    public static final int starGem2216 = 1492;
    public static final int starGem2777 = 1493;
    public static final int starGem2650 = 1494;
    public static final int starGem2286 = 1495;
    public static final int starGem2484 = 1496;
    public static final int starGem2930 = 1497;
    public static final int starPeg8775 = 1498;
    public static final int starPeg8781 = 1499;
    public static final int starPeg39 = 1500;
    public static final int starPeg8308 = 1501;
    public static final int starPeg8650 = 1502;
    public static final int starPeg8634 = 1503;
    public static final int starPeg8684 = 1504;
    public static final int starPeg8450 = 1505;
    public static final int starPeg8880 = 1506;
    public static final int starPeg8905 = 1507;
    public static final int starOph6556 = 1508;
    public static final int starOph6378 = 1509;
    public static final int starOph6603 = 1510;
    public static final int starOph6149 = 1511;
    public static final int starOph6056 = 1512;
    public static final int starOph6075 = 1513;
    public static final int starSer5854 = 1514;
    public static final int starSer7141 = 1515;
    public static final int starSer5879 = 1516;
    public static final int starHer6406 = 1517;
    public static final int starHer6148 = 1518;
    public static final int starHer6410 = 1519;
    public static final int starHer6526 = 1520;
    public static final int starHer6117 = 1521;
    public static final int starHer6008 = 1522;
    public static final int starPer936 = 1523;
    public static final int starPer1017 = 1524;
    public static final int starPer1131 = 1525;
    public static final int starPer1228 = 1526;
    public static final int starPer834 = 1527;
    public static final int starPer941 = 1528;
    public static final int starPhe99 = 1529;
    public static final int starPhe338 = 1530;
    public static final int starVel3634 = 1531;
    public static final int starVel3485 = 1532;
    public static final int starVel3734 = 1533;
    public static final int starAqr8232 = 1534;
    public static final int starAqr8414 = 1535;
    public static final int starAqr8709 = 1536;
    public static final int starAqr8518 = 1537;
    public static final int starAqr7950 = 1538;
    public static final int starAqr8499 = 1539;
    public static final int starAqr8610 = 1540;
    public static final int starAqr8264 = 1541;
    public static final int starCru4853 = 1542;
    public static final int starCru4730 = 1543;
    public static final int starCru4763 = 1544;
    public static final int starCru4700 = 1545;
    public static final int starCru4656 = 1546;
    public static final int starPsA8728 = 1547;
    public static final int starTrA6217 = 1548;
    public static final int starCap7776 = 1549;
    public static final int starCap7754 = 1550;
    public static final int starCap8278 = 1551;
    public static final int starCap8322 = 1552;
    public static final int starCap7773 = 1553;
    public static final int starSge7479 = 1554;
    public static final int starCar2326 = 1555;
    public static final int starCar3685 = 1556;
    public static final int starCar3307 = 1557;
    public static final int starCar3699 = 1558;
    public static final int starDra5744 = 1559;
    public static final int starDra5291 = 1560;
    public static final int starDra6705 = 1561;
    public static final int starDra6536 = 1562;
    public static final int starDra7310 = 1563;
    public static final int starDra6688 = 1564;
    public static final int starDra4434 = 1565;
    public static final int starDra6370 = 1566;
    public static final int starDra7462 = 1567;
    public static final int starDra6396 = 1568;
    public static final int starDra6132 = 1569;
    public static final int starDra6636 = 1570;
    public static final int starCVn4915 = 1571;
    public static final int starCVn4785 = 1572;
    public static final int starCVn4846 = 1573;
    public static final int starAql7595 = 1574;
    public static final int starAql7557 = 1575;
    public static final int starAql7525 = 1576;
    public static final int starAql7602 = 1577;
    public static final int starAql7235 = 1578;

    // These items are generally available in Sword/Shield v1.2.0 and onwards.
    public static final int maxHoney = 1579;
    public static final int maxMushrooms = 1580;
    public static final int galaricaTwig = 1581;
    public static final int galaricaCuff = 1582;
    public static final int styleCard = 1583;
    public static final int armorPass = 1584;
    public static final int rotomBikeSparkingWhite = 1585;
    public static final int rotomBikeGlisteningBlack = 1586;
    public static final int expCharm = 1587;
    public static final int armoriteOre = 1588;
    public static final int markCharm = 1589;

    // These items are generally available in Sword/Shield v1.3.0 and onwards.
    public static final int reinsofUnityFuse = 1590;
    public static final int reinsofUnitySeparate = 1591;
    public static final int galaricaWreath = 1592;
    public static final int legendaryClue1 = 1593;
    public static final int legendaryClue2 = 1594;
    public static final int legendaryClue3 = 1595;
    public static final int legendaryClueQuestionMark = 1596;
    public static final int crownPass = 1597;
    public static final int woodenCrown = 1598;
    public static final int radiantPetal = 1599;
    public static final int whiteManeHair = 1600;
    public static final int blackManeHair = 1601;
    public static final int icerootCarrot = 1602;
    public static final int shaderootCarrot = 1603;
    public static final int dyniteOre = 1604;
    public static final int carrotSeeds = 1605;
    public static final int abilityPatch = 1606;
    public static final int reinsofUnity = 1607;
}
