#ifndef GUARD_CONSTANTS_TRAINERS_H
#define GUARD_CONSTANTS_TRAINERS_H

#include "opponents.h"

#define TRAINER_ENCOUNTER_MUSIC_MALE         0 // standard male encounter music
#define TRAINER_ENCOUNTER_MUSIC_FEMALE       1 // standard female encounter music
#define TRAINER_ENCOUNTER_MUSIC_GIRL         2 // used for male Tubers and Young Couples too
#define TRAINER_ENCOUNTER_MUSIC_SUSPICIOUS   3
#define TRAINER_ENCOUNTER_MUSIC_INTENSE      4
#define TRAINER_ENCOUNTER_MUSIC_COOL         5
#define TRAINER_ENCOUNTER_MUSIC_AQUA         6
#define TRAINER_ENCOUNTER_MUSIC_MAGMA        7
#define TRAINER_ENCOUNTER_MUSIC_SWIMMER      8
#define TRAINER_ENCOUNTER_MUSIC_TWINS        9 // used for other trainer classes too
#define TRAINER_ENCOUNTER_MUSIC_ELITE_FOUR  10
#define TRAINER_ENCOUNTER_MUSIC_HIKER       11 // used for other trainer classes too
#define TRAINER_ENCOUNTER_MUSIC_INTERVIEWER 12
#define TRAINER_ENCOUNTER_MUSIC_RICH        13 // Rich Boys and Gentlemen

#define TRAINER_PIC_AQUA_LEADER_ARCHIE      0
#define TRAINER_PIC_AQUA_GRUNT_M            1
#define TRAINER_PIC_AQUA_GRUNT_F            2
#define TRAINER_PIC_RS_AROMA_LADY           3
#define TRAINER_PIC_RS_RUIN_MANIAC          4
#define TRAINER_PIC_INTERVIEWER             5
#define TRAINER_PIC_RS_TUBER_F              6
#define TRAINER_PIC_RS_TUBER_M              7
#define TRAINER_PIC_RS_COOLTRAINER_M        8
#define TRAINER_PIC_RS_COOLTRAINER_F        9
#define TRAINER_PIC_HEX_MANIAC             10
#define TRAINER_PIC_RS_LADY                11
#define TRAINER_PIC_RS_BEAUTY              12
#define TRAINER_PIC_RICH_BOY               13
#define TRAINER_PIC_RS_POKEMANIAC          14
#define TRAINER_PIC_RS_SWIMMER_M           15
#define TRAINER_PIC_RS_BLACK_BELT          16
#define TRAINER_PIC_GUITARIST              17
#define TRAINER_PIC_KINDLER                18
#define TRAINER_PIC_RS_CAMPER              19
#define TRAINER_PIC_BUG_MANIAC             20
#define TRAINER_PIC_RS_PSYCHIC_M           21
#define TRAINER_PIC_RS_PSYCHIC_F           22
#define TRAINER_PIC_RS_GENTLEMAN           23
#define TRAINER_PIC_ELITE_FOUR_SIDNEY      24
#define TRAINER_PIC_ELITE_FOUR_PHOEBE      25
#define TRAINER_PIC_LEADER_ROXANNE         26
#define TRAINER_PIC_LEADER_BRAWLY          27
#define TRAINER_PIC_LEADER_TATE_AND_LIZA   28
#define TRAINER_PIC_SCHOOL_KID_M           29
#define TRAINER_PIC_SCHOOL_KID_F           30
#define TRAINER_PIC_SR_AND_JR              31
#define TRAINER_PIC_POKEFAN_M              32
#define TRAINER_PIC_POKEFAN_F              33
#define TRAINER_PIC_EXPERT_M               34
#define TRAINER_PIC_EXPERT_F               35
#define TRAINER_PIC_RS_YOUNGSTER           36
#define TRAINER_PIC_CHAMPION_STEVEN        37
#define TRAINER_PIC_RS_FISHERMAN           38
#define TRAINER_PIC_CYCLING_TRIATHLETE_M   39
#define TRAINER_PIC_CYCLING_TRIATHLETE_F   40
#define TRAINER_PIC_RUNNING_TRIATHLETE_M   41
#define TRAINER_PIC_RUNNING_TRIATHLETE_F   42
#define TRAINER_PIC_SWIMMING_TRIATHLETE_M  43
#define TRAINER_PIC_SWIMMING_TRIATHLETE_F  44
#define TRAINER_PIC_DRAGON_TAMER           45
#define TRAINER_PIC_RS_BIRD_KEEPER         46
#define TRAINER_PIC_NINJA_BOY              47
#define TRAINER_PIC_BATTLE_GIRL            48
#define TRAINER_PIC_PARASOL_LADY           49
#define TRAINER_PIC_RS_SWIMMER_F           50
#define TRAINER_PIC_RS_PICNICKER           51
#define TRAINER_PIC_RS_TWINS               52
#define TRAINER_PIC_RS_SAILOR              53
#define TRAINER_PIC_COLLECTOR              54
#define TRAINER_PIC_WALLY                  55
#define TRAINER_PIC_RS_BRENDAN_1           56
#define TRAINER_PIC_RS_MAY_1               57
#define TRAINER_PIC_RS_POKEMON_BREEDER_M   58
#define TRAINER_PIC_RS_POKEMON_BREEDER_F   59
#define TRAINER_PIC_RS_POKEMON_RANGER_M    60
#define TRAINER_PIC_RS_POKEMON_RANGER_F    61
#define TRAINER_PIC_MAGMA_LEADER_MAXIE     62
#define TRAINER_PIC_MAGMA_GRUNT_M          63
#define TRAINER_PIC_MAGMA_GRUNT_F          64
#define TRAINER_PIC_RS_LASS                65
#define TRAINER_PIC_RS_BUG_CATCHER         66
#define TRAINER_PIC_RS_HIKER               67
#define TRAINER_PIC_RS_YOUNG_COUPLE        68
#define TRAINER_PIC_OLD_COUPLE             69
#define TRAINER_PIC_RS_SIS_AND_BRO         70
#define TRAINER_PIC_AQUA_ADMIN_M           71
#define TRAINER_PIC_AQUA_ADMIN_F           72
#define TRAINER_PIC_MAGMA_ADMIN_M          73
#define TRAINER_PIC_MAGMA_ADMIN_F          74
#define TRAINER_PIC_LEADER_WATTSON         75
#define TRAINER_PIC_LEADER_FLANNERY        76
#define TRAINER_PIC_LEADER_NORMAN          77
#define TRAINER_PIC_LEADER_WINONA          78
#define TRAINER_PIC_LEADER_WALLACE         79
#define TRAINER_PIC_ELITE_FOUR_GLACIA      80
#define TRAINER_PIC_ELITE_FOUR_DRAKE       81
#define TRAINER_PIC_YOUNGSTER              82
#define TRAINER_PIC_BUG_CATCHER            83
#define TRAINER_PIC_LASS                   84
#define TRAINER_PIC_SAILOR                 85
#define TRAINER_PIC_CAMPER                 86
#define TRAINER_PIC_PICNICKER              87
#define TRAINER_PIC_POKEMANIAC             88
#define TRAINER_PIC_SUPER_NERD             89
#define TRAINER_PIC_HIKER                  90
#define TRAINER_PIC_BIKER                  91
#define TRAINER_PIC_BURGLAR                92
#define TRAINER_PIC_ENGINEER               93
#define TRAINER_PIC_FISHERMAN              94
#define TRAINER_PIC_SWIMMER_M              95
#define TRAINER_PIC_CUE_BALL               96
#define TRAINER_PIC_GAMER                  97
#define TRAINER_PIC_BEAUTY                 98
#define TRAINER_PIC_SWIMMER_F              99
#define TRAINER_PIC_PSYCHIC_M             100
#define TRAINER_PIC_ROCKER                101
#define TRAINER_PIC_JUGGLER               102
#define TRAINER_PIC_TAMER                 103
#define TRAINER_PIC_BIRD_KEEPER           104
#define TRAINER_PIC_BLACK_BELT            105
#define TRAINER_PIC_RIVAL_EARLY           106
#define TRAINER_PIC_SCIENTIST             107
#define TRAINER_PIC_LEADER_GIOVANNI       108
#define TRAINER_PIC_ROCKET_GRUNT_M        109
#define TRAINER_PIC_COOLTRAINER_M         110
#define TRAINER_PIC_COOLTRAINER_F         111
#define TRAINER_PIC_ELITE_FOUR_LORELEI    112
#define TRAINER_PIC_ELITE_FOUR_BRUNO      113
#define TRAINER_PIC_ELITE_FOUR_AGATHA     114
#define TRAINER_PIC_ELITE_FOUR_LANCE      115
#define TRAINER_PIC_LEADER_BROCK          116
#define TRAINER_PIC_LEADER_MISTY          117
#define TRAINER_PIC_LEADER_LT_SURGE       118
#define TRAINER_PIC_LEADER_ERIKA          119
#define TRAINER_PIC_LEADER_KOGA           120
#define TRAINER_PIC_LEADER_BLAINE         121
#define TRAINER_PIC_LEADER_SABRINA        122
#define TRAINER_PIC_GENTLEMAN             123
#define TRAINER_PIC_RIVAL_LATE            124
#define TRAINER_PIC_CHAMPION_RIVAL        125
#define TRAINER_PIC_CHANNELER             126
#define TRAINER_PIC_TWINS                 127
#define TRAINER_PIC_COOL_COUPLE           128
#define TRAINER_PIC_YOUNG_COUPLE          129
#define TRAINER_PIC_CRUSH_KIN             130
#define TRAINER_PIC_SIS_AND_BRO           131
#define TRAINER_PIC_PROFESSOR_OAK         132
#define TRAINER_PIC_RS_BRENDAN_2          133
#define TRAINER_PIC_RS_MAY_2              134
#define TRAINER_PIC_RED                   135
#define TRAINER_PIC_LEAF                  136
#define TRAINER_PIC_ROCKET_GRUNT_F        137
#define TRAINER_PIC_PSYCHIC_F             138
#define TRAINER_PIC_CRUSH_GIRL            139
#define TRAINER_PIC_TUBER_F               140
#define TRAINER_PIC_POKEMON_BREEDER       141
#define TRAINER_PIC_POKEMON_RANGER_M      142
#define TRAINER_PIC_POKEMON_RANGER_F      143
#define TRAINER_PIC_AROMA_LADY            144
#define TRAINER_PIC_RUIN_MANIAC           145
#define TRAINER_PIC_LADY                  146
#define TRAINER_PIC_PAINTER               147

// The player back pics are assumed to alternate according to the gender values (MALE/FEMALE)
#define TRAINER_BACK_PIC_RED                    0
#define TRAINER_BACK_PIC_LEAF                   1
#define TRAINER_BACK_PIC_RUBY_SAPPHIRE_BRENDAN  2
#define TRAINER_BACK_PIC_RUBY_SAPPHIRE_MAY      3
#define TRAINER_BACK_PIC_POKEDUDE               4
#define TRAINER_BACK_PIC_OLD_MAN                5

// Special Trainer Ids.
#define TRAINER_UNION_ROOM          0xC00
#define TRAINER_LINK_OPPONENT       0x800
#define TRAINER_SECRET_BASE         0x400

#define TRAINER_CLASS_NONE                0
#define TRAINER_CLASS_PKMN_TRAINER_UNUSED 1
#define TRAINER_CLASS_AQUA_LEADER         2
#define TRAINER_CLASS_TEAM_AQUA           3
#define TRAINER_CLASS_RS_AROMA_LADY       4
#define TRAINER_CLASS_RS_RUIN_MANIAC      5
#define TRAINER_CLASS_INTERVIEWER         6
#define TRAINER_CLASS_RS_TUBER_F          7
#define TRAINER_CLASS_RS_TUBER_M          8
#define TRAINER_CLASS_RS_COOLTRAINER      9
#define TRAINER_CLASS_HEX_MANIAC          10
#define TRAINER_CLASS_RS_LADY             11
#define TRAINER_CLASS_RS_BEAUTY           12
#define TRAINER_CLASS_RICH_BOY            13
#define TRAINER_CLASS_RS_POKEMANIAC       14
#define TRAINER_CLASS_RS_SWIMMER_M        15
#define TRAINER_CLASS_RS_BLACK_BELT       16
#define TRAINER_CLASS_GUITARIST           17
#define TRAINER_CLASS_KINDLER             18
#define TRAINER_CLASS_RS_CAMPER           19
#define TRAINER_CLASS_BUG_MANIAC          20
#define TRAINER_CLASS_RS_PSYCHIC          21
#define TRAINER_CLASS_RS_GENTLEMAN        22
#define TRAINER_CLASS_RS_ELITE_FOUR       23
#define TRAINER_CLASS_RS_LEADER           24
#define TRAINER_CLASS_SCHOOL_KID          25
#define TRAINER_CLASS_SR_AND_JR           26
#define TRAINER_CLASS_POKEFAN             27
#define TRAINER_CLASS_EXPERT              28
#define TRAINER_CLASS_RS_YOUNGSTER        29
#define TRAINER_CLASS_RS_CHAMPION         30
#define TRAINER_CLASS_RS_FISHERMAN        31
#define TRAINER_CLASS_TRIATHLETE          32
#define TRAINER_CLASS_DRAGON_TAMER        33
#define TRAINER_CLASS_RS_BIRD_KEEPER      34
#define TRAINER_CLASS_NINJA_BOY           35
#define TRAINER_CLASS_BATTLE_GIRL         36
#define TRAINER_CLASS_PARASOL_LADY        37
#define TRAINER_CLASS_RS_SWIMMER_F        38
#define TRAINER_CLASS_RS_PICNICKER        39
#define TRAINER_CLASS_RS_TWINS            40
#define TRAINER_CLASS_RS_SAILOR           41
#define TRAINER_CLASS_BOARDER             42
#define TRAINER_CLASS_COLLECTOR           43
#define TRAINER_CLASS_PKMN_TRAINER        44
#define TRAINER_CLASS_RS_PKMN_BREEDER     45
#define TRAINER_CLASS_RS_PKMN_RANGER      46
#define TRAINER_CLASS_MAGMA_LEADER        47
#define TRAINER_CLASS_TEAM_MAGMA          48
#define TRAINER_CLASS_RS_LASS             49
#define TRAINER_CLASS_RS_BUG_CATCHER      50
#define TRAINER_CLASS_RS_HIKER            51
#define TRAINER_CLASS_RS_YOUNG_COUPLE     52
#define TRAINER_CLASS_OLD_COUPLE          53
#define TRAINER_CLASS_RS_SIS_AND_BRO      54
#define TRAINER_CLASS_AQUA_ADMIN          55
#define TRAINER_CLASS_MAGMA_ADMIN         56
#define TRAINER_CLASS_YOUNGSTER           57
#define TRAINER_CLASS_BUG_CATCHER         58
#define TRAINER_CLASS_LASS                59
#define TRAINER_CLASS_SAILOR              60
#define TRAINER_CLASS_CAMPER              61
#define TRAINER_CLASS_PICNICKER           62
#define TRAINER_CLASS_POKEMANIAC          63
#define TRAINER_CLASS_SUPER_NERD          64
#define TRAINER_CLASS_HIKER               65
#define TRAINER_CLASS_BIKER               66
#define TRAINER_CLASS_BURGLAR             67
#define TRAINER_CLASS_ENGINEER            68
#define TRAINER_CLASS_FISHERMAN           69
#define TRAINER_CLASS_SWIMMER_M           70
#define TRAINER_CLASS_CUE_BALL            71
#define TRAINER_CLASS_GAMER               72
#define TRAINER_CLASS_BEAUTY              73
#define TRAINER_CLASS_SWIMMER_F           74
#define TRAINER_CLASS_PSYCHIC             75
#define TRAINER_CLASS_ROCKER              76
#define TRAINER_CLASS_JUGGLER             77
#define TRAINER_CLASS_TAMER               78
#define TRAINER_CLASS_BIRD_KEEPER         79
#define TRAINER_CLASS_BLACK_BELT          80
#define TRAINER_CLASS_RIVAL_EARLY         81
#define TRAINER_CLASS_SCIENTIST           82
#define TRAINER_CLASS_BOSS                83
#define TRAINER_CLASS_LEADER              84
#define TRAINER_CLASS_TEAM_ROCKET         85
#define TRAINER_CLASS_COOLTRAINER         86
#define TRAINER_CLASS_ELITE_FOUR          87
#define TRAINER_CLASS_GENTLEMAN           88
#define TRAINER_CLASS_RIVAL_LATE          89
#define TRAINER_CLASS_CHAMPION            90
#define TRAINER_CLASS_CHANNELER           91
#define TRAINER_CLASS_TWINS               92
#define TRAINER_CLASS_COOL_COUPLE         93
#define TRAINER_CLASS_YOUNG_COUPLE        94
#define TRAINER_CLASS_CRUSH_KIN           95
#define TRAINER_CLASS_SIS_AND_BRO         96
#define TRAINER_CLASS_PKMN_PROF           97
#define TRAINER_CLASS_PLAYER              98
#define TRAINER_CLASS_CRUSH_GIRL          99
#define TRAINER_CLASS_TUBER               100
#define TRAINER_CLASS_PKMN_BREEDER        101
#define TRAINER_CLASS_PKMN_RANGER         102
#define TRAINER_CLASS_AROMA_LADY          103
#define TRAINER_CLASS_RUIN_MANIAC         104
#define TRAINER_CLASS_LADY                105
#define TRAINER_CLASS_PAINTER             106

#define FACILITY_CLASS_AQUA_LEADER_ARCHIE     0
#define FACILITY_CLASS_AQUA_GRUNT_M           1
#define FACILITY_CLASS_AQUA_GRUNT_F           2
#define FACILITY_CLASS_RS_AROMA_LADY          3
#define FACILITY_CLASS_RS_RUIN_MANIAC         4
#define FACILITY_CLASS_INTERVIEWER            5
#define FACILITY_CLASS_RS_TUBER_F             6
#define FACILITY_CLASS_RS_TUBER_M             7
#define FACILITY_CLASS_RS_COOLTRAINER_M       8
#define FACILITY_CLASS_RS_COOLTRAINER_F       9
#define FACILITY_CLASS_HEX_MANIAC             10
#define FACILITY_CLASS_RS_LADY                11
#define FACILITY_CLASS_RS_BEAUTY              12
#define FACILITY_CLASS_RICH_BOY               13
#define FACILITY_CLASS_RS_POKEMANIAC          14
#define FACILITY_CLASS_RS_SWIMMER_M           15
#define FACILITY_CLASS_RS_BLACK_BELT          16
#define FACILITY_CLASS_GUITARIST              17
#define FACILITY_CLASS_KINDLER                18
#define FACILITY_CLASS_RS_CAMPER              19
#define FACILITY_CLASS_BUG_MANIAC             20
#define FACILITY_CLASS_RS_PSYCHIC_M           21
#define FACILITY_CLASS_RS_PSYCHIC_F           22
#define FACILITY_CLASS_RS_GENTLEMAN           23
#define FACILITY_CLASS_ELITE_FOUR_SIDNEY      24
#define FACILITY_CLASS_ELITE_FOUR_PHOEBE      25
#define FACILITY_CLASS_LEADER_ROXANNE         26
#define FACILITY_CLASS_LEADER_BRAWLY          27
#define FACILITY_CLASS_LEADER_TATE_AND_LIZA   28
#define FACILITY_CLASS_SCHOOL_KID_M           29
#define FACILITY_CLASS_SCHOOL_KID_F           30
#define FACILITY_CLASS_SR_AND_JR              31
#define FACILITY_CLASS_POKEFAN_M              32
#define FACILITY_CLASS_POKEFAN_F              33
#define FACILITY_CLASS_EXPERT_M               34
#define FACILITY_CLASS_EXPERT_F               35
#define FACILITY_CLASS_RS_YOUNGSTER           36
#define FACILITY_CLASS_CHAMPION_STEVEN        37
#define FACILITY_CLASS_RS_FISHERMAN           38
#define FACILITY_CLASS_CYCLING_TRIATHLETE_M   39
#define FACILITY_CLASS_CYCLING_TRIATHLETE_F   40
#define FACILITY_CLASS_RUNNING_TRIATHLETE_M   41
#define FACILITY_CLASS_RUNNING_TRIATHLETE_F   42
#define FACILITY_CLASS_SWIMMING_TRIATHLETE_M  43
#define FACILITY_CLASS_SWIMMING_TRIATHLETE_F  44
#define FACILITY_CLASS_DRAGON_TAMER           45
#define FACILITY_CLASS_RS_BIRD_KEEPER         46
#define FACILITY_CLASS_NINJA_BOY              47
#define FACILITY_CLASS_BATTLE_GIRL            48
#define FACILITY_CLASS_PARASOL_LADY           49
#define FACILITY_CLASS_RS_SWIMMER_F           50
#define FACILITY_CLASS_RS_PICNICKER           51
#define FACILITY_CLASS_RS_TWINS               52
#define FACILITY_CLASS_RS_SAILOR              53
#define FACILITY_CLASS_BOARDER_M              54
#define FACILITY_CLASS_BOARDER_F              55
#define FACILITY_CLASS_COLLECTOR              56
#define FACILITY_CLASS_PKMN_TRAINER_WALLY     57
#define FACILITY_CLASS_PKMN_TRAINER_BRENDAN   58
#define FACILITY_CLASS_PKMN_TRAINER_BRENDAN_2 59
#define FACILITY_CLASS_PKMN_TRAINER_BRENDAN_3 60
#define FACILITY_CLASS_PKMN_TRAINER_MAY       61
#define FACILITY_CLASS_PKMN_TRAINER_MAY_2     62
#define FACILITY_CLASS_PKMN_TRAINER_MAY_3     63
#define FACILITY_CLASS_RS_PKMN_BREEDER_M      64
#define FACILITY_CLASS_RS_PKMN_BREEDER_F      65
#define FACILITY_CLASS_RS_PKMN_RANGER_M       66
#define FACILITY_CLASS_RS_PKMN_RANGER_F       67
#define FACILITY_CLASS_MAGMA_LEADER_MAXIE     68
#define FACILITY_CLASS_MAGMA_GRUNT_M          69
#define FACILITY_CLASS_MAGMA_GRUNT_F          70
#define FACILITY_CLASS_RS_LASS                71
#define FACILITY_CLASS_RS_BUG_CATCHER         72
#define FACILITY_CLASS_RS_HIKER               73
#define FACILITY_CLASS_RS_YOUNG_COUPLE        74
#define FACILITY_CLASS_OLD_COUPLE             75
#define FACILITY_CLASS_RS_SIS_AND_BRO         76
#define FACILITY_CLASS_AQUA_ADMIN_M           77
#define FACILITY_CLASS_AQUA_ADMIN_F           78
#define FACILITY_CLASS_MAGMA_ADMIN_M          79
#define FACILITY_CLASS_MAGMA_ADMIN_F          80
#define FACILITY_CLASS_LEADER_WATTSON         81
#define FACILITY_CLASS_LEADER_FLANNERY        82
#define FACILITY_CLASS_LEADER_NORMAN          83
#define FACILITY_CLASS_LEADER_WINONA          84
#define FACILITY_CLASS_LEADER_WALLACE         85
#define FACILITY_CLASS_ELITE_FOUR_GLACIA      86
#define FACILITY_CLASS_ELITE_FOUR_DRAKE       87
#define FACILITY_CLASS_YOUNGSTER              88
#define FACILITY_CLASS_BUG_CATCHER            89
#define FACILITY_CLASS_LASS                   90
#define FACILITY_CLASS_SAILOR                 91
#define FACILITY_CLASS_CAMPER                 92
#define FACILITY_CLASS_PICNICKER              93
#define FACILITY_CLASS_POKEMANIAC             94
#define FACILITY_CLASS_SUPER_NERD             95
#define FACILITY_CLASS_HIKER                  96
#define FACILITY_CLASS_BIKER                  97
#define FACILITY_CLASS_BURGLAR                98
#define FACILITY_CLASS_ENGINEER               99
#define FACILITY_CLASS_FISHERMAN              100
#define FACILITY_CLASS_SWIMMER_M              101
#define FACILITY_CLASS_CUE_BALL               102
#define FACILITY_CLASS_GAMER                  103
#define FACILITY_CLASS_BEAUTY                 104
#define FACILITY_CLASS_SWIMMER_F              105
#define FACILITY_CLASS_PSYCHIC_M              106
#define FACILITY_CLASS_ROCKER                 107
#define FACILITY_CLASS_JUGGLER                108
#define FACILITY_CLASS_TAMER                  109
#define FACILITY_CLASS_BIRD_KEEPER            110
#define FACILITY_CLASS_BLACK_BELT             111
#define FACILITY_CLASS_RIVAL_EARLY            112
#define FACILITY_CLASS_SCIENTIST              113
#define FACILITY_CLASS_BOSS                   114
#define FACILITY_CLASS_ROCKET_GRUNT_M         115
#define FACILITY_CLASS_COOLTRAINER_M          116
#define FACILITY_CLASS_COOLTRAINER_F          117
#define FACILITY_CLASS_ELITE_FOUR_LORELEI     118
#define FACILITY_CLASS_ELITE_FOUR_BRUNO       119
#define FACILITY_CLASS_LEADER_M               120
#define FACILITY_CLASS_LEADER_F               121
#define FACILITY_CLASS_GENTLEMAN              122
#define FACILITY_CLASS_RIVAL_LATE             123
#define FACILITY_CLASS_CHAMPION_RIVAL         124
#define FACILITY_CLASS_CHANNELER              125
#define FACILITY_CLASS_TWINS                  126
#define FACILITY_CLASS_COOL_COUPLE            127
#define FACILITY_CLASS_YOUNG_COUPLE           128
#define FACILITY_CLASS_CRUSH_KIN              129
#define FACILITY_CLASS_SIS_AND_BRO            130
#define FACILITY_CLASS_PKMN_PROF              131
#define FACILITY_CLASS_BRENDAN                132
#define FACILITY_CLASS_MAY                    133
#define FACILITY_CLASS_RED                    134
#define FACILITY_CLASS_LEAF                   135
#define FACILITY_CLASS_ROCKET_GRUNT_F         136
#define FACILITY_CLASS_PSYCHIC_F              137
#define FACILITY_CLASS_CRUSH_GIRL             138
#define FACILITY_CLASS_TUBER                  139
#define FACILITY_CLASS_PKMN_BREEDER           140
#define FACILITY_CLASS_PKMN_RANGER_M          141
#define FACILITY_CLASS_PKMN_RANGER_F          142
#define FACILITY_CLASS_AROMA_LADY             143
#define FACILITY_CLASS_RUIN_MANIAC            144
#define FACILITY_CLASS_LADY                   145
#define FACILITY_CLASS_PAINTER                146
#define FACILITY_CLASS_ELITE_FOUR_AGATHA      147
#define FACILITY_CLASS_ELITE_FOUR_LANCE       148
#define FACILITY_CLASS_CHAMPION_RIVAL_2       149

#define F_TRAINER_FEMALE (1 << 7)

// All trainer parties specify the IV, level, and species for each Pokémon in the
// party. Some trainer parties also specify held items and custom moves for each
// Pokémon.
#define F_TRAINER_PARTY_CUSTOM_MOVESET (1 << 0)
#define F_TRAINER_PARTY_HELD_ITEM      (1 << 1)

#endif  // GUARD_CONSTANTS_TRAINERS_H
