# PLANO DE INTEGRAÇÃO SEGURA - Sistema Bonus

## PROBLEMA IDENTIFICADO

O CFRU **INSERE** código na ROM, mas **NÃO SUBSTITUI** scripts originais.
Nossos scripts criam NOVOS eventos, mas não se conectam com os originais.

## SOLUÇÕES SEGURAS

### Opção 1: Hook <PERSON> (RECOMENDADA)

```c
// Hook na função que processa fim de batalha de treinador
void PostTrainerBattleHook(void) {
    // Sistema original JÁ executou completamente
    // Badge e TM já foram dados
    
    // Agora verificamos se é líder de ginásio
    if (IsGymLeader(gTrainerBattleOpponent_A)) {
        GiveGymLeaderBonusReward();
    }
}
```

**Arquivo:** `hooks`
```
PostTrainerBattleHook 0x[ENDEREÇO_FIM_BATALHA_TREINADOR] 0
```

### Opção 2: Hook na Função de Badge

```c
// Hook na função original que dá badges
void GiveBadgeHook(u16 badgeFlag) {
    // Chama função original primeiro
    OriginalGiveBadge(badgeFlag);
    
    // Depois dá bonus
    GiveGymLeaderBonusReward();
}
```

### Opção 3: Repoint Scripts Específicos

```
# repoints file
EventScript_BrockWithBonus 0x[ENDEREÇO_SCRIPT_BROCK_ORIGINAL]
EventScript_MistyWithBonus 0x[ENDEREÇO_SCRIPT_MISTY_ORIGINAL]
```

## IMPLEMENTAÇÃO ESCOLHIDA: Hook Pós-Batalha

### 1. Encontrar Endereço Correto

Buscar na ROM onde o jogo processa fim de batalha de treinador:
- Após `trainerbattle1` executar
- Após badges/TMs serem dados
- Antes de retornar ao overworld

### 2. Criar Hook Seguro

```c
void PostGymBattleHook(void) {
    // Só executa se:
    // 1. Batalha foi vencida
    // 2. Oponente é líder de ginásio
    // 3. Bonus ainda não foi dado
    
    if (gBattleOutcome == B_OUTCOME_WON && 
        IsGymLeader(gTrainerBattleOpponent_A) &&
        !HasReceivedGymLeaderBonus(gTrainerBattleOpponent_A)) {
        
        GiveGymLeaderBonusReward();
    }
}
```

### 3. Função de Verificação

```c
bool8 IsGymLeader(u16 trainerId) {
    for (u32 i = 0; i < ARRAY_COUNT(sGymLeaderBonusRewards); i++) {
        if (sGymLeaderBonusRewards[i].trainerId == trainerId) {
            return TRUE;
        }
    }
    return FALSE;
}
```

## GARANTIAS DE SEGURANÇA

### ✅ Sistema Original Intocado
- Hook executa APÓS sistema original
- Não modifica flags originais
- Não interfere com lógica de badge

### ✅ Execução Condicional
- Só executa para líderes de ginásio
- Só executa se bonus não foi dado
- Só executa se batalha foi vencida

### ✅ Flags Separadas
- Usa flags próprias para bonus
- Não toca flags de badge originais
- Sistema pode ser removido sem problemas

## ARQUIVOS NECESSÁRIOS

### 1. `src/gym_leader_bonus.c`
```c
#include "global.h"
// ... implementação do sistema bonus
```

### 2. `hooks`
```
PostGymBattleHook 0x[ENDEREÇO] 0
```

### 3. `include/gym_leader_bonus.h`
```c
void PostGymBattleHook(void);
bool8 IsGymLeader(u16 trainerId);
// ... outras declarações
```

## TESTE DE VERIFICAÇÃO

### Antes da Implementação
1. Derrotar Brock normalmente
2. Verificar se badge e TM são dados
3. Anotar comportamento original

### Após Implementação
1. Derrotar Brock com sistema
2. Verificar se badge e TM ainda são dados (original)
3. Verificar se item bonus é dado (novo)
4. Confirmar que flags originais não mudaram

### Teste de Remoção
1. Remover hook do sistema
2. Verificar se jogo funciona normalmente
3. Confirmar que nada foi quebrado

## CONCLUSÃO

Esta abordagem **GARANTE** que:
- Sistema original permanece 100% intacto
- Bonus é dado automaticamente após vitória
- Não há interferência com mecânicas existentes
- Sistema pode ser removido sem problemas

É a implementação mais segura possível!
