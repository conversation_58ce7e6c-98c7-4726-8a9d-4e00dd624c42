#include "defines.h"
#include "defines_battle.h"
#include "../include/gym_leader_rewards.h"
#include "../include/event_data.h"
#include "../include/item.h"
#include "../include/string_util.h"
#include "../include/text.h"
#include "../include/constants/items.h"
#include "../include/constants/flags.h"
#include "../include/constants/battle.h"
#include "../include/constants/trainer_classes.h"
#include "../include/constants/species.h"
#include "../include/new/item.h"
#include "../include/new/roamer.h"
#include "../include/script.h"

// BONUS reward texts (complementary to existing TM system)
// These are defined in strings/gym_leader_rewards.string
extern const u8 sText_BonusBrock[];
extern const u8 sText_BonusMisty[];
extern const u8 sText_BonusLtSurge[];
extern const u8 sText_BonusErika[];
extern const u8 sText_BonusKoga[];
extern const u8 sText_BonusSabrina[];
extern const u8 sText_BonusBlaine[];
extern const u8 sText_BonusGiovanni[];

// RIVAL reward texts (completely separate system)
extern const u8 sText_RivalReward1[];
extern const u8 sText_RivalReward2[];
extern const u8 sText_RivalReward3[];
extern const u8 sText_RivalReward4[];
extern const u8 sText_RivalReward5[];
extern const u8 sText_RivalReward6[];
extern const u8 sText_RivalReward7[];
extern const u8 sText_RivalReward8[];

// Post-battle message texts (defined externally)
extern const u8 gText_ReceivedMegaStoneAndZCrystal[];
extern const u8 gText_ReceivedRivalRewards[];
extern const u8 gText_MoneyAndGymRewards[];
extern const u8 gText_MoneyAndRivalRewards[];
extern const u8 gText_GotMoneyForWinning[];

// ENHANCED Gym Leader Rewards System - Based on their actual Pokemon teams
// Each gym leader gives 4 thematic items based on their Pokemon and type specialty
struct GymLeaderEnhancedReward
{
    u16 trainerId;          // ID do treinador líder
    u16 zCrystal;          // Z-Crystal relacionado ao Pokemon usado
    u16 megaStone;         // Mega Stone relacionada ao Pokemon usado
    u16 evolutionStone;    // Pedra elemental relacionada ao tipo
    u16 typeBoostItem;     // Item de hold que boostra o tipo
    u16 flagReceived;      // Flag se já recebeu as recompensas
    const u8 *rewardText;  // Texto para as recompensas
};

const struct GymLeaderEnhancedReward sGymLeaderEnhancedRewards[] = {
    // BROCK - Rock-type specialist (uses Onix, Graveler)
    {
        .trainerId = TRAINER_BROCK,
        .zCrystal = ITEM_ROCKIUM_Z,         // Rock Z-Crystal (for his Rock types)
        .megaStone = ITEM_STEELIXITE,       // Steelix Mega Stone (Onix evolution)
        .evolutionStone = ITEM_HARD_STONE,  // Rock-type boost item (no evolution stone for Rock)
        .typeBoostItem = ITEM_HARD_STONE,   // Rock-type boost item
        .flagReceived = FLAG_RECEIVED_BONUS_BROCK,
        .rewardText = sText_BonusBrock
    },
    // MISTY - Water-type specialist (uses Staryu, Starmie)
    {
        .trainerId = TRAINER_MISTY,
        .zCrystal = ITEM_WATERIUM_Z,        // Water Z-Crystal (for her Water types)
        .megaStone = ITEM_GYARADOSITE,      // Gyarados Mega Stone (Water fallback)
        .evolutionStone = ITEM_WATER_STONE, // Water Stone (evolves Staryu to Starmie)
        .typeBoostItem = ITEM_MYSTIC_WATER, // Water-type boost item
        .flagReceived = FLAG_RECEIVED_BONUS_MISTY,
        .rewardText = sText_BonusMisty
    },
    // LT. SURGE - Electric-type specialist (uses Raichu, Pikachu)
    {
        .trainerId = TRAINER_LT_SURGE,
        .zCrystal = ITEM_PIKANIUM_Z,        // Pikanium Z (for his Pikachu/Raichu)
        .megaStone = ITEM_MANECTITE,        // Manectric Mega Stone (Electric fallback)
        .evolutionStone = ITEM_THUNDER_STONE, // Thunder Stone (evolves Pikachu to Raichu)
        .typeBoostItem = ITEM_MAGNET,       // Electric-type boost item
        .flagReceived = FLAG_RECEIVED_BONUS_LT_SURGE,
        .rewardText = sText_BonusLtSurge
    },
    // ERIKA - Grass-type specialist (uses Vileplume, Tangela)
    {
        .trainerId = TRAINER_ERIKA,
        .zCrystal = ITEM_GRASSIUM_Z,        // Grass Z-Crystal (for her Grass types)
        .megaStone = ITEM_VENUSAURITE,      // Venusaur Mega Stone (Grass fallback)
        .evolutionStone = ITEM_LEAF_STONE,  // Leaf Stone (evolves Gloom to Vileplume)
        .typeBoostItem = ITEM_MIRACLE_SEED, // Grass-type boost item
        .flagReceived = FLAG_RECEIVED_BONUS_ERIKA,
        .rewardText = sText_BonusErika
    },
    // KOGA - Poison-type specialist (uses Weezing, Muk)
    {
        .trainerId = TRAINER_KOGA,
        .zCrystal = ITEM_POISONIUM_Z,       // Poison Z-Crystal (for his Poison types)
        .megaStone = ITEM_GENGARITE,        // Gengar Mega Stone (Poison fallback)
        .evolutionStone = ITEM_POISON_BARB, // No evolution stone for Poison, use boost item
        .typeBoostItem = ITEM_POISON_BARB,  // Poison-type boost item
        .flagReceived = FLAG_RECEIVED_BONUS_KOGA,
        .rewardText = sText_BonusKoga
    },
    // SABRINA - Psychic-type specialist (uses Alakazam, Kadabra)
    {
        .trainerId = TRAINER_SABRINA,
        .zCrystal = ITEM_PSYCHIUM_Z,        // Psychic Z-Crystal (for her Psychic types)
        .megaStone = ITEM_ALAKAZITE,        // Alakazam Mega Stone (her signature Pokemon)
        .evolutionStone = ITEM_TWISTED_SPOON, // No evolution stone for Psychic, use boost item
        .typeBoostItem = ITEM_TWISTED_SPOON, // Psychic-type boost item
        .flagReceived = FLAG_RECEIVED_BONUS_SABRINA,
        .rewardText = sText_BonusSabrina
    },
    // BLAINE - Fire-type specialist (uses Arcanine, Rapidash)
    {
        .trainerId = TRAINER_BLAINE,
        .zCrystal = ITEM_FIRIUM_Z,          // Fire Z-Crystal (for his Fire types)
        .megaStone = ITEM_CHARIZARDITE_Y,   // Charizard Y Mega Stone (Fire fallback)
        .evolutionStone = ITEM_FIRE_STONE,  // Fire Stone (evolves Growlithe to Arcanine)
        .typeBoostItem = ITEM_CHARCOAL,     // Fire-type boost item
        .flagReceived = FLAG_RECEIVED_BONUS_BLAINE,
        .rewardText = sText_BonusBlaine
    },
    // GIOVANNI - Ground-type specialist (uses Nidoking, Rhyhorn)
    {
        .trainerId = TRAINER_GIOVANNI,
        .zCrystal = ITEM_GROUNDIUM_Z,       // Ground Z-Crystal (for his Ground types)
        .megaStone = ITEM_KANGASKHANITE,    // Kangaskhan Mega Stone (Normal fallback)
        .evolutionStone = ITEM_MOON_STONE,  // Moon Stone (evolves Nidorino to Nidoking)
        .typeBoostItem = ITEM_SOFT_SAND,    // Ground-type boost item
        .flagReceived = FLAG_RECEIVED_BONUS_GIOVANNI,
        .rewardText = sText_BonusGiovanni
    }
};

// Legacy table for compatibility (kept for reference)
const struct GymLeaderBonusReward sGymLeaderBonusRewards[] = {
    // trainerId, bonusItem, quantity, flagReceived, rewardText
    {TRAINER_BROCK,    ITEM_RAZZ_BERRY,    5, FLAG_RECEIVED_BONUS_BROCK,    sText_BonusBrock},
    {TRAINER_MISTY,    ITEM_MYSTIC_WATER,  1, FLAG_RECEIVED_BONUS_MISTY,    sText_BonusMisty},
    {TRAINER_LT_SURGE, ITEM_Z_POWER_RING,  1, FLAG_RECEIVED_BONUS_LT_SURGE, sText_BonusLtSurge},
    {TRAINER_ERIKA,    ITEM_MIRACLE_SEED,  3, FLAG_RECEIVED_BONUS_ERIKA,    sText_BonusErika},
    {TRAINER_KOGA,     ITEM_POISON_BARB,   1, FLAG_RECEIVED_BONUS_KOGA,     sText_BonusKoga},
    {TRAINER_SABRINA,  ITEM_TWISTED_SPOON, 1, FLAG_RECEIVED_BONUS_SABRINA,  sText_BonusSabrina},
    {TRAINER_BLAINE,   ITEM_CHARCOAL,      1, FLAG_RECEIVED_BONUS_BLAINE,   sText_BonusBlaine},
    {TRAINER_GIOVANNI, ITEM_SOFT_SAND,     1, FLAG_RECEIVED_BONUS_GIOVANNI, sText_BonusGiovanni},
};

const u8 sGymLeaderBonusRewardsCount = ARRAY_COUNT(sGymLeaderBonusRewards);
const u8 sGymLeaderEnhancedRewardsCount = ARRAY_COUNT(sGymLeaderEnhancedRewards);

// RIVAL Rewards table (COMPLETELY SEPARATE from gym leaders)
// Each rival battle gives a unique Mega Stone + Z-Crystal combination
// Uses DIFFERENT flags to ensure NO interference with existing systems
struct RivalReward
{
    u16 rivalBattleMin;     // Minimum trainer ID for this rival battle
    u16 rivalBattleMax;     // Maximum trainer ID for this rival battle
    u16 megaStone;          // Mega Stone reward
    u16 zCrystal;           // Z-Crystal reward
    u16 flagReceived;       // SEPARATE flag system for rivals
    const u8* rewardText;   // Text shown when giving reward
};

const struct RivalReward sRivalRewards[] = {
    // RIVAL1 - Oak's Lab (IDs 0x146-0x148)
    {
        .rivalBattleMin = 0x146,
        .rivalBattleMax = 0x148,
        .megaStone = ITEM_VENUSAURITE,
        .zCrystal = ITEM_NORMALIUM_Z,
        .flagReceived = FLAG_RECEIVED_RIVAL_REWARD_1,
        .rewardText = sText_RivalReward1
    },
    // RIVAL2 - Route 22 weak (IDs 0x149-0x14B)
    {
        .rivalBattleMin = 0x149,
        .rivalBattleMax = 0x14B,
        .megaStone = ITEM_CHARIZARDITE_X,
        .zCrystal = ITEM_FIGHTINIUM_Z,
        .flagReceived = FLAG_RECEIVED_RIVAL_REWARD_2,
        .rewardText = sText_RivalReward2
    },
    // RIVAL3 - Cerulean (IDs 0x14C-0x14E)
    {
        .rivalBattleMin = 0x14C,
        .rivalBattleMax = 0x14E,
        .megaStone = ITEM_BLASTOISINITE,
        .zCrystal = ITEM_FLYINIUM_Z,
        .flagReceived = FLAG_RECEIVED_RIVAL_REWARD_3,
        .rewardText = sText_RivalReward3
    },
    // RIVAL4 - SS Anne (CORRECTED IDs from .example)
    {
        .rivalBattleMin = 0x1AA,  // 426
        .rivalBattleMax = 0x1AC,  // 428
        .megaStone = ITEM_ALAKAZITE,
        .zCrystal = ITEM_POISONIUM_Z,
        .flagReceived = FLAG_RECEIVED_RIVAL_REWARD_4,
        .rewardText = sText_RivalReward4
    },
    // RIVAL5 - Pokemon Tower (CORRECTED IDs from .example)
    {
        .rivalBattleMin = 0x1AD,  // 429
        .rivalBattleMax = 0x1AF,  // 431
        .megaStone = ITEM_GENGARITE,
        .zCrystal = ITEM_GROUNDIUM_Z,
        .flagReceived = FLAG_RECEIVED_RIVAL_REWARD_5,
        .rewardText = sText_RivalReward5
    },
    // RIVAL6 - Silph Co (CORRECTED IDs from .example)
    {
        .rivalBattleMin = 0x1B0,  // 432
        .rivalBattleMax = 0x1B2,  // 434
        .megaStone = ITEM_GYARADOSITE,
        .zCrystal = ITEM_ROCKIUM_Z,
        .flagReceived = FLAG_RECEIVED_RIVAL_REWARD_6,
        .rewardText = sText_RivalReward6
    },
    // RIVAL7 - Route 22 strong (CORRECTED IDs from .example)
    {
        .rivalBattleMin = 0x1B3,  // 435
        .rivalBattleMax = 0x1B5,  // 437
        .megaStone = ITEM_AERODACTYLITE,
        .zCrystal = ITEM_BUGINIUM_Z,
        .flagReceived = FLAG_RECEIVED_RIVAL_REWARD_7,
        .rewardText = sText_RivalReward7
    },
    // RIVAL8 - Champion (CORRECTED IDs from .example)
    {
        .rivalBattleMin = 0x1B6,  // 438
        .rivalBattleMax = 0x1B8,  // 440
        .megaStone = ITEM_MEWTWONITE_X,
        .zCrystal = ITEM_DRAGONIUM_Z,
        .flagReceived = FLAG_RECEIVED_RIVAL_REWARD_8,
        .rewardText = sText_RivalReward8
    }
};

const u8 sRivalRewardsCount = ARRAY_COUNT(sRivalRewards);

// ROAMER ACTIVATION SYSTEM - Simple implementation for badge progression
// Activates specific roamers when gym leaders are defeated

// Structure to map badges to roamers
struct BadgeRoamerMapping
{
    u16 badgeFlag;      // Badge flag to check
    u16 species;        // Pokemon species to activate
    u8 level;           // Level of the roamer
    u16 activatedFlag;  // Flag to track if this roamer was already activated
};

// Mapping table for badge -> roamer activation
static const struct BadgeRoamerMapping sBadgeRoamerMappings[] = {
    {FLAG_BADGE01_GET, SPECIES_RAIKOU,   40, FLAG_ROAMER_BADGE1_ACTIVATED},
    {FLAG_BADGE02_GET, SPECIES_ENTEI,    42, FLAG_ROAMER_BADGE2_ACTIVATED},
    {FLAG_BADGE03_GET, SPECIES_SUICUNE,  44, FLAG_ROAMER_BADGE3_ACTIVATED},
    {FLAG_BADGE04_GET, SPECIES_ARTICUNO, 46, FLAG_ROAMER_BADGE4_ACTIVATED},
    {FLAG_BADGE05_GET, SPECIES_ZAPDOS,   48, FLAG_ROAMER_BADGE5_ACTIVATED},
    {FLAG_BADGE06_GET, SPECIES_MOLTRES,  50, FLAG_ROAMER_BADGE6_ACTIVATED},
    {FLAG_BADGE07_GET, SPECIES_MEWTWO,   60, FLAG_ROAMER_BADGE7_ACTIVATED},
    {FLAG_BADGE08_GET, SPECIES_MEW,      50, FLAG_ROAMER_BADGE8_ACTIVATED}
};

// Function to activate a single roamer
static void ActivateSingleRoamer(u16 species, u8 level)
{
    // Check if this species is already roaming
    if (IsSpeciesRoaming(species))
        return; // Already active, skip

    // Use the existing roamer system
    Var8000 = species;
    Var8001 = level;
    Var8002 = 1; // Can appear on land
    Var8003 = 1; // Can appear on water

    // Call the roamer initialization function
    sp129_InitRoamer();
}

// Function to check and activate roamers for existing badges (RETROACTIVE)
void CheckAndActivateRoamersForExistingBadges(void)
{
    // Check each badge and activate corresponding roamer if not already done
    for (u8 i = 0; i < ARRAY_COUNT(sBadgeRoamerMappings); i++)
    {
        const struct BadgeRoamerMapping* mapping = &sBadgeRoamerMappings[i];

        // If player has the badge but roamer wasn't activated yet
        if (FlagGet(mapping->badgeFlag) && !FlagGet(mapping->activatedFlag))
        {
            ActivateSingleRoamer(mapping->species, mapping->level);
            FlagSet(mapping->activatedFlag); // Mark as activated
        }
    }
}

// Function called when gym leader is defeated (NEW BADGE OBTAINED)
void ActivateRoamerForBadge(u16 trainerId)
{
    // Define which roamer to activate for each gym leader
    u16 species = SPECIES_NONE;
    u8 level = 50;
    u16 activatedFlag = 0;

    switch (trainerId)
    {
        case TRAINER_BROCK:
            species = SPECIES_RAIKOU;
            level = 40;
            activatedFlag = FLAG_ROAMER_BADGE1_ACTIVATED;
            break;
        case TRAINER_MISTY:
            species = SPECIES_ENTEI;
            level = 42;
            activatedFlag = FLAG_ROAMER_BADGE2_ACTIVATED;
            break;
        case TRAINER_LT_SURGE:
            species = SPECIES_SUICUNE;
            level = 44;
            activatedFlag = FLAG_ROAMER_BADGE3_ACTIVATED;
            break;
        case TRAINER_ERIKA:
            species = SPECIES_ARTICUNO;
            level = 46;
            activatedFlag = FLAG_ROAMER_BADGE4_ACTIVATED;
            break;
        case TRAINER_KOGA:
            species = SPECIES_ZAPDOS;
            level = 48;
            activatedFlag = FLAG_ROAMER_BADGE5_ACTIVATED;
            break;
        case TRAINER_SABRINA:
            species = SPECIES_MOLTRES;
            level = 50;
            activatedFlag = FLAG_ROAMER_BADGE6_ACTIVATED;
            break;
        case TRAINER_BLAINE:
            species = SPECIES_MEWTWO;
            level = 60;
            activatedFlag = FLAG_ROAMER_BADGE7_ACTIVATED;
            break;
        case TRAINER_GIOVANNI:
            species = SPECIES_MEW;
            level = 50;
            activatedFlag = FLAG_ROAMER_BADGE8_ACTIVATED;
            break;
        default:
            return; // Not a gym leader or unknown trainer
    }

    // Activate the roamer if not already done
    if (species != SPECIES_NONE && !FlagGet(activatedFlag))
    {
        ActivateSingleRoamer(species, level);
        FlagSet(activatedFlag); // Mark as activated

        // Note: No message shown to avoid interrupting gym leader dialogue
        // Players will discover roamers naturally during gameplay
    }
}

// Hook function called when trainer battle is won (CORRECT INTEGRATION)
// Called from HandleEndTurn_BattleWon in end_battle.c
void PostTrainerBattleHook_C(void)
{
    u16 trainerId = gTrainerBattleOpponent_A;

    // ROAMER RETROACTIVE CHECK: Check for existing badges on first run
    // This ensures players with existing saves get their roamers activated
    static bool8 hasCheckedExistingBadges = FALSE;
    if (!hasCheckedExistingBadges)
    {
        CheckAndActivateRoamersForExistingBadges();
        hasCheckedExistingBadges = TRUE;
    }

    // Check gym leaders (ENHANCED system with Mega Stones + Z-Crystals)
    if (IsGymLeader(trainerId) &&
        !HasReceivedGymLeaderBonus(trainerId))
    {
        GiveGymLeaderMegaReward(); // NEW: Give Mega Stone + Z-Crystal

        // ROAMER ACTIVATION: Activate roamer when gym leader is defeated
        ActivateRoamerForBadge(trainerId);
    }

    // Check rivals (SEPARATE system)
    if (IsRivalTrainer(trainerId) &&
        !HasReceivedRivalReward(trainerId))
    {
        GiveRivalReward();
    }
}

// Function to give BONUS reward to player after gym leader battle
// This is COMPLEMENTARY to the existing TM/badge system
void GiveGymLeaderBonusReward(void)
{
    u16 trainerId = gTrainerBattleOpponent_A;

    for (u32 i = 0; i < ARRAY_COUNT(sGymLeaderBonusRewards); i++)
    {
        if (sGymLeaderBonusRewards[i].trainerId == trainerId)
        {
            // Check if player hasn't received this bonus yet
            if (!FlagGet(sGymLeaderBonusRewards[i].flagReceived))
            {
                // Special case for Lt. Surge - give multiple Z-Power items + Thunder Stone
                if (trainerId == TRAINER_LT_SURGE)
                {
                    AddBagItem(ITEM_Z_POWER_RING, 1);    // Z-Power Ring
                    AddBagItem(ITEM_PIKANIUM_Z, 1);      // Pikanium Z
                    AddBagItem(ITEM_ELECTRIUM_Z, 1);     // Electrium Z
                    AddBagItem(ITEM_THUNDER_STONE, 1);   // Thunder Stone
                }
                else
                {
                    // Give the standard bonus item
                    AddBagItem(sGymLeaderBonusRewards[i].bonusItem, sGymLeaderBonusRewards[i].quantity);
                }

                // Set flag to prevent duplicate bonus
                FlagSet(sGymLeaderBonusRewards[i].flagReceived);

                // Buffer item name for text display
                CopyItemName(sGymLeaderBonusRewards[i].bonusItem, gStringVar1);
            }
            break;
        }
    }
}

// Function to check if trainer is a gym leader (based on .example analysis)
bool8 IsGymLeader(u16 trainerId)
{
    // Check in the ENHANCED Rewards system (primary)
    for (u8 i = 0; i < sGymLeaderEnhancedRewardsCount; i++)
    {
        if (sGymLeaderEnhancedRewards[i].trainerId == trainerId)
        {
            return TRUE;
        }
    }
    return FALSE;
}

// Function to check if trainer is a rival (based on .example analysis)
bool8 IsRivalTrainer(u16 trainerId)
{
    // Check by trainer class (most reliable method)
    u8 trainerClass = gTrainers[trainerId].trainerClass;
    if (trainerClass == CLASS_RIVAL_2 || trainerClass == CLASS_CHAMPION)
    {
        return TRUE;
    }

    // Check by specific trainer IDs (Fire Red/Leaf Green pattern)
    // Based on REAL .example Gen3Constants.java rival mapping (CORRECTED)

    // RIVAL1 battles (Oak's Lab) - REAL IDs from .example
    if (trainerId >= 0x146 && trainerId <= 0x148)
        return TRUE;

    // RIVAL2 battles (Route 22 weak) - REAL IDs from .example
    if (trainerId >= 0x149 && trainerId <= 0x14B)
        return TRUE;

    // RIVAL3 battles (Cerulean) - REAL IDs from .example
    if (trainerId >= 0x14C && trainerId <= 0x14E)
        return TRUE;

    // RIVAL4 battles (SS Anne) - CORRECTED IDs from .example
    if (trainerId >= 0x1AA && trainerId <= 0x1AC)  // 426-428
        return TRUE;

    // RIVAL5 battles (Pokemon Tower) - CORRECTED IDs from .example
    if (trainerId >= 0x1AD && trainerId <= 0x1AF)  // 429-431
        return TRUE;

    // RIVAL6 battles (Silph Co) - CORRECTED IDs from .example
    if (trainerId >= 0x1B0 && trainerId <= 0x1B2)  // 432-434
        return TRUE;

    // RIVAL7 battles (Route 22 strong) - CORRECTED IDs from .example
    if (trainerId >= 0x1B3 && trainerId <= 0x1B5)  // 435-437
        return TRUE;

    // RIVAL8 battles (Champion) - CORRECTED IDs from .example
    if (trainerId >= 0x1B6 && trainerId <= 0x1B8)  // 438-440
        return TRUE;

    return FALSE;
}

// Function to check if trainer is a specific rival battle
bool8 IsSpecificRivalBattle(u16 trainerId, u8 rivalIndex)
{
    if (rivalIndex >= sRivalRewardsCount)
        return FALSE;

    const struct RivalReward* rival = &sRivalRewards[rivalIndex];
    return (trainerId >= rival->rivalBattleMin && trainerId <= rival->rivalBattleMax);
}

// Function to get rival battle index from trainer ID
u8 GetRivalBattleIndex(u16 trainerId)
{
    for (u8 i = 0; i < sRivalRewardsCount; i++)
    {
        if (IsSpecificRivalBattle(trainerId, i))
            return i;
    }
    return 0xFF; // Not found
}

// Function to check if rival reward was already received
bool8 HasReceivedRivalReward(u16 trainerId)
{
    u8 rivalIndex = GetRivalBattleIndex(trainerId);
    if (rivalIndex == 0xFF)
        return FALSE;

    return FlagGet(sRivalRewards[rivalIndex].flagReceived);
}

// Function to give rival rewards (COMPLETELY SEPARATE from gym system)
void GiveRivalReward(void)
{
    u16 trainerId = gTrainerBattleOpponent_A;
    u8 rivalIndex = GetRivalBattleIndex(trainerId);

    if (rivalIndex == 0xFF)
        return; // Not a rival battle

    const struct RivalReward* rival = &sRivalRewards[rivalIndex];

    // Check if already received (SAFETY CHECK)
    if (FlagGet(rival->flagReceived))
        return; // Already received, don't duplicate

    // Give Mega Stone
    AddBagItem(rival->megaStone, 1);

    // Give Z-Crystal
    AddBagItem(rival->zCrystal, 1);

    // Set flag to prevent duplicate rewards
    FlagSet(rival->flagReceived);

    // Note: Message will be shown in money display during battle
}

// Function to give gym leader ENHANCED rewards (4 thematic items)
void GiveGymLeaderMegaReward(void)
{
    u16 trainerId = gTrainerBattleOpponent_A;

    // Find the gym leader in the enhanced rewards table
    for (u8 i = 0; i < sGymLeaderEnhancedRewardsCount; i++)
    {
        const struct GymLeaderEnhancedReward* gymLeader = &sGymLeaderEnhancedRewards[i];

        if (gymLeader->trainerId == trainerId)
        {
            // Check if already received (SAFETY CHECK)
            if (FlagGet(gymLeader->flagReceived))
                return; // Already received, don't duplicate

            // Give Z-Crystal (related to Pokemon used)
            AddBagItem(gymLeader->zCrystal, 1);

            // Give Mega Stone (related to Pokemon used)
            AddBagItem(gymLeader->megaStone, 1);

            // Give Evolution Stone (related to gym type)
            AddBagItem(gymLeader->evolutionStone, 1);

            // Give Type Boost Item (related to gym type)
            AddBagItem(gymLeader->typeBoostItem, 1);

            // Set flag to prevent duplicate rewards
            FlagSet(gymLeader->flagReceived);

            // Note: Message will be shown in money display during battle

            return;
        }
    }
}

// Function to buffer gym leader bonus item name for text display
void BufferGymLeaderBonusItemName(void)
{
    u16 trainerId = gTrainerBattleOpponent_A;

    for (u32 i = 0; i < ARRAY_COUNT(sGymLeaderBonusRewards); i++)
    {
        if (sGymLeaderBonusRewards[i].trainerId == trainerId)
        {
            CopyItemName(sGymLeaderBonusRewards[i].bonusItem, gStringVar1);
            break;
        }
    }
}

// Function to check if player has received bonus from specific gym leader
bool8 HasReceivedGymLeaderBonus(u16 trainerId)
{
    // Check in the ENHANCED Rewards system (primary)
    for (u8 i = 0; i < sGymLeaderEnhancedRewardsCount; i++)
    {
        if (sGymLeaderEnhancedRewards[i].trainerId == trainerId)
        {
            return FlagGet(sGymLeaderEnhancedRewards[i].flagReceived);
        }
    }
    return FALSE;
}

// Function to get bonus item ID for specific gym leader
u16 GetGymLeaderBonusItem(u16 trainerId)
{
    for (u32 i = 0; i < ARRAY_COUNT(sGymLeaderBonusRewards); i++)
    {
        if (sGymLeaderBonusRewards[i].trainerId == trainerId)
        {
            return sGymLeaderBonusRewards[i].bonusItem;
        }
    }
    return ITEM_NONE;
}

// Function to modify money message to include reward information
void ModifyMoneyMessageForRewards(void)
{
    u16 trainerId = gTrainerBattleOpponent_A;

    // Check if this trainer gives special rewards
    if (IsGymLeader(trainerId) && !HasReceivedGymLeaderBonus(trainerId))
    {
        // Modify money message to include gym leader rewards
        StringCopy(gStringVar4, gText_MoneyAndGymRewards);
        return;
    }

    if (IsRivalTrainer(trainerId) && !HasReceivedRivalReward(trainerId))
    {
        // Modify money message to include rival rewards
        StringCopy(gStringVar4, gText_MoneyAndRivalRewards);
        return;
    }

    // Default money message (no special rewards)
    StringCopy(gStringVar4, gText_GotMoneyForWinning);
}
