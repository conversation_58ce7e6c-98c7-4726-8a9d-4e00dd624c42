# Welcome to Complete Fire Red Upgrade Expansion Version!
This fork is made by Tha Code Mining hub to add many corresponding decomps , other fork and our own additions to CFRU , everything here can be used with credits to respective code makers.
##### This is CFRU expansion project and not affiliated to the og CFRU, it is going to be levels higher than CFRU. It is adviced to read the wiki [here](https://github.com/Shiny-Miner/CFRU-expansion/wiki) before touching anything.
#### The new features (after CFRU dev branch):
- Fixed bugs with dev branch (shiny odds , daycare nidoran breeding)
- All known bugs fixed (Update April 2025!)
- Last used pokeball by L button
- Shows enemy team by L button
- Auto trigger wild battles
- Continue screen icons
- Unbound Mining system!
- Tm case icons (graphic modificable)
- Timebox in menu
- Skipped Gengar and nidoran cutscene
- Ev-Iv screen (BW) , can be called by callasm
- Battle backgrounds with CFRU based on DNS!
- Switch Pokémon in Party Menu Screen pressing Select
- Repeat item usage
- All Gen 9 moves and abilities 
- Nickname by Party menu
- Item expansion and some gen 9 items! No need to use leon rombase.
- Dexnav Swsh
- Item storage swsh + changable background
- transparent showpokepic
- Removed Extra save confirmation (the overwrite warning)
- Added new egg hatch animation
- Wonder Trades
- Mini textbox/namebox
- Use HMs in field
- Updated Z moves,Mega evo and dynamax/gmax trigger icons
- Second page in options menu
- Following Pokemon
- Nuzlocke mode
- Fully costumized debug menu to give Pokemon and items customly (Girl in Oak's lab),
- Premium Quantity Rewards (with custom table)
- New item:
-- EV-IV machine
-- PokeVial
- And much more!

##### Introduction to the maintainers
This fork is made by Tha Code Mining hub to add many corresponding decomps , other fork and our own additions to CFRU , everything here can be used with credits to respective code makers.
- The shiny's team (actually not a team but collaborators)
(FORK, NOT ORIGINAL MAKERS OF CFRU OBVIOUSLY)
- Shiny hunter/Miner,
- ansh860, (Fixes, Tm case icons, continue screen icons, more)
- Zake, (Battle backgrounds help and Following mon code)
- grilokapu (item expansion, gen 9 moves+abilities, nuzlocke fainting, more)
- 1RWT16KU1D (Tera in works)
##### For queries and support :
Join us on discord: https://discord.com/invite/axNX3rhR9K
##### Useful and Optional Additions:
- [Main Menu HGSS](https://github.com/Shiny-Miner/C-injections-FR/tree/Main-Menu-HGSS)
- [Custom EV-IV screen](https://github.com/Acimut/Custom-EV-IV-Display-Screen)
- [Main Menu BW](https://github.com/Shiny-Miner/C-injections-FR/tree/Main-Menu-BW)
- [New version of BW summary screen](https://github.com/Shiny-Miner/New-BW-summary-screen)
- [DPE gen 9](https://github.com/Shiny-Miner/Dynamic-Pokemon-Expansion-Gen-9) (obviously)
- [Dynamic surf overworlds expansion](https://github.com/Shiny-Miner/dynamic_surf_ows_masters-expansion/tree/master)
- [Option menu BW](https://github.com/Shiny-Miner/FR-OptionMenu)
- [Modified Naming screen BW](https://github.com/Shiny-Miner/Naming-screen-BW)
- Some music patches said in leon rombase + others...
###### Notes :
- It is advised to use [DPE-gen-9](https://github.com/Shiny-Miner/Dynamic-Pokemon-Expansion-Gen-9) with it.
- If you want to see how potential this expansion would have in future read [this](https://github.com/Shiny-Miner/CFRU-expansion/blob/Experiments/todo.md)
- Please don't try to use leon rombase on it as it already contains items + more
- Added convienience for compiling, now run a_makepy.bat to make the project (it executes "python scripts//make.py") and run b_cleanpy.bat to clean repo (it runs python scripts//clean.py)
- Do not talk to Fat guy and Lady in pallet town unless if you're trying to test following pokemon code.
