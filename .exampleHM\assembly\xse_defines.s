.thumb

.equ ShowTrainerIntroSpeech, 0x34
.equ ShowTrainerCantBattleSpeech, 0x35
.equ GetTrainerFlag, 0x36
.equ EndTrainerApproach, 0x37
.equ SetUpTrainerEncounterMusic, 0x38
.equ IsTrainerReadyForRematch, 0x3A
.equ HasEnoughMonsForDoubleBattle, 0x3D
.equ CAMERA_START, 0x113
.equ CAMERA_END, 0x114
.equ SetUpTrainerMovement, 0x13A

.equ CAMERA, 0x7F
.equ FOLLOWER, 0xFE
.equ PLAYER, 0xFF
.equ PLAYERFACING, 0x800C
.equ LASTRESULT, 0x800D
.equ CHOSEN_ITEM, 0x800E
.equ LASTTALKED, 0x800F

.equ look_down, 0x0
.equ look_up, 0x1
.equ look_left, 0x2
.equ look_right, 0x3

.equ walk_down_very_slow, 0x8
.equ walk_up_very_slow, 0x9
.equ walk_left_very_slow, 0xA 
.equ walk_right_very_slow, 0xB
.equ walk_down_slow, 0xC
.equ walk_up_slow, 0xD
.equ walk_left_slow, 0xE 
.equ walk_right_slow, 0xF
.equ walk_down, 0x10
.equ walk_up, 0x11
.equ walk_left, 0x12
.equ walk_right, 0x13
.equ jump_2_down, 0x14
.equ jump_2_up, 0x15
.equ jump_2_left, 0x16
.equ jump_2_right, 0x17
.equ pause_xshort, 0x18
.equ pause_vshort, 0x19
.equ pause_short, 0x1A
.equ pause_long, 0x1C
.equ run_down, 0x1D
.equ run_up, 0x1E
.equ run_left, 0x1F
.equ run_right, 0x20
.equ walk_down_onspot, 0x21
.equ walk_up_onspot, 0x22
.equ walk_left_onspot, 0x23
.equ walk_right_onspot, 0x24
.equ walk_down_onspot_fast, 0x25
.equ walk_up_onspot_fast, 0x26
.equ walk_left_onspot_fast, 0x27
.equ walk_right_onspot_fast, 0x28
.equ walk_down_onspot_vfast, 0x29
.equ walk_up_onspot_vfast, 0x2A
.equ walk_left_onspot_vfast, 0x2B
.equ walk_right_onspot_vfast, 0x2C
.equ walk_down_onspot_fastest, 0x2D
.equ walk_up_onspot_fastest, 0x2E
.equ walk_left_onspot_fastest, 0x2F
.equ walk_right_onspot_fastest, 0x30
.equ slide_down_slow, 0x31
.equ slide_up_slow, 0x32
.equ slide_left_slow, 0x33
.equ slide_right_slow, 0x34
.equ slide_down, 0x35
.equ slide_up, 0x36
.equ slide_left, 0x37
.equ slide_right, 0x38
.equ slide_down_fast, 0x39
.equ slide_up_fast, 0x3A
.equ slide_left_fast, 0x3B
.equ slide_right_fast, 0x3C
.equ player_run_down, 0x3D
.equ player_run_up, 0x3E
.equ player_run_left, 0x3F
.equ player_run_right, 0x40
.equ player_run_down_slow, 0x41
.equ player_run_up_slow, 0x42
.equ player_run_left_slow, 0x43
.equ player_run_right_slow, 0x44
.equ start_anim_in_direction, 0x45
.equ jump_special_down, 0x46
.equ jump_special_up, 0x47
.equ jump_special_left, 0x48
.equ jump_special_right, 0x49
.equ face_player, 0x4A
.equ face_away_player, 0x4B
.equ lock_facing, 0x4C
.equ unlock_facing, 0x4D
.equ jump_down, 0x4E
.equ jump_up, 0x4F
.equ jump_left, 0x50
.equ jump_right, 0x51
.equ jump_onspot_down, 0x52
.equ jump_onspot_up, 0x53
.equ jump_onspot_left, 0x54
.equ jump_onspot_right, 0x55
.equ jump_onspot_down_up, 0x56
.equ jump_onspot_up_down, 0x57
.equ jump_onspot_left_right, 0x58
.equ jump_onspot_right_left, 0x59
.equ face_default, 0x5A
.equ nurse_bow, 0x5B
.equ enable_jump_landing_ground_effect, 0x5C
.equ disable_jump_landing_ground_effect, 0x5D
.equ disable_anim, 0x5E
.equ enable_anim, 0x5F
.equ set_invisible, 0x60
.equ set_visible, 0x61
.equ exclaim, 0x62
.equ say_question, 0x63
.equ say_cross, 0x64
.equ say_double_exclaim, 0x65
.equ say_smile, 0x66
.equ reveal_trainer, 0x67
.equ smash_rock, 0x68
.equ cut_tree, 0x69
.equ set_fixed_priority, 0x6A
.equ clear_fixed_priority, 0x6B
.equ init_affine_anim, 0x6C
.equ clear_affine_anim, 0x6D
.equ end_m, 0xFE

.equ EventScript_RevealTrainer, 0x81A4FBA
.equ EventScript_NoDoubleTrainerBattle, 0x81A4F20
.equ EventScript_NotEnoughMonsForDoubleBattle, 0x81A4F19

.equ VAR_8006_LOC, 0x20370C4
.equ SELECTED_EVENT_OBJECT, 0x3005074

.equ BLUE, 0x0
.equ RED, 0x1
.equ BLACK, 0x3

.equ NO, 0x0
.equ YES, 0x1

.equ FALSE, 0x0
.equ TRUE, 0x1

.equ NOT_SET, 0x0
.equ SET, 0x1

.equ BOY, 0x0
.equ GIRL, 0x1

.equ DOWN, 0x1
.equ UP, 0x2
.equ LEFT, 0x3
.equ RIGHT, 0x4

.equ PARTY_SIZE, 0x6
.equ MAX_MON_MOVES, 0x4
.equ NUM_STATS, 0x6
.equ NUM_NATURES, 25

.equ FADEIN_BLACK, 0x0
.equ FADEOUT_BLACK, 0x1
.equ FADEIN_WHITE, 0x2
.equ FADEOUT_WHITE, 0x3

.equ ENDMART, 0x0

.equ TWO_MULTICHOICE_OPTIONS, 0x20
.equ THREE_MULTICHOICE_OPTIONS, 0x21
.equ FOUR_MULTICHOICE_OPTIONS, 0x22
.equ FIVE_MULTICHOICE_OPTIONS, 0x23
.equ SIX_MULTICHOICE_OPTIONS, 0x24
.equ SEVEN_MULTICHOICE_OPTIONS, 0x25

.equ SCROLL_MULTICHOICE_NUM, 0x8000
.equ SCROLL_MULTICHOICE_HEIGHT, 0x8001

.equ TYPE_NORMAL, 0x0
.equ TYPE_FIGHTING, 0x1
.equ TYPE_FLYING, 0x2
.equ TYPE_POISON, 0x3
.equ TYPE_GROUND, 0x4
.equ TYPE_ROCK, 0x5
.equ TYPE_BUG, 0x6
.equ TYPE_GHOST, 0x7
.equ TYPE_STEEL, 0x8
.equ TYPE_TYPELESS, 0x9
.equ TYPE_FIRE, 0xA
.equ TYPE_WATER, 0xB
.equ TYPE_GRASS, 0xC
.equ TYPE_ELECTRIC, 0xD
.equ TYPE_PSYCHIC, 0xE
.equ TYPE_ICE, 0xF
.equ TYPE_DRAGON, 0x10
.equ TYPE_DARK, 0x11
.equ TYPE_ROOSTLESS, 0x13
.equ TYPE_FAIRY, 0x17

.equ NUM_HIDDEN_POWER_TYPES, 16

.equ DELAY_HALFSECOND, 0x20
.equ DELAY_1SECOND, 0x40

.equ SUNDAY, 0x0
.equ MONDAY, 0x1
.equ TUESDAY, 0x2
.equ WEDNESDAY, 0x3
.equ THURSDAY, 0x4
.equ FRIDAY, 0x5
.equ SATURDAY, 0x6

.equ MAP_SCRIPT_TERMIN, 0
.equ MAP_SCRIPT_ON_LOAD, 1						@Setmaptile
.equ MAP_SCRIPT_ON_FRAME_TABLE, 2				@Level Script
.equ MAP_SCRIPT_ON_TRANSITION, 3				@On entering map/not on menu close
.equ MAP_SCRIPT_ON_WARP_INTO_MAP_TABLE, 4		@Level Script 2 (eg. face up on enter map)
.equ MAP_SCRIPT_ON_RESUME, 5					@On entering map/on menu close
.equ MAP_SCRIPT_ON_DIVE_WARP, 6
.equ MAP_SCRIPT_ON_RETURN_TO_FIELD, 7
.equ LEVEL_SCRIPT_TERMIN, 0

@Specials
.equ HealPlayerParty, 0x0
.equ SetCableClubWarp, 0x1
.equ DoCableClubWarp, 0x2
.equ ReturnFromLinkRoom, 0x3
.equ CleanupLinkRoomState, 0x4
.equ ExitLinkRoom, 0x5
.equ NullFieldSpecial, 0x6
.equ NullFieldSpecial, 0x7
.equ NullFieldSpecial, 0x8
.equ NullFieldSpecial, 0x9
.equ NullFieldSpecial, 0xA
.equ NullFieldSpecial, 0xB
.equ NullFieldSpecial, 0xC
.equ NullFieldSpecial, 0xD
.equ NullFieldSpecial, 0xE
.equ NullFieldSpecial, 0xF
.equ NullFieldSpecial, 0x10
.equ NullFieldSpecial, 0x11
.equ NullFieldSpecial, 0x12
.equ NullFieldSpecial, 0x13
.equ NullFieldSpecial, 0x14
.equ NullFieldSpecial, 0x15
.equ NullFieldSpecial, 0x16
.equ NullFieldSpecial, 0x17
.equ NullFieldSpecial, 0x18
.equ NullFieldSpecial, 0x19
.equ NullFieldSpecial, 0x1A
.equ NullFieldSpecial, 0x1B
.equ TryBattleLinkup, 0x1C
.equ TryTradeLinkup, 0x1D
.equ TryRecordMixLinkup, 0x1E
.equ CloseLink, 0x1F
.equ EnterColosseumPlayerSpot, 0x20
.equ EnterTradeSeat, 0x21
.equ StartWiredCableClubTrade, 0x22
.equ CableClub_AskSaveTheGame, 0x23
.equ NullFieldSpecial, 0x24
.equ NullFieldSpecial, 0x25
.equ NullFieldSpecial, 0x26
.equ SavePlayerParty, 0x27
.equ LoadPlayerParty, 0x28
.equ ChooseHalfPartyForBattle, 0x29
.equ Script_ShowLinkTrainerCard, 0x2A
.equ NullFieldSpecial, 0x2B
.equ NullFieldSpecial, 0x2C
.equ NullFieldSpecial, 0x2D
.equ NullFieldSpecial, 0x2E
.equ NullFieldSpecial, 0x2F
.equ NullFieldSpecial, 0x30
.equ NullFieldSpecial, 0x31
.equ IsEnigmaBerryValid, 0x32
.equ GetTrainerBattleMode, 0x33
.equ ShowTrainerIntroSpeech, 0x34
.equ ShowTrainerCantBattleSpeech, 0x35
.equ Script_HasTrainerBeenFought, 0x36
.equ EndTrainerApproach, 0x37
.equ PlayTrainerEncounterMusic, 0x38
.equ ShouldTryRematchBattle, 0x39
.equ IsTrainerReadyForRematch, 0x3A
.equ StartRematchBattle, 0x3B
.equ ShowPokemonStorageSystemPC, 0x3C
.equ HasEnoughMonsForDoubleBattle, 0x3D
.equ NullFieldSpecial, 0x3E
.equ NullFieldSpecial, 0x3F
.equ NullFieldSpecial, 0x40
.equ NullFieldSpecial, 0x41
.equ NullFieldSpecial, 0x42
.equ NullFieldSpecial, 0x43
.equ NullFieldSpecial, 0x44
.equ HasLeadMonBeenRenamed, 0x45
.equ NullFieldSpecial, 0x46
.equ NullFieldSpecial, 0x47
.equ NullFieldSpecial, 0x48
.equ NullFieldSpecial, 0x49
.equ NullFieldSpecial, 0x4A
.equ NullFieldSpecial, 0x4B
.equ NullFieldSpecial, 0x4C
.equ NullFieldSpecial, 0x4D
.equ NullFieldSpecial, 0x4E
.equ NullFieldSpecial, 0x4F
.equ NullFieldSpecial, 0x50
.equ NullFieldSpecial, 0x51
.equ NullFieldSpecial, 0x52
.equ NullFieldSpecial, 0x53
.equ NullFieldSpecial, 0x54
.equ NullFieldSpecial, 0x55
.equ NullFieldSpecial, 0x56
.equ NullFieldSpecial, 0x57
.equ NullFieldSpecial, 0x58
.equ NullFieldSpecial, 0x59
.equ NullFieldSpecial, 0x5A
.equ NullFieldSpecial, 0x5B
.equ TryContestLinkup, 0x5C
.equ Field_AskSaveTheGame, 0x5D
.equ DoWateringBerryTreeAnim, 0x5E
.equ ShowEasyChatScreen, 0x5F
.equ ShowEasyChatMessage, 0x60
.equ NullFieldSpecial, 0x61
.equ NullFieldSpecial, 0x62
.equ NullFieldSpecial, 0x63
.equ NullFieldSpecial, 0x64
.equ NullFieldSpecial, 0x65
.equ NullFieldSpecial, 0x66
.equ NullFieldSpecial, 0x67
.equ NullFieldSpecial, 0x68
.equ NullFieldSpecial, 0x69
.equ NullFieldSpecial, 0x6A
.equ NullFieldSpecial, 0x6B
.equ NullFieldSpecial, 0x6C
.equ NullFieldSpecial, 0x6D
.equ NullFieldSpecial, 0x6E
.equ NullFieldSpecial, 0x6F
.equ NullFieldSpecial, 0x70
.equ NullFieldSpecial, 0x71
.equ NullFieldSpecial, 0x72
.equ NullFieldSpecial, 0x73
.equ NullFieldSpecial, 0x74
.equ NullFieldSpecial, 0x75
.equ NullFieldSpecial, 0x76
.equ GetHeracrossSizeRecordInfo, 0x77
.equ CompareHeracrossSize, 0x78
.equ GetMagikarpSizeRecordInfo, 0x79
.equ CompareMagikarpSize, 0x7A
.equ NameRaterWasNicknameChanged, 0x7B
.equ BufferMonNickname, 0x7C
.equ IsMonOTIDNotPlayers, 0x7D
.equ NullFieldSpecial, 0x7E
.equ NullFieldSpecial, 0x7F
.equ BufferRandomHobbyOrLifestyleString, 0x80
.equ NullFieldSpecial, 0x81
.equ RegisteredItemHandleBikeSwap, 0x82
.equ CalculatePlayerPartyCount, 0x83
.equ CountPartyNonEggMons, 0x84
.equ CountPartyAliveNonEggMons_IgnoreVar0x8004Slot, 0x85
.equ NullFieldSpecial, 0x86
.equ NullFieldSpecial, 0x87
.equ NullFieldSpecial, 0x88
.equ NullFieldSpecial, 0x89
.equ NullFieldSpecial, 0x8A
.equ NullFieldSpecial, 0x8B
.equ NullFieldSpecial, 0x8C
.equ ShowFieldMessageStringVar4, 0x8D
.equ DrawWholeMapView, 0x8E
.equ GetPlayerXY, 0x8F
.equ NullFieldSpecial, 0x90
.equ NullFieldSpecial, 0x91
.equ NullFieldSpecial, 0x92
.equ GetPlayerTrainerIdOnesDigit, 0x93
.equ BufferBigGuyOrBigGirlString, 0x94
.equ BufferSonOrDaughterString, 0x95
.equ SetHiddenItemFlag, 0x96
.equ NullFieldSpecial, 0x97
.equ NullFieldSpecial, 0x98
.equ Overworld_PlaySpecialMapMusic, 0x99
.equ NullFieldSpecial, 0x9A
.equ NullFieldSpecial, 0x9B
.equ NullFieldSpecial, 0x9C
.equ StartOldManTutorialBattle, 0x9D
.equ ChangePokemonNickname, 0x9E
.equ ChoosePartyMon, 0x9F
.equ NullFieldSpecial, 0xA0
.equ NullFieldSpecial, 0xA1
.equ NullFieldSpecial, 0xA2
.equ Script_IsFanClubMemberFanOfPlayer, 0xA3
.equ Script_GetNumFansOfPlayerInTrainerFanClub, 0xA4
.equ Script_BufferFanClubTrainerName, 0xA5
.equ Script_TryLoseFansFromPlayTimeAfterLinkBattle, 0xA6
.equ Script_TryLoseFansFromPlayTime, 0xA7
.equ Script_SetPlayerGotFirstFans, 0xA8
.equ Script_UpdateTrainerFanClubGameClear, 0xA9
.equ Script_TryGainNewFanFromCounter, 0xAA
.equ RockSmashWildEncounter, 0xAB
.equ NullFieldSpecial, 0xAC
.equ NullFieldSpecial, 0xAD
.equ NullFieldSpecial, 0xAE
.equ NullFieldSpecial, 0xAF
.equ NullFieldSpecial, 0xB0
.equ NullFieldSpecial, 0xB1
.equ NullFieldSpecial, 0xB2
.equ NullFieldSpecial, 0xB3
.equ GetBattleOutcome, 0xB4
.equ GetDaycareMonNicknames, 0xB5
.equ GetDaycareState, 0xB6
.equ RejectEggFromDayCare, 0xB7
.equ GiveEggFromDaycare, 0xB8
.equ SetDaycareCompatibilityString, 0xB9
.equ GetSelectedMonNicknameAndSpecies, 0xBA
.equ StoreSelectedPokemonInDaycare, 0xBB
.equ ChooseSendDaycareMon, 0xBC
.equ ShowDaycareLevelMenu, 0xBD
.equ GetNumLevelsGainedFromDaycare, 0xBE
.equ GetDaycareCost, 0xBF
.equ TakePokemonFromDaycare, 0xC0
.equ ScriptHatchMon, 0xC1
.equ EggHatch, 0xC2
.equ DaycareMonReceivedMail, 0xC3
.equ ShowBattleRecords, 0xC4
.equ IsEnoughForCostInVar0x8005, 0xC5
.equ SubtractMoneyFromVar0x8005, 0xC6
.equ TryFieldPoisonWhiteOut, 0xC7
.equ SetCB2WhiteOut, 0xC8
.equ NullFieldSpecial, 0xC9
.equ NullFieldSpecial, 0xCA
.equ NullFieldSpecial, 0xCB
.equ NullFieldSpecial, 0xCC
.equ EnterSafariMode, 0xCD
.equ ExitSafariMode, 0xCE
.equ NullFieldSpecial, 0xCF
.equ NullFieldSpecial, 0xD0
.equ NullFieldSpecial, 0xD1
.equ NullFieldSpecial, 0xD2
.equ NullFieldSpecial, 0xD3
.equ GetPokedexCount, 0xD4
.equ GetProfOaksRatingMessage, 0xD5
.equ AnimatePcTurnOn, 0xD6
.equ AnimatePcTurnOff, 0xD7
.equ GetElevatorFloor, 0xD8
.equ NullFieldSpecial, 0xD9
.equ NullFieldSpecial, 0xDA
.equ SelectMoveTutorMon, 0xDB
.equ SelectMoveDeleterMove, 0xDC
.equ MoveDeleterForgetMove, 0xDD
.equ BufferMoveDeleterNicknameAndMove, 0xDE
.equ GetNumMovesSelectedMonHas, 0xDF
.equ DisplayMoveTutorMenu, 0xE0
.equ NullFieldSpecial, 0xE1
.equ NullFieldSpecial, 0xE2
.equ GetPlayerAvatarBike, 0xE3
.equ NullFieldSpecial, 0xE4
.equ NullFieldSpecial, 0xE5
.equ GetLeadMonFriendship, 0xE6
.equ BattleTowerMapScript2, 0xE7
.equ ChooseNextBattleTowerTrainer, 0xE8
.equ CheckPartyBattleTowerBanlist, 0xE9
.equ PrintBattleTowerTrainerGreeting, 0xEA
.equ BufferEReaderTrainerGreeting, 0xEB
.equ StartSpecialBattle, 0xEC
.equ SetBattleTowerProperty, 0xED
.equ BattleTowerUtil, 0xEE
.equ SetBattleTowerParty, 0xEF
.equ SaveBattleTowerProgress, 0xF0
.equ BattleTower_SoftReset, 0xF1
.equ DetermineBattleTowerPrize, 0xF2
.equ GiveBattleTowerPrize, 0xF3
.equ AwardBattleTowerRibbons, 0xF4
.equ ChooseBattleTowerPlayerParty, 0xF5
.equ ValidateEReaderTrainer, 0xF6
.equ NullFieldSpecial, 0xF7
.equ ReducePlayerPartyToThree, 0xF8
.equ BedroomPC, 0xF9
.equ PlayerPC, 0xFA
.equ ShowTownMap, 0xFB
.equ GetInGameTradeSpeciesInfo, 0xFC
.equ CreateInGameTradePokemon, 0xFD
.equ DoInGameTradeScene, 0xFE
.equ GetTradeSpecies, 0xFF
.equ NullFieldSpecial, 0x100
.equ NullFieldSpecial, 0x101
.equ NullFieldSpecial, 0x102
.equ NullFieldSpecial, 0x103
.equ NullFieldSpecial, 0x104
.equ NullFieldSpecial, 0x105
.equ CreatePCMenu, 0x106
.equ HallOfFamePCBeginFade, 0x107
.equ ShowDiploma, 0x108
.equ NullFieldSpecial, 0x109
.equ NullFieldSpecial, 0x10A
.equ NullFieldSpecial, 0x10B
.equ NullFieldSpecial, 0x10C
.equ NullFieldSpecial, 0x10D
.equ LookThroughPorthole, 0x10E
.equ DoSoftReset, 0x10F
.equ EnterHallOfFame, 0x110
.equ AnimateElevator, 0x111
.equ NullFieldSpecial, 0x112
.equ SpawnCameraObject, 0x113
.equ RemoveCameraObject, 0x114
.equ NullFieldSpecial, 0x115
.equ NullFieldSpecial, 0x116
.equ NullFieldSpecial, 0x117
.equ NullFieldSpecial, 0x118
.equ NullFieldSpecial, 0x119
.equ NullFieldSpecial, 0x11A
.equ NullFieldSpecial, 0x11B
.equ NullFieldSpecial, 0x11C
.equ BufferEReaderTrainerName, 0x11D
.equ GetRandomSlotMachineId, 0x11E
.equ GetPlayerFacingDirection, 0x11F
.equ NullFieldSpecial, 0x120
.equ NullFieldSpecial, 0x121
.equ NullFieldSpecial, 0x122
.equ NullFieldSpecial, 0x123
.equ LeadMonHasEffortRibbon, 0x124
.equ GiveLeadMonEffortRibbon, 0x125
.equ AreLeadMonEVsMaxedOut, 0x126
.equ Script_FacePlayer, 0x127
.equ Script_ClearHeldMovement, 0x128
.equ InitRoamer, 0x129
.equ NullFieldSpecial, 0x12A
.equ PlayerHasGrassPokemonInParty, 0x12B
.equ NullFieldSpecial, 0x12C
.equ NullFieldSpecial, 0x12D
.equ IsStarterFirstStageInParty, 0x12E
.equ NullFieldSpecial, 0x12F
.equ IsThereRoomInAnyBoxForMorePokemon, 0x130
.equ NullFieldSpecial, 0x131
.equ DrawElevatorCurrentFloorWindow, 0x132
.equ NullFieldSpecial, 0x133
.equ IsPokerusInParty, 0x134
.equ SetIcefallCaveCrackedIceMetatiles, 0x135
.equ ShakeScreen, 0x136
.equ StartGroudonKyogreBattle, 0x137
.equ StartLegendaryBattle, 0x138
.equ StartRegiBattle, 0x139
.equ SetUpTrainerMovement, 0x13A
.equ NullFieldSpecial, 0x13B
.equ NullFieldSpecial, 0x13C
.equ StartDroughtWeatherBlend, 0x13D
.equ DoDiveWarp, 0x13E
.equ DoFallWarp, 0x13F
.equ NullFieldSpecial, 0x140
.equ NullFieldSpecial, 0x141
.equ SetEReaderTrainerGfxId, 0x142
.equ StartSouthernIslandBattle, 0x143
.equ NullFieldSpecial, 0x144
.equ NullFieldSpecial, 0x145
.equ NullFieldSpecial, 0x146
.equ GetPartyMonSpecies, 0x147
.equ IsSelectedMonEgg, 0x148
.equ NullFieldSpecial, 0x149
.equ NullFieldSpecial, 0x14A
.equ LoadPlayerBag, 0x14B
.equ Script_FadeOutMapMusic, 0x14C
.equ NullFieldSpecial, 0x14D
.equ NullFieldSpecial, 0x14E
.equ HasAllKantoMons, 0x14F
.equ IsMonOTNameNotPlayers, 0x150
.equ NullFieldSpecial, 0x151
.equ Dummy_TryEnableBravoTrainerBattleTower, 0x152
.equ DoesPartyHaveEnigmaBerry, 0x153
.equ NullFieldSpecial, 0x154
.equ SetPostgameFlags, 0x155
.equ StartMarowakBattle, 0x156
.equ ForcePlayerOntoBike, 0x157
.equ ListMenu, 0x158
.equ ReturnToListMenu, 0x159
.equ DoPicboxCancel, 0x15A
.equ SetVermilionTrashCans, 0x15B
.equ SeafoamIslandsB4F_CurrentDumpsPlayerOnLand, 0x15C
.equ SampleResortGorgeousMonAndReward, 0x15D
.equ CheckAddCoins, 0x15E
.equ GetDaycarePokemonCount, 0x15F
.equ CloseElevatorCurrentFloorWindow, 0x160
.equ ForcePlayerToStartSurfing, 0x161
.equ GetStarterSpecies, 0x162
.equ SetSeenMon, 0x163
.equ VsSeekerResetObjectMovementAfterChargeComplete, 0x164
.equ ShouldShowBoxWasFullMessage, 0x165
.equ ChangeBoxPokemonNickname, 0x166
.equ UpdateTrainerCardPhotoIcons, 0x167
.equ StickerManGetBragFlags, 0x168
.equ SetUsedPkmnCenterQuestLogEvent, 0x169
.equ IsWirelessAdapterConnected, 0x16A
.equ TryBecomeLinkLeader, 0x16B
.equ TryJoinLinkGroup, 0x16C
.equ RunUnionRoom, 0x16D
.equ ShowWirelessCommunicationScreen, 0x16E
.equ EnableNationalPokedex, 0x16F
.equ SetWalkingIntoSignVars, 0x170
.equ DisableMsgBoxWalkaway, 0x171
.equ VsSeekerFreezeObjectsAfterChargeComplete, 0x172
.equ SetFlavorTextFlagFromSpecialVars, 0x173
.equ UpdatePickStateFromSpecialVar8005, 0x174
.equ OverworldWhiteOutGetMoneyLoss, 0x175
.equ PutMonInRoute5Daycare, 0x176
.equ GetCostToWithdrawRoute5DaycareMon, 0x177
.equ IsThereMonInRoute5Daycare, 0x178
.equ GetNumLevelsGainedForRoute5DaycareMon, 0x179
.equ TakePokemonFromRoute5Daycare, 0x17A
.equ DoSeagallopFerryScene, 0x17B
.equ DoesPlayerPartyContainSpecies, 0x17C
.equ Script_SetHelpContext, 0x17D
.equ BackupHelpContext, 0x17E
.equ RestoreHelpContext, 0x17F
.equ ValidateSavedWonderCard, 0x180
.equ SetUnlockedPokedexFlags, 0x181
.equ InitUnionRoom, 0x182
.equ BufferUnionRoomPlayerName, 0x183
.equ QuestLog_StartRecordingInputsAfterDeferredEvent, 0x184
.equ GetMartClerkObjectId, 0x185
.equ GetMysteryGiftCardStat, 0x186
.equ GetQuestLogState, 0x187
.equ QuestLog_CutRecording, 0x188
.equ WonderNews_GetRewardInfo, 0x189
.equ GetPCBoxToSendMon, 0x18A
.equ OpenMuseumFossilPic, 0x18B
.equ CloseMuseumFossilPic, 0x18C
.equ ChooseMonForMoveTutor, 0x18D
.equ ChooseMonForWirelessMinigame, 0x18E
.equ SetBattledTrainerFlag, 0x18F
.equ SetHelpContextForMap, 0x190
.equ DoSSAnneDepartureCutscene, 0x191
.equ IsPokemonJumpSpeciesInParty, 0x192
.equ IsNationalPokedexEnabled, 0x193
.equ CallTrainerTowerFunc, 0x194
.equ ShowPokemonJumpRecords, 0x195
.equ BufferTMHMMoveName, 0x196
.equ DaisyMassageServices, 0x197
.equ HelpSystem_Disable, 0x198
.equ HelpSystem_Enable, 0x199
.equ SetPostgameFlags, 0x19A
.equ HasAtLeastOneBerry, 0x19B
.equ DisplayBerryPowderVendorMenu, 0x19C
.equ RemoveBerryPowderVendorMenu, 0x19D
.equ Script_HasEnoughBerryPowder, 0x19E
.equ Script_TakeBerryPowder, 0x19F
.equ PrintPlayerBerryPowderAmount, 0x1A0
.equ DoPokemonLeagueLightingEffect, 0x1A1
.equ ShowBerryCrushRankings, 0x1A2
.equ CapeBrinkGetMoveToTeachLeadPokemon, 0x1A3
.equ HasLearnedAllMovesFromCapeBrinkTutor, 0x1A4
.equ DoCredits, 0x1A5
.equ ShowDodrioBerryPickingRecords, 0x1A6
.equ DrawSeagallopDestinationMenu, 0x1A7
.equ GetSelectedSeagallopDestination, 0x1A8
.equ GetSeagallopNumber, 0x1A9
.equ GetPlayerFacingDirection, 0x1AA
.equ DoDeoxysTriangleInteraction, 0x1AB
.equ SetDeoxysTrianglePalette, 0x1AC
.equ IsPlayerLeftOfVermilionSailor, 0x1AD
.equ IsBadEggInParty, 0x1AE
.equ ReadTrainerTowerAndValidate, 0x1AF
.equ HasAllMons, 0x1B0
.equ IsPlayerNotInTrainerTowerLobby, 0x1B1
.equ BrailleCursorToggle, 0x1B2
.equ Script_ResetUnionRoomTrade, 0x1B3
.equ PlayerPartyContainsSpeciesWithPlayerID, 0x1B4
.equ AnimateTeleporterHousing, 0x1B5
.equ IsDodrioInParty, 0x1B6
.equ AnimateTeleporterCable, 0x1B7
.equ InitElevatorFloorSelectMenuPos, 0x1B8
.equ UpdateLoreleiDollCollection, 0x1B9
.equ LoopWingFlapSound, 0x1BA
.equ CreateEnemyEventMo, 0x1BB

@Raid Specials
.equ IsRaidBattleAvaliable, 0x115
.equ StartRaidBattleIntro, 0x116
.equ CreateRaidPokemon, 0x117
.equ StartRaidBattle, 0x118
.equ FinishRaidBattle, 0x119
.equ ClearRaidBattleFlag, 0x11A
.equ AreRaidBattlesDone, 0x11B
