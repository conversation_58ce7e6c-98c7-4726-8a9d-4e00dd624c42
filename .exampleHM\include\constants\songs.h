#ifndef GUARD_CONSTANTS_SONGS_H
#define GUARD_CONSTANTS_SONGS_H

#define MUS_DUMMY                 0   // MUS_DUMMY
#define SE_USE_ITEM               1   // SE_KAIFUKU
#define SE_PC_LOGIN               2   // SE_PC_LOGIN
#define SE_PC_OFF                 3   // SE_PC_OFF
#define SE_PC_ON                  4   // SE_PC_ON
#define SE_SELECT                 5   // SE_SELECT
#define SE_WIN_OPEN               6   // SE_WIN_OPEN
#define SE_WALL_HIT               7   // SE_WALL_HIT
#define SE_RS_DOOR                8   // SE_DOOR
#define SE_EXIT                   9   // SE_KAIDAN
#define SE_LEDGE                  10  // SE_DANSA
#define SE_BIKE_BELL              11  // SE_JITENSYA
#define SE_NOT_EFFECTIVE          12  // SE_KOUKA_L
#define SE_EFFECTIVE              13  // SE_KOUKA_M
#define SE_SUPER_EFFECTIVE        14  // SE_KOUKA_H
#define SE_BALL_OPEN              15  // SE_BOWA2
#define SE_FAINT                  16  // SE_POKE_DEAD
#define SE_FLEE                   17  // SE_NIGERU
#define SE_SLIDING_DOOR           18  // SE_JIDO_DOA
#define SE_SHIP                   19  // SE_NAMINORI
#define SE_BANG                   20  // SE_BAN
#define SE_PIN                    21  // SE_PIN
#define SE_BOO                    22  // SE_BOO
#define SE_BALL                   23  // SE_BOWA
#define SE_CONTEST_PLACE          24  // SE_JYUNI
#define SE_SUCCESS                25  // SE_SEIKAI
#define SE_FAILURE                26  // SE_HAZURE
#define SE_EXP                    27  // SE_EXP
#define SE_BIKE_HOP               28  // SE_JITE_PYOKO
#define SE_SWITCH                 29  // SE_MU_PACHI
#define SE_CLICK                  30  // SE_TK_KASYA
#define SE_FU_ZAKU                31  // SE_FU_ZAKU
#define SE_CONTEST_CONDITION_LOSE 32  // SE_FU_ZAKU2
#define SE_LAVARIDGE_FALL_WARP    33  // SE_FU_ZUZUZU
#define SE_ICE_STAIRS             34  // SE_RU_GASHIN
#define SE_ICE_BREAK              35  // SE_RU_GASYAN
#define SE_ICE_CRACK              36  // SE_RU_BARI
#define SE_FALL                   37  // SE_RU_HYUU
#define SE_UNLOCK                 38  // SE_KI_GASYAN
#define SE_WARP_IN                39  // SE_TK_WARPIN
#define SE_WARP_OUT               40  // SE_TK_WARPOUT
#define SE_REPEL                  41  // SE_TU_SAA
#define SE_ROTATING_GATE          42  // SE_HI_TURUN
#define SE_TRUCK_MOVE             43  // SE_TRACK_MOVE
#define SE_TRUCK_STOP             44  // SE_TRACK_STOP
#define SE_TRUCK_UNLOAD           45  // SE_TRACK_HAIKI
#define SE_TRUCK_DOOR             46  // SE_TRACK_DOOR
#define SE_BERRY_BLENDER          47  // SE_MOTER
#define SE_SAVE                   48  // SE_SAVE
#define SE_BALL_BOUNCE_1          49  // SE_KON
#define SE_BALL_BOUNCE_2          50  // SE_KON2
#define SE_BALL_BOUNCE_3          51  // SE_KON3
#define SE_BALL_BOUNCE_4          52  // SE_KON4
#define SE_BALL_TRADE             53  // SE_SUIKOMU
#define SE_BALL_THROW             54  // SE_NAGERU
#define SE_NOTE_C                 55  // SE_TOY_C
#define SE_NOTE_D                 56  // SE_TOY_D
#define SE_NOTE_E                 57  // SE_TOY_E
#define SE_NOTE_F                 58  // SE_TOY_F
#define SE_NOTE_G                 59  // SE_TOY_G
#define SE_NOTE_A                 60  // SE_TOY_A
#define SE_NOTE_B                 61  // SE_TOY_B
#define SE_NOTE_C_HIGH            62  // SE_TOY_C1
#define SE_PUDDLE                 63  // SE_MIZU
#define SE_BRIDGE_WALK            64  // SE_HASHI
#define SE_ITEMFINDER             65  // SE_DAUGI
#define SE_DING_DONG              66  // SE_PINPON
#define SE_BALLOON_RED            67  // SE_FUUSEN1
#define SE_BALLOON_BLUE           68  // SE_FUUSEN2
#define SE_BALLOON_YELLOW         69  // SE_FUUSEN3
#define SE_BREAKABLE_DOOR         70  // SE_TOY_KABE
#define SE_MUD_BALL               71  // SE_TOY_DANGO
#define SE_FIELD_POISON           72  // SE_DOKU
#define SE_ESCALATOR              73  // SE_ESUKA
#define SE_THUNDERSTORM           74  // SE_T_AME
#define SE_THUNDERSTORM_STOP      75  // SE_T_AME_E
#define SE_DOWNPOUR               76  // SE_T_OOAME
#define SE_DOWNPOUR_STOP          77  // SE_T_OOAME_E
#define SE_RAIN                   78  // SE_T_KOAME
#define SE_RAIN_STOP              79  // SE_T_KOAME_E
#define SE_THUNDER                80  // SE_T_KAMI
#define SE_THUNDER2               81  // SE_T_KAMI2
#define SE_ELEVATOR               82  // SE_ELEBETA
#define SE_LOW_HEALTH             83  // SE_HINSI
#define SE_EXP_MAX                84  // SE_EXPMAX
#define SE_ROULETTE_BALL          85  // SE_TAMAKORO
#define SE_ROULETTE_BALL2         86  // SE_TAMAKORO_E
#define SE_TAILLOW_WING_FLAP      87  // SE_BASABASA
#define SE_RS_SHOP                88  // SE_REGI
#define SE_CONTEST_HEART          89  // SE_C_GAJI
#define SE_CONTEST_CURTAIN_RISE   90  // SE_C_MAKU_U
#define SE_CONTEST_CURTAIN_FALL   91  // SE_C_MAKU_D
#define SE_CONTEST_ICON_CHANGE    92  // SE_C_PASI
#define SE_CONTEST_ICON_CLEAR     93  // SE_C_SYU
#define SE_CONTEST_MONS_TURN      94  // SE_C_PIKON
#define SE_SHINY                  95  // SE_REAPOKE
#define SE_INTRO_BLAST            96  // SE_OP_BASYU
#define SE_MUGSHOT                97  // SE_BT_START
#define SE_APPLAUSE               98  // SE_DENDOU
#define SE_VEND                   99  // SE_JIHANKI
#define SE_ORB                    100 // SE_TAMA
#define SE_DEX_SCROLL             101 // SE_Z_SCROLL
#define SE_DEX_PAGE               102 // SE_Z_PAGE
#define SE_POKENAV_ON             103 // SE_PN_ON
#define SE_POKENAV_OFF            104 // SE_PN_OFF
#define SE_DEX_SEARCH             105 // SE_Z_SEARCH
#define SE_EGG_HATCH              106 // SE_TAMAGO
#define SE_BALL_TRAY_ENTER        107 // SE_TB_START
#define SE_BALL_TRAY_BALL         108 // SE_TB_KON
#define SE_BALL_TRAY_EXIT         109 // SE_TB_KARA
#define SE_GLASS_FLUTE            110 // SE_BIDORO
// Move SFX
#define SE_M_THUNDERBOLT          111 // SE_W085
#define SE_M_THUNDERBOLT2         112 // SE_W085B
#define SE_M_HARDEN               113 // SE_W231
#define SE_M_NIGHTMARE            114 // SE_W171
#define SE_M_VITAL_THROW          115 // SE_W233
#define SE_M_VITAL_THROW2         116 // SE_W233B
#define SE_M_BUBBLE               117 // SE_W145
#define SE_M_BUBBLE2              118 // SE_W145B
#define SE_M_BUBBLE3              119 // SE_W145C
#define SE_M_RAIN_DANCE           120 // SE_W240
#define SE_M_CUT                  121 // SE_W015
#define SE_M_STRING_SHOT          122 // SE_W081
#define SE_M_STRING_SHOT2         123 // SE_W081B
#define SE_M_ROCK_THROW           124 // SE_W088
#define SE_M_GUST                 125 // SE_W016
#define SE_M_GUST2                126 // SE_W016B
#define SE_M_DOUBLE_SLAP          127 // SE_W003
#define SE_M_DOUBLE_TEAM          128 // SE_W104
#define SE_M_RAZOR_WIND           129 // SE_W013
#define SE_M_ICY_WIND             130 // SE_W196
#define SE_M_THUNDER_WAVE         131 // SE_W086
#define SE_M_COMET_PUNCH          132 // SE_W004
#define SE_M_MEGA_KICK            133 // SE_W025
#define SE_M_MEGA_KICK2           134 // SE_W025B
#define SE_M_CRABHAMMER           135 // SE_W152
#define SE_M_JUMP_KICK            136 // SE_W026
#define SE_M_FLAME_WHEEL          137 // SE_W172
#define SE_M_FLAME_WHEEL2         138 // SE_W172B
#define SE_M_FLAMETHROWER         139 // SE_W053
#define SE_M_FIRE_PUNCH           140 // SE_W007
#define SE_M_TOXIC                141 // SE_W092
#define SE_M_SACRED_FIRE          142 // SE_W221
#define SE_M_SACRED_FIRE2         143 // SE_W221B
#define SE_M_EMBER                144 // SE_W052
#define SE_M_TAKE_DOWN            145 // SE_W036
#define SE_M_BLIZZARD             146 // SE_W059
#define SE_M_BLIZZARD2            147 // SE_W059B
#define SE_M_SCRATCH              148 // SE_W010
#define SE_M_VICEGRIP             149 // SE_W011
#define SE_M_WING_ATTACK          150 // SE_W017
#define SE_M_FLY                  151 // SE_W019
#define SE_M_SAND_ATTACK          152 // SE_W028
#define SE_M_RAZOR_WIND2          153 // SE_W013B
#define SE_M_BITE                 154 // SE_W044
#define SE_M_HEADBUTT             155 // SE_W029
#define SE_M_SURF                 156 // SE_W057
#define SE_M_HYDRO_PUMP           157 // SE_W056
#define SE_M_WHIRLPOOL            158 // SE_W250
#define SE_M_HORN_ATTACK          159 // SE_W030
#define SE_M_TAIL_WHIP            160 // SE_W039
#define SE_M_MIST                 161 // SE_W054
#define SE_M_POISON_POWDER        162 // SE_W077
#define SE_M_BIND                 163 // SE_W020
#define SE_M_DRAGON_RAGE          164 // SE_W082
#define SE_M_SING                 165 // SE_W047
#define SE_M_PERISH_SONG          166 // SE_W195
#define SE_M_PAY_DAY              167 // SE_W006
#define SE_M_DIG                  168 // SE_W091
#define SE_M_DIZZY_PUNCH          169 // SE_W146
#define SE_M_SELF_DESTRUCT        170 // SE_W120
#define SE_M_EXPLOSION            171 // SE_W153
#define SE_M_ABSORB_2             172 // SE_W071B
#define SE_M_ABSORB               173 // SE_W071
#define SE_M_SCREECH              174 // SE_W103
#define SE_M_BUBBLE_BEAM          175 // SE_W062
#define SE_M_BUBBLE_BEAM2         176 // SE_W062B
#define SE_M_SUPERSONIC           177 // SE_W048
#define SE_M_BELLY_DRUM           178 // SE_W187
#define SE_M_METRONOME            179 // SE_W118
#define SE_M_BONEMERANG           180 // SE_W155
#define SE_M_LICK                 181 // SE_W122
#define SE_M_PSYBEAM              182 // SE_W060
#define SE_M_FAINT_ATTACK         183 // SE_W185
#define SE_M_SWORDS_DANCE         184 // SE_W014
#define SE_M_LEER                 185 // SE_W043
#define SE_M_SWAGGER              186 // SE_W207
#define SE_M_SWAGGER2             187 // SE_W207B
#define SE_M_HEAL_BELL            188 // SE_W215
#define SE_M_CONFUSE_RAY          189 // SE_W109
#define SE_M_SNORE                190 // SE_W173
#define SE_M_BRICK_BREAK          191 // SE_W280
#define SE_M_GIGA_DRAIN           192 // SE_W202
#define SE_M_PSYBEAM2             193 // SE_W060B
#define SE_M_SOLAR_BEAM           194 // SE_W076
#define SE_M_PETAL_DANCE          195 // SE_W080
#define SE_M_TELEPORT             196 // SE_W100
#define SE_M_MINIMIZE             197 // SE_W107
#define SE_M_SKETCH               198 // SE_W166
#define SE_M_SWIFT                199 // SE_W129
#define SE_M_REFLECT              200 // SE_W115
#define SE_M_BARRIER              201 // SE_W112
#define SE_M_DETECT               202 // SE_W197
#define SE_M_LOCK_ON              203 // SE_W199
#define SE_M_MOONLIGHT            204 // SE_W236
#define SE_M_CHARM                205 // SE_W204
#define SE_M_CHARGE               206 // SE_W268
#define SE_M_STRENGTH             207 // SE_W070
#define SE_M_HYPER_BEAM           208 // SE_W063
#define SE_M_WATERFALL            209 // SE_W127
#define SE_M_REVERSAL             210 // SE_W179
#define SE_M_ACID_ARMOR           211 // SE_W151
#define SE_M_SANDSTORM            212 // SE_W201
#define SE_M_TRI_ATTACK           213 // SE_W161
#define SE_M_TRI_ATTACK2          214 // SE_W161B
#define SE_M_ENCORE               215 // SE_W227
#define SE_M_ENCORE2              216 // SE_W227B
#define SE_M_BATON_PASS           217 // SE_W226
#define SE_M_MILK_DRINK           218 // SE_W208
#define SE_M_ATTRACT              219 // SE_W213
#define SE_M_ATTRACT2             220 // SE_W213B
#define SE_M_MORNING_SUN          221 // SE_W234
#define SE_M_FLATTER              222 // SE_W260
#define SE_M_SAND_TOMB            223 // SE_W328
#define SE_M_GRASSWHISTLE         224 // SE_W320
#define SE_M_SPIT_UP              225 // SE_W255
#define SE_M_DIVE                 226 // SE_W291
#define SE_M_EARTHQUAKE           227 // SE_W089
#define SE_M_TWISTER              228 // SE_W239
#define SE_M_SWEET_SCENT          229 // SE_W230
#define SE_M_YAWN                 230 // SE_W281
#define SE_M_SKY_UPPERCUT         231 // SE_W327
#define SE_M_STAT_INCREASE        232 // SE_W287
#define SE_M_HEAT_WAVE            233 // SE_W257
#define SE_M_UPROAR               234 // SE_W253
#define SE_M_HAIL                 235 // SE_W258
#define SE_M_COSMIC_POWER         236 // SE_W322
#define SE_M_TEETER_DANCE         237 // SE_W298
#define SE_M_STAT_DECREASE        238 // SE_W287B
#define SE_M_HAZE                 239 // SE_W114
#define SE_M_HYPER_BEAM2          240 // SE_W063B
// New FRLG SFX
#define SE_DOOR                   241 // SE_W_DOOR
#define SE_CARD_FLIP              242 // SE_CARD1
#define SE_CARD_FLIPPING          243 // SE_CARD2
#define SE_CARD_OPEN              244 // SE_CARD3
#define SE_BAG_CURSOR             245 // SE_BAG1
#define SE_BAG_POCKET             246 // SE_BAG2
#define SE_BALL_CLICK             247 // SE_GETTING
#define SE_SHOP                   248 // SE_SHOP
#define SE_SS_ANNE_HORN           249 // SE_KITEKI
#define SE_HELP_OPEN              250 // SE_HELP_OP
#define SE_HELP_CLOSE             251 // SE_HELP_CL
#define SE_HELP_ERROR             252 // SE_HELP_NG
#define SE_DEOXYS_MOVE            253 // SE_DEOMOV
#define SE_POKE_JUMP_SUCCESS      254 // SE_EXCELLENT
#define SE_POKE_JUMP_FAILURE      255 // SE_NAWAMISS
// Music kept from RS
#define MUS_HEAL                  256 // MUS_ME_ASA
#define MUS_LEVEL_UP              257 // MUS_FANFA1
#define MUS_OBTAIN_ITEM           258 // MUS_FANFA4
#define MUS_EVOLVED               259 // MUS_FANFA5
#define MUS_OBTAIN_BADGE          260 // MUS_ME_BACHI
#define MUS_OBTAIN_TMHM           261 // MUS_ME_WAZA
#define MUS_OBTAIN_BERRY          262 // MUS_ME_KINOMI
#define MUS_EVOLUTION_INTRO       263 // MUS_ME_SHINKA
#define MUS_EVOLUTION             264 // MUS_SHINKA
#define MUS_RS_VS_GYM_LEADER      265 // MUS_BATTLE32
#define MUS_RS_VS_TRAINER         266 // MUS_BATTLE20
#define MUS_SCHOOL                267 // MUS_P_SCHOOL
#define MUS_SLOTS_JACKPOT         268 // MUS_ME_B_BIG
#define MUS_SLOTS_WIN             269 // MUS_ME_B_SMALL
#define MUS_MOVE_DELETED          270 // MUS_ME_WASURE
#define MUS_TOO_BAD               271 // MUS_ME_ZANNEN
// New FRLG Music
#define MUS_FOLLOW_ME             272 // MUS_ANNAI
#define MUS_GAME_CORNER           273 // MUS_SLOT
#define MUS_ROCKET_HIDEOUT        274 // MUS_AJITO
#define MUS_GYM                   275 // MUS_GYM
#define MUS_JIGGLYPUFF            276 // MUS_PURIN
#define MUS_INTRO_FIGHT           277 // MUS_DEMO
#define MUS_TITLE                 278 // MUS_TITLE
#define MUS_CINNABAR              279 // MUS_GUREN
#define MUS_LAVENDER              280 // MUS_SHION
#define MUS_HEAL_UNUSED           281 // MUS_KAIHUKU
#define MUS_CYCLING               282 // MUS_CYCLING
#define MUS_ENCOUNTER_ROCKET      283 // MUS_ROCKET
#define MUS_ENCOUNTER_GIRL        284 // MUS_SHOUJO
#define MUS_ENCOUNTER_BOY         285 // MUS_SHOUNEN
#define MUS_HALL_OF_FAME          286 // MUS_DENDOU
#define MUS_VIRIDIAN_FOREST       287 // MUS_T_MORI
#define MUS_MT_MOON               288 // MUS_OTSUKIMI
#define MUS_POKE_MANSION          289 // MUS_POKEYASHI
#define MUS_CREDITS               290 // MUS_ENDING
#define MUS_ROUTE1                291 // MUS_LOAD01
#define MUS_ROUTE24               292 // MUS_OPENING
#define MUS_ROUTE3                293 // MUS_LOAD02
#define MUS_ROUTE11               294 // MUS_LOAD03
#define MUS_VICTORY_ROAD          295 // MUS_CHAMP_R
#define MUS_VS_GYM_LEADER         296 // MUS_VS_GYM
#define MUS_VS_TRAINER            297 // MUS_VS_TORE
#define MUS_VS_WILD               298 // MUS_VS_YASEI
#define MUS_VS_CHAMPION           299 // MUS_VS_LAST
#define MUS_PALLET                300 // MUS_MASARA
#define MUS_OAK_LAB               301 // MUS_KENKYU
#define MUS_OAK                   302 // MUS_OHKIDO
#define MUS_POKE_CENTER           303 // MUS_POKECEN
#define MUS_SS_ANNE               304 // MUS_SANTOAN
#define MUS_SURF                  305 // MUS_NAMINORI
#define MUS_POKE_TOWER            306 // MUS_P_TOWER
#define MUS_SILPH                 307 // MUS_SHIRUHU
#define MUS_FUCHSIA               308 // MUS_HANADA
#define MUS_CELADON               309 // MUS_TAMAMUSI
#define MUS_VICTORY_TRAINER       310 // MUS_WIN_TRE
#define MUS_VICTORY_WILD          311 // MUS_WIN_YASEI
#define MUS_VICTORY_GYM_LEADER    312 // MUS_WIN_GYM
#define MUS_VERMILLION            313 // MUS_KUCHIBA
#define MUS_PEWTER                314 // MUS_NIBI
#define MUS_ENCOUNTER_RIVAL       315 // MUS_RIVAL1
#define MUS_RIVAL_EXIT            316 // MUS_RIVAL2
#define MUS_DEX_RATING            317 // MUS_FAN2
#define MUS_OBTAIN_KEY_ITEM       318 // MUS_FAN5
#define MUS_CAUGHT_INTRO          319 // MUS_FAN6
#define MUS_PHOTO                 320 // MUS_ME_PHOTO
#define MUS_GAME_FREAK            321 // MUS_TITLEROG
#define MUS_CAUGHT                322 // MUS_GET_YASEI
#define MUS_NEW_GAME_INSTRUCT     323 // MUS_SOUSA
#define MUS_NEW_GAME_INTRO        324 // MUS_SEKAIKAN
#define MUS_NEW_GAME_EXIT         325 // MUS_SEIBETU
#define MUS_POKE_JUMP             326 // MUS_JUMP
#define MUS_UNION_ROOM            327 // MUS_UNION
#define MUS_NET_CENTER            328 // MUS_NETWORK
#define MUS_MYSTERY_GIFT          329 // MUS_OKURIMONO
#define MUS_BERRY_PICK            330 // MUS_KINOMIKUI
#define MUS_SEVII_CAVE            331 // MUS_NANADUNGEON
#define MUS_TEACHY_TV_SHOW        332 // MUS_OSHIE_TV
#define MUS_SEVII_ROUTE           333 // MUS_NANASHIMA
#define MUS_SEVII_DUNGEON         334 // MUS_NANAISEKI
#define MUS_SEVII_123             335 // MUS_NANA123
#define MUS_SEVII_45              336 // MUS_NANA45
#define MUS_SEVII_67              337 // MUS_NANA67
#define MUS_POKE_FLUTE            338 // MUS_POKEFUE
#define MUS_VS_DEOXYS             339 // MUS_VS_DEO
#define MUS_VS_MEWTWO             340 // MUS_VS_MYU2
#define MUS_VS_LEGEND             341 // MUS_VS_DEN
#define MUS_ENCOUNTER_GYM_LEADER  342 // MUS_EXEYE
#define MUS_ENCOUNTER_DEOXYS      343 // MUS_DEOEYE
#define MUS_TRAINER_TOWER         344 // MUS_T_TOWER
#define MUS_SLOW_PALLET           345 // MUS_SLOWMASARA
#define MUS_TEACHY_TV_MENU        346 // MUS_TVNOIZE

#define MUS_NONE                    0xFFFF

#endif  // GUARD_CONSTANTS_SONGS_H
