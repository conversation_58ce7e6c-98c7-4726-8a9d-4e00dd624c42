#!/usr/bin/env python3

"""
Celadon Department Store TM Shop Modifier
Implements modifications for Shop ID 11 (Celadon Department 2F North - TM Shop)

This script adds ALL available TMs from the CFRU project while maintaining
the shop's exclusive focus on Technical Machines and move-learning items.
"""

import struct

# Celadon TM Shop data (Shop ID 11)
CELADON_TM_SHOP_ID = 11
CELADON_TM_SHOP_OFFSET = 0x16BB74  # From .example project
CELADON_TM_SHOP_NAME = "Celadon Department 2F North (TMs)"

# All TMs available in CFRU (TM01-TM120)
# Based on CFRU config: NUM_TMS 120 and EXPANDED_TMSHMS
CFRU_ALL_TMS = [
    # Original FireRed TMs (TM01-TM50)
    0x121,  # ITEM_TM01_FOCUS_PUNCH
    0x122,  # ITEM_TM02_DRAGON_CLAW
    0x123,  # ITEM_TM03_WATER_PULSE
    0x124,  # ITEM_TM04_CALM_MIND
    0x125,  # ITEM_TM05_ROAR
    0x126,  # ITEM_TM06_TOXIC
    0x127,  # ITEM_TM07_HAIL
    0x128,  # ITEM_TM08_BULK_UP
    0x129,  # ITEM_TM09_BULLET_SEED
    0x12A,  # ITEM_TM10_HIDDEN_POWER
    0x12B,  # ITEM_TM11_SUNNY_DAY
    0x12C,  # ITEM_TM12_TAUNT
    0x12D,  # ITEM_TM13_ICE_BEAM
    0x12E,  # ITEM_TM14_BLIZZARD
    0x12F,  # ITEM_TM15_HYPER_BEAM
    0x130,  # ITEM_TM16_LIGHT_SCREEN
    0x131,  # ITEM_TM17_PROTECT
    0x132,  # ITEM_TM18_RAIN_DANCE
    0x133,  # ITEM_TM19_GIGA_DRAIN
    0x134,  # ITEM_TM20_SAFEGUARD
    0x135,  # ITEM_TM21_FRUSTRATION
    0x136,  # ITEM_TM22_SOLARBEAM
    0x137,  # ITEM_TM23_IRON_TAIL
    0x138,  # ITEM_TM24_THUNDERBOLT
    0x139,  # ITEM_TM25_THUNDER
    0x13A,  # ITEM_TM26_EARTHQUAKE
    0x13B,  # ITEM_TM27_RETURN
    0x13C,  # ITEM_TM28_DIG
    0x13D,  # ITEM_TM29_PSYCHIC
    0x13E,  # ITEM_TM30_SHADOW_BALL
    0x13F,  # ITEM_TM31_BRICK_BREAK
    0x140,  # ITEM_TM32_DOUBLE_TEAM
    0x141,  # ITEM_TM33_REFLECT
    0x142,  # ITEM_TM34_SHOCK_WAVE
    0x143,  # ITEM_TM35_FLAMETHROWER
    0x144,  # ITEM_TM36_SLUDGE_BOMB
    0x145,  # ITEM_TM37_SANDSTORM
    0x146,  # ITEM_TM38_FIRE_BLAST
    0x147,  # ITEM_TM39_ROCK_TOMB
    0x148,  # ITEM_TM40_AERIAL_ACE
    0x149,  # ITEM_TM41_TORMENT
    0x14A,  # ITEM_TM42_FACADE
    0x14B,  # ITEM_TM43_SECRET_POWER
    0x14C,  # ITEM_TM44_REST
    0x14D,  # ITEM_TM45_ATTRACT
    0x14E,  # ITEM_TM46_THIEF
    0x14F,  # ITEM_TM47_STEEL_WING
    0x150,  # ITEM_TM48_SKILL_SWAP
    0x151,  # ITEM_TM49_SNATCH
    0x152,  # ITEM_TM50_OVERHEAT
    
    # Extended TMs (TM51-TM120) from CFRU
    # Based on include/constants/items.h and asm_defines.s
    376,    # ITEM_TM51
    377,    # ITEM_TM52
    378,    # ITEM_TM53
    379,    # ITEM_TM54
    380,    # ITEM_TM55
    381,    # ITEM_TM56
    382,    # ITEM_TM57
    383,    # ITEM_TM58
    0x180,  # ITEM_TM59
    0x181,  # ITEM_TM60
    0x182,  # ITEM_TM61
    0x183,  # ITEM_TM62
    0x184,  # ITEM_TM63
    0x185,  # ITEM_TM64
    0x186,  # ITEM_TM65
    0x187,  # ITEM_TM66
    0x188,  # ITEM_TM67
    0x189,  # ITEM_TM68
    0x18A,  # ITEM_TM69
    0x18B,  # ITEM_TM70 *** CONFIRMED WORKING ***
    0x18C,  # ITEM_TM71
    0x18D,  # ITEM_TM72
    0x18E,  # ITEM_TM73
    0x18F,  # ITEM_TM74
    0x190,  # ITEM_TM75
    0x191,  # ITEM_TM76
    0x192,  # ITEM_TM77
    0x193,  # ITEM_TM78
    0x194,  # ITEM_TM79
    0x195,  # ITEM_TM80
    0x196,  # ITEM_TM81
    0x197,  # ITEM_TM82
    0x198,  # ITEM_TM83
    0x199,  # ITEM_TM84
    0x19A,  # ITEM_TM85
    0x19B,  # ITEM_TM86
    0x19C,  # ITEM_TM87
    0x19D,  # ITEM_TM88
    0x19E,  # ITEM_TM89
    0x19F,  # ITEM_TM90
    0x1A0,  # ITEM_TM91
    0x1A1,  # ITEM_TM92
    0x1A2,  # ITEM_TM93
    0x1A3,  # ITEM_TM94
    0x1A4,  # ITEM_TM95
    0x1A5,  # ITEM_TM96
    0x1A6,  # ITEM_TM97
    0x1A7,  # ITEM_TM98
    0x1A8,  # ITEM_TM99
    0x1A9,  # ITEM_TM100
    0x1AA,  # ITEM_TM101
    0x1AB,  # ITEM_TM102
    0x1AC,  # ITEM_TM103
    0x1AD,  # ITEM_TM104
    0x1AE,  # ITEM_TM105
    0x1AF,  # ITEM_TM106
    0x1B0,  # ITEM_TM107
    0x1B1,  # ITEM_TM108
    0x1B2,  # ITEM_TM109
    0x1B3,  # ITEM_TM110
    0x1B4,  # ITEM_TM111
    0x1B5,  # ITEM_TM112
    0x1B6,  # ITEM_TM113
    0x1B7,  # ITEM_TM114
    0x1B8,  # ITEM_TM115
    0x1B9,  # ITEM_TM116
    0x1BA,  # ITEM_TM117
    0x1BB,  # ITEM_TM118
    0x1BC,  # ITEM_TM119
    0x1BD,  # ITEM_TM120
    
    0x0000  # Terminator
]

# COMPLETE TM list - ALL TMs in CFRU (TM01-TM120)
CELADON_TM_SHOP_ITEMS_COMPLETE = CFRU_ALL_TMS

# For backward compatibility - previous curated list
CELADON_TM_SHOP_ITEMS_CURATED = [
    # Essential TMs (most popular and useful)
    0x121,  # TM01 Focus Punch
    0x122,  # TM02 Dragon Claw
    0x123,  # TM03 Water Pulse
    0x124,  # TM04 Calm Mind
    0x126,  # TM06 Toxic
    0x12D,  # TM13 Ice Beam
    0x12E,  # TM14 Blizzard
    0x12F,  # TM15 Hyper Beam
    0x131,  # TM17 Protect
    0x133,  # TM19 Giga Drain
    0x136,  # TM22 Solar Beam
    0x138,  # TM24 Thunderbolt
    0x139,  # TM25 Thunder
    0x13A,  # TM26 Earthquake
    0x13D,  # TM29 Psychic
    0x13E,  # TM30 Shadow Ball
    0x13F,  # TM31 Brick Break
    0x143,  # TM35 Flamethrower
    0x144,  # TM36 Sludge Bomb
    0x146,  # TM38 Fire Blast
    0x148,  # TM40 Aerial Ace
    0x152,  # TM50 Overheat

    # New TMs (CFRU exclusive)
    0x18B,  # TM70 *** CONFIRMED WORKING ***
    0x18C,  # TM71
    0x18D,  # TM72
    0x18E,  # TM73
    0x18F,  # TM74
    0x190,  # TM75
    0x191,  # TM76
    0x192,  # TM77
    0x193,  # TM78
    0x194,  # TM79
    0x195,  # TM80

    0x0000  # Terminator
]

# Use COMPLETE list by default (ALL TMs)
CELADON_TM_SHOP_ITEMS = CELADON_TM_SHOP_ITEMS_COMPLETE

def generate_bytereplacement_entries(shop_id, items):
    """
    Generate bytereplacement entries for a shop modification.
    Returns a list of strings in the format expected by insert.py.
    """
    # Shop offsets from .example project
    shop_offsets = {
        11: 0x16BB74,  # Celadon Department 2F North (TMs)
    }
    
    if shop_id not in shop_offsets:
        print(f"Error: Invalid shop ID {shop_id}")
        return []
    
    shop_offset = shop_offsets[shop_id]
    entries = []
    
    # Add header comment
    entries.append(f"# {CELADON_TM_SHOP_NAME} - Shop ID {shop_id}")
    entries.append(f"# Offset: 0x{shop_offset:06X}")
    entries.append(f"# Items: {len(items)-1} TMs (excluding terminator)")
    entries.append(f"# CFRU TM Shop - Technical Machines ONLY")
    
    # Generate entries for each item
    for i, item_id in enumerate(items):
        offset = shop_offset + (i * 2)
        rom_address = 0x08000000 + offset
        
        # Convert item_id to little endian bytes
        low_byte = item_id & 0xFF
        high_byte = (item_id >> 8) & 0xFF
        
        entries.append(f"{rom_address:08X} {low_byte:02X}")
        entries.append(f"{rom_address+1:08X} {high_byte:02X}")
    
    entries.append("")  # Empty line for separation
    return entries

def modify_celadon_tm_shop():
    """
    Generate bytereplacement entries for Celadon TM shop modification.
    """
    print("=" * 70)
    print("🏬 CELADON DEPARTMENT STORE TM SHOP MODIFICATION")
    print("*** COMPLETE TM COLLECTION - ALL TMs (TM01-TM120) ***")
    print("Based on .example project methodology")
    print("=" * 70)

    print(f"\n🎯 Target: {CELADON_TM_SHOP_NAME}")
    print(f"📍 Shop ID: {CELADON_TM_SHOP_ID}")
    print(f"📍 Offset: 0x{CELADON_TM_SHOP_OFFSET:06X}")
    print(f"🔗 ROM Address: 0x{0x08000000 + CELADON_TM_SHOP_OFFSET:08X}")

    total_tms = len(CELADON_TM_SHOP_ITEMS) - 1  # Exclude terminator
    print(f"\n📦 Items to add: {total_tms} TMs")
    print("🎯 Category: Technical Machines ONLY")
    print("✅ Includes ALL TMs from TM01 to TM120")
    print("✅ Includes TM70 (confirmed working)")
    print("✅ Complete CFRU TM collection")
    print("✅ Maintains shop specialization")

    # Generate bytereplacement entries
    entries = generate_bytereplacement_entries(CELADON_TM_SHOP_ID, CELADON_TM_SHOP_ITEMS)

    # Write to bytereplacement file
    with open('bytereplacement', 'a') as f:
        f.write("\n# === CELADON TM SHOP COMPLETE MODIFICATION (AUTO-GENERATED) ===\n")
        f.write("# ALL TMs from TM01 to TM120 included\n")
        for entry in entries:
            f.write(entry + "\n")

    print(f"\n✅ Generated {total_tms} TM entries")
    print("📝 Entries written to 'bytereplacement' file")

    # Show comprehensive TM breakdown
    print(f"\n📊 COMPLETE TM BREAKDOWN:")

    # Count TMs by range
    tm01_50 = [item for item in CELADON_TM_SHOP_ITEMS[:-1] if 0x121 <= item <= 0x152]
    tm51_70 = [item for item in CELADON_TM_SHOP_ITEMS[:-1] if 376 <= item <= 0x18B]
    tm71_120 = [item for item in CELADON_TM_SHOP_ITEMS[:-1] if 0x18C <= item <= 0x1BD]

    print(f"   • TM01-TM50 (Original FireRed): {len(tm01_50)}")
    print(f"   • TM51-TM70 (CFRU Extended): {len(tm51_70)}")
    print(f"   • TM71-TM120 (CFRU New): {len(tm71_120)}")
    print(f"   • TOTAL TMs: {len(tm01_50) + len(tm51_70) + len(tm71_120)}")

    # Show ranges
    print(f"\n📋 TM RANGES INCLUDED:")
    print(f"   • TM01-TM50: 0x121-0x152 ({len(tm01_50)} TMs)")
    print(f"   • TM51-TM70: 376-0x18B ({len(tm51_70)} TMs)")
    print(f"   • TM71-TM120: 0x18C-0x1BD ({len(tm71_120)} TMs)")

    # Verify completeness
    if total_tms == 120:
        print(f"\n🎉 COMPLETE COLLECTION: All 120 TMs included!")
    else:
        print(f"\n⚠️ PARTIAL COLLECTION: {total_tms}/120 TMs included")

def show_testing_instructions():
    """
    Show instructions for testing the TM shop modification.
    """
    print("\n" + "=" * 70)
    print("🧪 TESTING INSTRUCTIONS")
    print("=" * 70)
    
    print("\n📋 How to test the Celadon TM Shop modification:")
    
    print("\n1️⃣ COMPILE AND INSERT:")
    print("   python scripts/make.py")
    
    print("\n2️⃣ START THE GAME:")
    print("   • Load the generated ROM (test.gba)")
    print("   • Start a new game or load existing save")
    
    print("\n3️⃣ REACH CELADON CITY:")
    print("   • Progress to Celadon City")
    print("   • Enter the Celadon Department Store")
    
    print("\n4️⃣ ACCESS THE TM SHOP:")
    print("   • Go to the 2nd Floor")
    print("   • Talk to the clerk on the NORTH side")
    print("   • This is the TM specialist shop")
    
    print("\n5️⃣ VERIFY COMPLETE TM INVENTORY:")
    print("   • Shop should show ONLY Technical Machines")
    print("   • Look for TM01-TM50 (Original FireRed TMs)")
    print("   • Look for TM51-TM70 (CFRU Extended TMs)")
    print("   • Look for TM71-TM120 (CFRU New TMs)")
    print("   • Verify TM70 (confirmed working)")
    print("   • Verify NO other item types (no potions, stones, etc.)")

    print("\n6️⃣ TEST PURCHASE AND FUNCTIONALITY:")
    print("   • Try buying TM70 to confirm it works")
    print("   • Test purchasing TMs from different ranges")
    print("   • Check that TMs appear in TM pocket")
    print("   • Verify TMs can be used on Pokemon")
    print("   • Test both original and new TMs")

    print("\n✅ EXPECTED RESULTS:")
    print("   • Shop sells ALL 120 TMs (complete collection)")
    print("   • TM01-TM50: Original FireRed TMs")
    print("   • TM51-TM70: CFRU Extended TMs")
    print("   • TM71-TM120: CFRU New TMs")
    print("   • ONLY Technical Machines (no other items)")
    print("   • All TMs functional and usable")

def main():
    """
    Main function - generates Celadon TM shop modification.
    """
    print("CFRU Celadon TM Shop Modifier")
    print("*** COMPLETE TM COLLECTION IMPLEMENTATION ***")
    print("ALL TMs (TM01-TM120) - Shop ID 11 ONLY")
    print("=" * 50)

    # Generate Celadon TM shop modification
    modify_celadon_tm_shop()

    # Show testing instructions
    show_testing_instructions()

    print("\n" + "=" * 70)
    print("🎯 COMPLETE TM COLLECTION IMPLEMENTATION")
    print("=" * 70)

    print("\n✅ WHAT WAS IMPLEMENTED:")
    print("   • Celadon Department Store 2F North TM Shop (Shop ID 11)")
    print("   • ALL 120 Technical Machines (complete CFRU collection)")
    print("   • TM01-TM50: Original FireRed TMs")
    print("   • TM51-TM70: CFRU Extended TMs")
    print("   • TM71-TM120: CFRU New TMs")
    print("   • Maintains exclusive TM specialization")
    print("   • Uses proven bytereplacement methodology")

    print("\n🔧 NEXT STEPS:")
    print("   1. Run 'python scripts/make.py' to compile and insert")
    print("   2. Test the TM shop in Celadon Department Store 2F North")
    print("   3. Verify ALL 120 TMs are available")
    print("   4. Verify ONLY TMs are sold (no other item types)")
    print("   5. Test TMs from all ranges (TM01-50, TM51-70, TM71-120)")
    print("   6. Confirm all TMs work correctly")

    print("\n🎉 THE ULTIMATE TM SHOP:")
    print("   • Complete collection of ALL TMs in CFRU")
    print("   • One-stop shop for every Technical Machine")
    print("   • Perfect for competitive team building")
    print("   • No more hunting for specific TMs!")

if __name__ == "__main__":
    main()
