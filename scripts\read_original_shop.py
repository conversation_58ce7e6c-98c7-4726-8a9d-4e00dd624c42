#!/usr/bin/env python3

"""
Original Shop Reader
Reads the original FireRed ROM to see what's actually at the shop offsets.

This script will help us understand the real structure and identify
why NPCs are being corrupted when we modify shops.
"""

import struct
import os

def read_bytes_at_offset(rom_data, offset, count):
    """Read bytes at a specific offset."""
    if offset + count > len(rom_data):
        return None
    return rom_data[offset:offset+count]

def read_u16_at_offset(rom_data, offset):
    """Read a u16 (little endian) at a specific offset."""
    bytes_data = read_bytes_at_offset(rom_data, offset, 2)
    if bytes_data is None:
        return None
    return struct.unpack('<H', bytes_data)[0]

def read_shop_items(rom_data, shop_offset):
    """
    Read items from a shop at the specified offset.
    Returns a list of item IDs (excluding terminator).
    """
    items = []
    offset = shop_offset
    
    print(f"Reading shop at offset 0x{shop_offset:06X}:")
    
    for i in range(50):  # Max 50 items to prevent infinite loop
        item_id = read_u16_at_offset(rom_data, offset)
        if item_id is None:
            print(f"  ERROR: Cannot read at offset 0x{offset:06X}")
            break
            
        print(f"  Offset 0x{offset:06X}: 0x{item_id:04X} ({item_id})")
        
        if item_id == 0x0000:  # Terminator
            print(f"  Found terminator at offset 0x{offset:06X}")
            break
            
        items.append(item_id)
        offset += 2
    
    return items

def analyze_memory_around_offset(rom_data, offset, before=32, after=32):
    """Analyze memory around a specific offset."""
    start = max(0, offset - before)
    end = min(len(rom_data), offset + after)
    
    print(f"\nMemory analysis around offset 0x{offset:06X}:")
    print(f"Range: 0x{start:06X} to 0x{end:06X}")
    
    for addr in range(start, end, 16):
        line = f"0x{addr:06X}: "
        hex_part = ""
        ascii_part = ""
        
        for i in range(16):
            if addr + i < end and addr + i < len(rom_data):
                byte = rom_data[addr + i]
                hex_part += f"{byte:02X} "
                ascii_part += chr(byte) if 32 <= byte <= 126 else "."
            else:
                hex_part += "   "
                ascii_part += " "
        
        print(f"{line}{hex_part} | {ascii_part}")

def main():
    """Main function."""
    rom_file = "BPRE0.gba"
    
    if not os.path.exists(rom_file):
        print(f"Error: ROM file '{rom_file}' not found!")
        print("Please ensure the FireRed U 1.0 ROM is in the current directory.")
        return
    
    with open(rom_file, 'rb') as f:
        rom_data = f.read()
    
    print("=" * 80)
    print("🔍 ORIGINAL FIRERED ROM SHOP ANALYSIS")
    print("Reading actual data from original ROM")
    print("=" * 80)
    
    # Celadon TM Shop (Shop ID 11)
    celadon_tm_offset = 0x16BB74
    
    print(f"\n🎯 CELADON TM SHOP (Shop ID 11)")
    print(f"Offset: 0x{celadon_tm_offset:06X}")
    
    # Read the shop items
    original_items = read_shop_items(rom_data, celadon_tm_offset)
    
    print(f"\nFound {len(original_items)} items in original shop:")
    for i, item_id in enumerate(original_items):
        print(f"  Item {i+1}: 0x{item_id:04X} ({item_id})")
    
    # Analyze memory around the shop
    analyze_memory_around_offset(rom_data, celadon_tm_offset, 64, 128)
    
    # Check the next shop (Shop ID 12)
    next_shop_offset = 0x16BC30
    print(f"\n🏪 NEXT SHOP (Shop ID 12)")
    print(f"Offset: 0x{next_shop_offset:06X}")
    
    distance = next_shop_offset - celadon_tm_offset
    print(f"Distance between shops: {distance} bytes ({distance//2} u16 values)")
    
    # Read next shop items
    next_shop_items = read_shop_items(rom_data, next_shop_offset)
    
    print(f"\nFound {len(next_shop_items)} items in next shop:")
    for i, item_id in enumerate(next_shop_items):
        print(f"  Item {i+1}: 0x{item_id:04X} ({item_id})")
    
    # Calculate safe boundary
    max_items = (distance // 2) - 1  # -1 for terminator
    print(f"\n📊 BOUNDARY ANALYSIS:")
    print(f"  Available space: {distance} bytes")
    print(f"  Max items (theoretical): {max_items}")
    print(f"  Current items: {len(original_items)}")
    print(f"  Remaining space: {max_items - len(original_items)} items")
    
    if max_items >= 120:
        print(f"  ✅ Can fit all 120 TMs")
    elif max_items >= 50:
        print(f"  ⚠️  Can fit {max_items} TMs (partial)")
    else:
        print(f"  ❌ Only {max_items} TMs fit (very limited)")
    
    # Check what's between the shops
    print(f"\n🔍 DATA BETWEEN SHOPS:")
    between_start = celadon_tm_offset + (len(original_items) + 1) * 2  # +1 for terminator
    between_end = next_shop_offset
    
    if between_end > between_start:
        print(f"Gap from 0x{between_start:06X} to 0x{between_end:06X} ({between_end - between_start} bytes)")
        analyze_memory_around_offset(rom_data, between_start, 0, between_end - between_start)
    else:
        print("No gap between shops - they are adjacent!")

if __name__ == "__main__":
    main()
