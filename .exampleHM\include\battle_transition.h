#ifndef GUARD_BATTLE_TRANSITION_H
#define GUARD_BATTLE_TRANSITION_H

#include "global.h"

enum
{
    MUGSHOT_LORELEI,
    MUGSHOT_BRUNO,
    M<PERSON>GSHOT_AGATHA,
    MUGSHOT_LANCE,
    M<PERSON>GSHOT_BLUE,
    <PERSON><PERSON><PERSON>HOTS_COUNT
};

enum {
    B_TRANSITION_BLUR,
    B_TRANSITION_SWIRL,
    B_TRANSITION_SHUFFLE,
    B_TRANSITION_BIG_POKEBALL,
    B_TRANSITION_POKEBALLS_TRAIL,
    B_TRANSITION_CLOCKWISE_WIPE,
    B_TRANSITION_RIPPLE,
    B_TRANSITION_WAVE,
    B_TRANSITION_SLICE, 
    B_TRANSITION_WHITE_BARS_FADE,
    B_TRANSITION_GRID_SQUARES,
    B_TRANSITION_ANGLED_WIPES,
    B_TRANSITION_LORELEI,
    B_TRANSITION_BRUNO,
    B_TRA<PERSON><PERSON><PERSON>_AGATHA,
    B_TRANSITION_LANCE,
    B_TRANSITION_BLUE,
    B_TRANSITION_SPIRAL,
    B_TRANSITION_COUNT
};

extern const struct SpritePalette gSpritePalette_Pokeball;

bool8 IsBattleTransitionDone(void);
void BattleTransition_StartOnField(u8 transitionId);
bool8 FldEff_PokeballTrail(void);

#endif // GUARD_BATTLE_TRANSITION_H
