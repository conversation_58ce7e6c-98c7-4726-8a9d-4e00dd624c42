package com.dabomstew.pkrandom.constants;

/*----------------------------------------------------------------------------*/
/*--  Gen2Items.java - defines an index number constant for every item in   --*/
/*--                   the Generation 2 games.                              --*/
/*--                                                                        --*/
/*--  Part of "Universal Pokemon Randomizer ZX" by the UPR-ZX team          --*/
/*--  Pokemon and any associated names and the like are                     --*/
/*--  trademark and (C) Nintendo 1996-2020.                                 --*/
/*--                                                                        --*/
/*--  The custom code written here is licensed under the terms of the GPL:  --*/
/*--                                                                        --*/
/*--  This program is free software: you can redistribute it and/or modify  --*/
/*--  it under the terms of the GNU General Public License as published by  --*/
/*--  the Free Software Foundation, either version 3 of the License, or     --*/
/*--  (at your option) any later version.                                   --*/
/*--                                                                        --*/
/*--  This program is distributed in the hope that it will be useful,       --*/
/*--  but WITHOUT ANY WARRANTY; without even the implied warranty of        --*/
/*--  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the          --*/
/*--  GNU General Public License for more details.                          --*/
/*--                                                                        --*/
/*--  You should have received a copy of the GNU General Public License     --*/
/*--  along with this program. If not, see <http://www.gnu.org/licenses/>.  --*/
/*----------------------------------------------------------------------------*/

public class Gen2Items {
    // https://bulbapedia.bulbagarden.net/wiki/List_of_items_by_index_number_(Generation_II)
    public static final int nothing = 0;
    public static final int masterBall = 1;
    public static final int ultraBall = 2;
    public static final int brightPowder = 3;
    public static final int greatBall = 4;
    public static final int pokeBall = 5;
    public static final int terusama6 = 6;
    public static final int bicycle = 7;
    public static final int moonStone = 8;
    public static final int antidote = 9;
    public static final int burnHeal = 10;
    public static final int iceHeal = 11;
    public static final int awakening = 12;
    public static final int parlyzHeal = 13;
    public static final int fullRestore = 14;
    public static final int maxPotion = 15;
    public static final int hyperPotion = 16;
    public static final int superPotion = 17;
    public static final int potion = 18;
    public static final int escapeRope = 19;
    public static final int repel = 20;
    public static final int maxElixer = 21;
    public static final int fireStone = 22;
    public static final int thunderstone = 23;
    public static final int waterStone = 24;
    public static final int terusama25 = 25;
    public static final int hpUp = 26;
    public static final int protein = 27;
    public static final int iron = 28;
    public static final int carbos = 29;
    public static final int luckyPunch = 30;
    public static final int calcium = 31;
    public static final int rareCandy = 32;
    public static final int xAccuracy = 33;
    public static final int leafStone = 34;
    public static final int metalPowder = 35;
    public static final int nugget = 36;
    public static final int pokeDoll = 37;
    public static final int fullHeal = 38;
    public static final int revive = 39;
    public static final int maxRevive = 40;
    public static final int guardSpec = 41;
    public static final int superRepel = 42;
    public static final int maxRepel = 43;
    public static final int direHit = 44;
    public static final int terusama45 = 45;
    public static final int freshWater = 46;
    public static final int sodaPop = 47;
    public static final int lemonade = 48;
    public static final int xAttack = 49;
    public static final int terusama50 = 50;
    public static final int xDefend = 51;
    public static final int xSpeed = 52;
    public static final int xSpecial = 53;
    public static final int coinCase = 54;
    public static final int itemfinder = 55;
    public static final int terusama56 = 56;
    public static final int expShare = 57;
    public static final int oldRod = 58;
    public static final int goodRod = 59;
    public static final int silverLeaf = 60;
    public static final int superRod = 61;
    public static final int ppUp = 62;
    public static final int ether = 63;
    public static final int maxEther = 64;
    public static final int elixer = 65;
    public static final int redScale = 66;
    public static final int secretPotion = 67;
    public static final int ssTicket = 68;
    public static final int mysteryEgg = 69;
    public static final int clearBell = 70; // exclusive to Crystal
    public static final int silverWing = 71;
    public static final int moomooMilk = 72;
    public static final int quickClaw = 73;
    public static final int psnCureBerry = 74;
    public static final int goldLeaf = 75;
    public static final int softSand = 76;
    public static final int sharpBeak = 77;
    public static final int przCureBerry = 78;
    public static final int burntBerry = 79;
    public static final int iceBerry = 80;
    public static final int poisonBarb = 81;
    public static final int kingsRock = 82;
    public static final int bitterBerry = 83;
    public static final int mintBerry = 84;
    public static final int redApricorn = 85;
    public static final int tinyMushroom = 86;
    public static final int bigMushroom = 87;
    public static final int silverPowder = 88;
    public static final int bluApricorn = 89;
    public static final int terusama90 = 90;
    public static final int amuletCoin = 91;
    public static final int ylwApricorn = 92;
    public static final int grnApricorn = 93;
    public static final int cleanseTag = 94;
    public static final int mysticWater = 95;
    public static final int twistedSpoon = 96;
    public static final int whtApricorn = 97;
    public static final int blackbelt = 98;
    public static final int blkApricorn = 99;
    public static final int terusama100 = 100;
    public static final int pnkApricorn = 101;
    public static final int blackGlasses = 102;
    public static final int slowpokeTail = 103;
    public static final int pinkBow = 104;
    public static final int stick = 105;
    public static final int smokeBall = 106;
    public static final int neverMeltIce = 107;
    public static final int magnet = 108;
    public static final int miracleBerry = 109;
    public static final int pearl = 110;
    public static final int bigPearl = 111;
    public static final int everstone = 112;
    public static final int spellTag = 113;
    public static final int rageCandyBar = 114;
    public static final int gsBall = 115; // exclusive to Crystal
    public static final int blueCard = 116; // exclusive to Crystal
    public static final int miracleSeed = 117;
    public static final int thickClub = 118;
    public static final int focusBand = 119;
    public static final int terusama120 = 120;
    public static final int energyPowder = 121;
    public static final int energyRoot = 122;
    public static final int healPowder = 123;
    public static final int revivalHerb = 124;
    public static final int hardStone = 125;
    public static final int luckyEgg = 126;
    public static final int cardKey = 127;
    public static final int machinePart = 128;
    public static final int eggTicket = 129; // exclusive to Crystal
    public static final int lostItem = 130;
    public static final int stardust = 131;
    public static final int starPiece = 132;
    public static final int basementKey = 133;
    public static final int pass = 134;
    public static final int terusama135 = 135;
    public static final int terusama136 = 136;
    public static final int terusama137 = 137;
    public static final int charcoal = 138;
    public static final int berryJuice = 139;
    public static final int scopeLens = 140;
    public static final int terusama141 = 141;
    public static final int terusama142 = 142;
    public static final int metalCoat = 143;
    public static final int dragonFang = 144;
    public static final int terusama145 = 145;
    public static final int leftovers = 146;
    public static final int terusama147 = 147;
    public static final int terusama148 = 148;
    public static final int terusama149 = 149;
    public static final int mysteryBerry = 150;
    public static final int dragonScale = 151;
    public static final int berserkGene = 152;
    public static final int terusama153 = 153;
    public static final int terusama154 = 154;
    public static final int terusama155 = 155;
    public static final int sacredAsh = 156;
    public static final int heavyBall = 157;
    public static final int flowerMail = 158;
    public static final int levelBall = 159;
    public static final int lureBall = 160;
    public static final int fastBall = 161;
    public static final int terusama162 = 162;
    public static final int lightBall = 163;
    public static final int friendBall = 164;
    public static final int moonBall = 165;
    public static final int loveBall = 166;
    public static final int normalBox = 167;
    public static final int gorgeousBox = 168;
    public static final int sunStone = 169;
    public static final int polkadotBow = 170;
    public static final int terusama171 = 171;
    public static final int upGrade = 172;
    public static final int berry = 173;
    public static final int goldBerry = 174;
    public static final int squirtBottle = 175;
    public static final int terusama176 = 176;
    public static final int parkBall = 177;
    public static final int rainbowWing = 178;
    public static final int terusama179 = 179;
    public static final int brickPiece = 180;
    public static final int surfMail = 181;
    public static final int litebluemail = 182;
    public static final int portraitmail = 183;
    public static final int lovelyMail = 184;
    public static final int eonMail = 185;
    public static final int morphMail = 186;
    public static final int blueskyMail = 187;
    public static final int musicMail = 188;
    public static final int mirageMail = 189;
    public static final int terusama190 = 190;
    public static final int tm01 = 191;
    public static final int tm02 = 192;
    public static final int tm03 = 193;
    public static final int tm04 = 194;
    public static final int tm04Unused = 195;
    public static final int tm05 = 196;
    public static final int tm06 = 197;
    public static final int tm07 = 198;
    public static final int tm08 = 199;
    public static final int tm09 = 200;
    public static final int tm10 = 201;
    public static final int tm11 = 202;
    public static final int tm12 = 203;
    public static final int tm13 = 204;
    public static final int tm14 = 205;
    public static final int tm15 = 206;
    public static final int tm16 = 207;
    public static final int tm17 = 208;
    public static final int tm18 = 209;
    public static final int tm19 = 210;
    public static final int tm20 = 211;
    public static final int tm21 = 212;
    public static final int tm22 = 213;
    public static final int tm23 = 214;
    public static final int tm24 = 215;
    public static final int tm25 = 216;
    public static final int tm26 = 217;
    public static final int tm27 = 218;
    public static final int tm28 = 219;
    public static final int tm28Unused = 220;
    public static final int tm29 = 221;
    public static final int tm30 = 222;
    public static final int tm31 = 223;
    public static final int tm32 = 224;
    public static final int tm33 = 225;
    public static final int tm34 = 226;
    public static final int tm35 = 227;
    public static final int tm36 = 228;
    public static final int tm37 = 229;
    public static final int tm38 = 230;
    public static final int tm39 = 231;
    public static final int tm40 = 232;
    public static final int tm41 = 233;
    public static final int tm42 = 234;
    public static final int tm43 = 235;
    public static final int tm44 = 236;
    public static final int tm45 = 237;
    public static final int tm46 = 238;
    public static final int tm47 = 239;
    public static final int tm48 = 240;
    public static final int tm49 = 241;
    public static final int tm50 = 242;
    public static final int hm01 = 243;
    public static final int hm02 = 244;
    public static final int hm03 = 245;
    public static final int hm04 = 246;
    public static final int hm05 = 247;
    public static final int hm06 = 248;
    public static final int hm07 = 249;
    public static final int hm08 = 250;
    public static final int hm09 = 251;
    public static final int hm10 = 252;
    public static final int hm11 = 253;
    public static final int hm12 = 254;
    public static final int cancel = 255;
}
