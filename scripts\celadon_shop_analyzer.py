#!/usr/bin/env python3

"""
Celadon Department Store Shop Analyzer
Based on .example project methodology for direct ROM modification.

This script analyzes the Celadon Department Store shops and identifies
what items each shop sells based on the FireRed U 1.0 offsets.
"""

# FireRed U 1.0 Celadon Shop Offsets (from .example project)
CELADON_SHOP_OFFSETS = {
    10: 0x16BB38,  # Celadon Department 2F South
    11: 0x16BB74,  # Celadon Department 2F North (TMs)
    12: 0x16BC30,  # Celadon Department 4F *** MAIN GAME ***
    13: 0x16BC84,  # Celadon Department 5F South *** MAIN GAME ***
    14: 0x16BC98,  # Celadon Department 5F North *** MAIN GAME ***
}

# Shop names for reference
CELADON_SHOP_NAMES = {
    10: "Celadon Department 2F South",
    11: "Celadon Department 2F North (TMs)",
    12: "Celadon Department 4F",
    13: "Celadon Department 5F South",
    14: "Celadon Department 5F North",
}

# Shop purposes based on .example analysis
CELADON_SHOP_PURPOSES = {
    10: {
        "purpose": "General Items",
        "description": "Basic healing items and supplies",
        "category": "Healing & Supplies",
        "expected_items": "Potions, Antidotes, Status healers, Repels"
    },
    11: {
        "purpose": "Technical Machines",
        "description": "Various TMs for sale",
        "category": "TM Shop",
        "expected_items": "Various Technical Machines"
    },
    12: {
        "purpose": "Vitamins",
        "description": "Stat-boosting vitamins (HP Up, Protein, Iron, etc.)",
        "category": "Stat Enhancement",
        "expected_items": "HP Up, Protein, Iron, Carbos, Calcium, Zinc"
    },
    13: {
        "purpose": "Special Items",
        "description": "Battle items and special supplies (X items, Guard Spec, etc.)",
        "category": "Battle Enhancement",
        "expected_items": "X Attack, X Defense, X Speed, Guard Spec, Dire Hit"
    },
    14: {
        "purpose": "Evolution Items",
        "description": "Evolution stones and special evolution items",
        "category": "Evolution Items",
        "expected_items": "Fire Stone, Water Stone, Thunder Stone, Leaf Stone"
    }
}

def analyze_celadon_shops():
    """
    Analyze Celadon Department Store shops based on .example project data.
    """
    print("=" * 70)
    print("🏬 CELADON DEPARTMENT STORE ANALYSIS")
    print("Based on .example project for FireRed U 1.0")
    print("=" * 70)
    
    for shop_id in sorted(CELADON_SHOP_OFFSETS.keys()):
        offset = CELADON_SHOP_OFFSETS[shop_id]
        name = CELADON_SHOP_NAMES[shop_id]
        purpose_data = CELADON_SHOP_PURPOSES[shop_id]
        
        print(f"\n🏪 Shop ID {shop_id}: {name}")
        print(f"   📍 Offset: 0x{offset:06X}")
        print(f"   🔗 ROM Address: 0x{0x08000000 + offset:08X}")
        print(f"   🎯 Purpose: {purpose_data['purpose']}")
        print(f"   📂 Category: {purpose_data['category']}")
        print(f"   📝 Description: {purpose_data['description']}")
        print(f"   🛍️ Expected Items: {purpose_data['expected_items']}")
    
    print("\n" + "=" * 70)
    print("🔍 SHOP SPECIALIZATION SUMMARY")
    print("=" * 70)
    
    specializations = [
        ("🏥", "2F South (Shop 10)", "HEALING & SUPPLIES", "Basic survival items"),
        ("💿", "2F North (Shop 11)", "TM SHOP", "Move learning items"),
        ("💪", "4F (Shop 12)", "VITAMINS *** MAIN GAME ***", "Stat enhancement items"),
        ("⚔️", "5F South (Shop 13)", "BATTLE ITEMS *** MAIN GAME ***", "Battle enhancement items"),
        ("🔮", "5F North (Shop 14)", "EVOLUTION ITEMS *** MAIN GAME ***", "Evolution stones and items")
    ]
    
    for icon, shop, category, description in specializations:
        print(f"\n{icon} {shop} - {category}")
        print(f"   Category: {description}")
    
    print("\n" + "=" * 70)
    print("💡 MODIFICATION RECOMMENDATIONS FOR CFRU")
    print("=" * 70)
    
    recommendations = [
        ("Shop 10 (General)", "Add new healing items and supplies"),
        ("Shop 11 (TM)", "Add new TMs not normally obtainable"),
        ("Shop 12 (Vitamins)", "Add new stat items (Bottle Caps, Ability items)"),
        ("Shop 13 (Battle)", "Add new battle items (Dynamax items, etc.)"),
        ("Shop 14 (Evolution)", "Add new evolution stones (Shiny, Dusk, Dawn, Ice)")
    ]
    
    for shop, recommendation in recommendations:
        print(f"   • {shop}: {recommendation}")

def show_shop_modification_potential():
    """
    Show what modifications could be made to each Celadon shop.
    """
    print("\n" + "=" * 70)
    print("🛠️ SHOP MODIFICATION POTENTIAL")
    print("=" * 70)
    
    modifications = {
        10: {
            "current": "Basic healing items",
            "additions": [
                "New healing items from CFRU",
                "Special berries",
                "Status condition items",
                "Enhanced repels"
            ]
        },
        11: {
            "current": "Limited TM selection",
            "additions": [
                "TM70 (confirmed working)",
                "New TMs not obtainable normally",
                "Move tutoring items",
                "TR equivalents"
            ]
        },
        12: {
            "current": "Basic vitamins (HP Up, Protein, etc.)",
            "additions": [
                "Ability Capsule",
                "Ability Patch", 
                "Bottle Caps",
                "Nature mints"
            ]
        },
        13: {
            "current": "X items and battle enhancers",
            "additions": [
                "Dynamax Candy",
                "Z-Move items",
                "Mega Evolution items",
                "Advanced battle items"
            ]
        },
        14: {
            "current": "Basic evolution stones",
            "additions": [
                "Shiny Stone",
                "Dusk Stone", 
                "Dawn Stone",
                "Ice Stone",
                "Link Cable",
                "Special evolution items (Protector, Electirizer, etc.)"
            ]
        }
    }
    
    for shop_id, data in modifications.items():
        shop_name = CELADON_SHOP_NAMES[shop_id]
        print(f"\n🏪 {shop_name}")
        print(f"   📦 Current: {data['current']}")
        print(f"   ➕ Potential additions:")
        for addition in data['additions']:
            print(f"      • {addition}")

def main():
    """
    Main function - analyzes Celadon Department Store shops.
    """
    print("CFRU Celadon Shop Analyzer")
    print("Based on .example project methodology")
    print("=" * 50)
    
    # Analyze Celadon shops
    analyze_celadon_shops()
    
    # Show modification potential
    show_shop_modification_potential()
    
    print("\n" + "=" * 70)
    print("📋 SUMMARY")
    print("=" * 70)
    
    print("\n✅ Celadon Department Store has 5 specialized shops:")
    print("   • Shop 10: General healing and supplies")
    print("   • Shop 11: Technical Machines (TMs)")
    print("   • Shop 12: Vitamins and stat enhancers")
    print("   • Shop 13: Battle items and enhancers")
    print("   • Shop 14: Evolution stones and items")
    
    print("\n🎯 Each shop has a clear specialization that can be enhanced")
    print("   with CFRU's additional items while maintaining the original theme.")
    
    print("\n🔧 Implementation:")
    print("   • Use the same bytereplacement methodology as Viridian shop")
    print("   • Respect each shop's original purpose and theme")
    print("   • Add complementary items that enhance the shop's category")

if __name__ == "__main__":
    main()
