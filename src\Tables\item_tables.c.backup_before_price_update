#include "../defines.h"
#include "../../include/battle.h"
#include "../../include/global.h"
#include "../../include/item.h"
#include "../../include/easy_text.h"
#include "../../include/constants/hold_effects.h"
#include "../../include/constants/items.h"
#include "../../include/constants/item_effects.h"
#include "../../include/new/item.h"
#include "../../include/new/item_effects.h"
#include "../../include/new/pickup_items.h"
#include "../../include/new/item_tables.h"
#include "../../include/new/Vanilla_functions.h"
/*
item_tables.c
	set up item data tables
		-pickup lists
		-fling
		-ban lists

tables to edit:
	sPickupCommonItems
	sPickupRareItems
	gFlingTable
	gConsumableItemEffects
	gItemsByType

*/

const item_t sPickupCommonItems[PU_NUM_COMMON_ITEMS] =
{
#ifndef UNBOUND //Modify this
	ITEM_POTION,
	ITEM_ANTIDOTE,
	ITEM_SUPER_POTION,
	ITEM_GREAT_BALL,
	ITEM_REPEL,
	ITEM_ESCAPE_ROPE,
	ITEM_FULL_HEAL,
	ITEM_HYPER_POTION,
	ITEM_ULTRA_BALL,
	ITEM_REVIVE,
	ITEM_RARE_CANDY,
	ITEM_SUN_STONE,
	ITEM_MOON_STONE,
	ITEM_HEART_SCALE,
	ITEM_FULL_RESTORE,
	ITEM_MAX_REVIVE,
	ITEM_PP_UP,
	ITEM_MAX_ELIXIR,

#else //For Pokemon Unbound
	ITEM_POTION,
	ITEM_ANTIDOTE,
	ITEM_SUPER_POTION,
	ITEM_GREAT_BALL,
	ITEM_REPEL,
	ITEM_ESCAPE_ROPE,
	ITEM_FULL_HEAL,
	ITEM_HYPER_POTION,
	ITEM_ULTRA_BALL,
	ITEM_REVIVE,
	ITEM_RARE_CANDY,
	ITEM_SHINY_STONE,
	ITEM_DUSK_STONE,
	ITEM_HEART_SCALE,
	ITEM_FULL_RESTORE,
	ITEM_MAX_REVIVE,
	ITEM_PP_UP,
	ITEM_MAX_ELIXIR,
#endif
};

const item_t sPickupRareItems[PU_NUM_RARE_ITEMS] =
{
#ifndef UNBOUND //Modify this
	ITEM_HYPER_POTION,
	ITEM_NUGGET,
	ITEM_KINGS_ROCK,
	ITEM_FULL_RESTORE,
	ITEM_ETHER,
	ITEM_IRON_BALL,
	ITEM_DESTINY_KNOT,
	ITEM_ELIXIR,
	ITEM_DESTINY_KNOT,
	ITEM_LEFTOVERS,
	ITEM_DESTINY_KNOT,

#else //For Pokemon Unbound
	ITEM_HYPER_POTION,    //Lv.  1 - 10
	ITEM_NUGGET,          //Lv.  1 - 20
	ITEM_ETHER,           //Lv. 11 - 30
	ITEM_DESTINY_KNOT,    //Lv. 21 - 40
	ITEM_FULL_RESTORE,    //Lv. 31 - 50
	ITEM_ELIXIR,          //Lv. 41 - 60
	ITEM_KINGS_ROCK,      //Lv. 51 - 70
	ITEM_BOTTLE_CAP,      //Lv. 61 - 80
	ITEM_BIG_NUGGET,      //Lv. 71 - 90
	ITEM_LEFTOVERS,       //Lv. 81 - 100
	ITEM_ABILITY_CAPSULE, //Lv. 91 - 100
#endif
};

const u32 pickup_common_item_ceilings[PU_COMMON_PER_ROW] =
{
	19661, 26214, 32768, 39322, 45875, 52429, 58982, 61604, 64225
};

const u32 pickup_rare_item_ceilings[PU_RARE_PER_ROW] =
{
	64881, 65536
};

//Any values not listed default to BP 30
const struct FlingStruct gFlingTable[ITEMS_COUNT] =
{
	[ITEM_CHERI_BERRY] = {10, 0},
	[ITEM_CHESTO_BERRY] = {10, 0},
	[ITEM_PECHA_BERRY] = {10, 0},
	[ITEM_RAWST_BERRY] = {10, 0},
	[ITEM_ASPEAR_BERRY] = {10, 0},
	[ITEM_LEPPA_BERRY] = {10, 0},
	[ITEM_ORAN_BERRY] = {10, 0},
	[ITEM_PERSIM_BERRY] = {10, 0},
	[ITEM_LUM_BERRY] = {10, 0},
	[ITEM_SITRUS_BERRY] = {10, 0},
	[ITEM_FIGY_BERRY] = {10, 0},
	[ITEM_WIKI_BERRY] = {10, 0},
	[ITEM_MAGO_BERRY] = {10, 0},
	[ITEM_AGUAV_BERRY] = {10, 0},
	[ITEM_IAPAPA_BERRY] = {10, 0},
	[ITEM_RAZZ_BERRY] = {10, 0},
	[ITEM_BLUK_BERRY] = {10, 0},
	[ITEM_NANAB_BERRY] = {10, 0},
	[ITEM_WEPEAR_BERRY] = {10, 0},
	[ITEM_PINAP_BERRY] = {10, 0},
	[ITEM_POMEG_BERRY] = {10, 0},
	[ITEM_KELPSY_BERRY] = {10, 0},
	[ITEM_QUALOT_BERRY] = {10, 0},
	[ITEM_HONDEW_BERRY] = {10, 0},
	[ITEM_GREPA_BERRY] = {10, 0},
	[ITEM_TAMATO_BERRY] = {10, 0},
	[ITEM_CORNN_BERRY] = {10, 0},
	[ITEM_MAGOST_BERRY] = {10, 0},
	[ITEM_RABUTA_BERRY] = {10, 0},
	[ITEM_NOMEL_BERRY] = {10, 0},
	[ITEM_SPELON_BERRY] = {10, 0},
	[ITEM_PAMTRE_BERRY] = {10, 0},
	[ITEM_WATMEL_BERRY] = {10, 0},
	[ITEM_DURIN_BERRY] = {10, 0},
	[ITEM_BELUE_BERRY] = {10, 0},
	[ITEM_LIECHI_BERRY] = {10, 0},
	[ITEM_GANLON_BERRY] = {10, 0},
	[ITEM_SALAC_BERRY] = {10, 0},
	[ITEM_PETAYA_BERRY] = {10, 0},
	[ITEM_APICOT_BERRY] = {10, 0},
	[ITEM_LANSAT_BERRY] = {10, 0},
	[ITEM_STARF_BERRY] = {10, 0},
	[ITEM_ENIGMA_BERRY] = {10, 0},
	[ITEM_OCCA_BERRY] = {10, 0},
	[ITEM_PASSHO_BERRY] = {10, 0},
	[ITEM_WACAN_BERRY] = {10, 0},
	[ITEM_RINDO_BERRY] = {10, 0},
	[ITEM_YACHE_BERRY] = {10, 0},
	[ITEM_CHOPLE_BERRY] = {10, 0},
	[ITEM_KEBIA_BERRY] = {10, 0},
	[ITEM_SHUCA_BERRY] = {10, 0},
	[ITEM_COBA_BERRY] = {10, 0},
	[ITEM_PAYAPA_BERRY] = {10, 0},
	[ITEM_TANGA_BERRY] = {10, 0},
	[ITEM_CHARTI_BERRY] = {10, 0},
	[ITEM_KASIB_BERRY] = {10, 0},
	[ITEM_HABAN_BERRY] = {10, 0},
	[ITEM_COLBUR_BERRY] = {10, 0},
	[ITEM_BABIRI_BERRY] = {10, 0},
	[ITEM_CHILAN_BERRY] = {10, 0},
	[ITEM_MICLE_BERRY] = {10, 0},
	[ITEM_CUSTAP_BERRY] = {10, 0},
	[ITEM_JABOCA_BERRY] = {10, 0},
	[ITEM_ROWAP_BERRY] = {10, 0},
	[ITEM_ROSELI_BERRY] = {10, 0},
	[ITEM_KEE_BERRY] = {10, 0},
	[ITEM_MARANGA_BERRY] = {10, 0},
	[ITEM_SEA_INCENSE] = {10, 0},
	[ITEM_LAX_INCENSE] = {10, 0},
	[ITEM_LUCK_INCENSE] = {10, 0},
	[ITEM_FULL_INCENSE] = {10, 0},
	[ITEM_ODD_INCENSE] = {10, 0},
	[ITEM_PURE_INCENSE] = {10, 0},
	[ITEM_ROCK_INCENSE] = {10, 0},
	[ITEM_ROSE_INCENSE] = {10, 0},
	[ITEM_WAVE_INCENSE] = {10, 0},
	[ITEM_AIR_BALLOON] = {10, 0},
	[ITEM_BIG_ROOT] = {10, 0},
	[ITEM_BRIGHT_POWDER] = {10, 0},
	[ITEM_CHOICE_BAND] = {10, 0},
	[ITEM_CHOICE_SCARF] = {10, 0},
	[ITEM_CHOICE_SPECS] = {10, 0},
	[ITEM_DESTINY_KNOT] = {10, 0},
	[ITEM_ELECTRIC_SEED] = {10, 0},
	[ITEM_EXPERT_BELT] = {10, 0},
	[ITEM_FOCUS_BAND] = {10, 0},
	[ITEM_FOCUS_SASH] = {10, 0},
	[ITEM_GRASSY_SEED] = {10, 0},
	[ITEM_LAGGING_TAIL] = {10, 0},
	[ITEM_LEFTOVERS] = {10, 0},
	[ITEM_MENTAL_HERB] = {10, 0},
	[ITEM_METAL_POWDER] = {10, 0},
	[ITEM_MISTY_SEED] = {10, 0},
	[ITEM_MUSCLE_BAND] = {10, 0},
	[ITEM_PINK_NECTAR] = {10, 0},
	[ITEM_POWER_HERB] = {10, 0},
	[ITEM_PSYCHIC_SEED] = {10, 0},
	[ITEM_PURPLE_NECTAR] = {10, 0},
	[ITEM_QUICK_POWDER] = {10, 0},
	[ITEM_REAPER_CLOTH] = {10, 0},
	[ITEM_RED_CARD] = {10, 0},
	[ITEM_RED_NECTAR] = {10, 0},
	[ITEM_RING_TARGET] = {10, 0},
	[ITEM_SHED_SHELL] = {10, 0},
	[ITEM_SILK_SCARF] = {10, 0},
	[ITEM_SILVER_POWDER] = {10, 0},
	[ITEM_SMOOTH_ROCK] = {10, 0},
	[ITEM_SOFT_SAND] = {10, 0},
	[ITEM_SOOTHE_BELL] = {10, 0},
	[ITEM_WHITE_HERB] = {10, 0},
	[ITEM_WIDE_LENS] = {10, 0},
	[ITEM_WISE_GLASSES] = {10, 0},
	[ITEM_YELLOW_NECTAR] = {10, 0},
	[ITEM_ZOOM_LENS] = {10, 0},
	[ITEM_HEALTH_WING] = {20, 0},
	[ITEM_MUSCLE_WING] = {20, 0},
	[ITEM_RESIST_WING] = {20, 0},
	[ITEM_GENIUS_WING] = {20, 0},
	[ITEM_CLEVER_WING] = {20, 0},
	[ITEM_SWIFT_WING] = {20, 0},
	[ITEM_PRETTY_WING] = {20, 0},
	[ITEM_ETHER] = {30, 0},
	[ITEM_ELIXIR] = {30, 0},
	[ITEM_MAX_ETHER] = {30, 0},
	[ITEM_MAX_ELIXIR] = {30, 0},
	[ITEM_ABSORB_BULB] = {30, 0},
	[ITEM_ADRENALINE_ORB] = {30, 0},
	[ITEM_AMULET_COIN] = {30, 0},
	[ITEM_BALM_MUSHROOM] = {30, 0},
	[ITEM_BERRY_JUICE] = {30, 0},
	[ITEM_BIG_MALASADA] = {30, 0},
	[ITEM_BIG_MUSHROOM] = {30, 0},
	[ITEM_BIG_NUGGET] = {30, 0},
	[ITEM_BIG_PEARL] = {30, 0},
	[ITEM_BINDING_BAND] = {30, 0},
	[ITEM_BLACK_BELT] = {30, 0},
	[ITEM_BLACK_GLASSES] = {30, 0},
	[ITEM_BLACK_SLUDGE] = {30, 0},
	[ITEM_BOTTLE_CAP] = {30, 0},
	[ITEM_CASTELIACONE] = {30, 0},
	[ITEM_CELL_BATTERY] = {30, 0},
	[ITEM_CHARCOAL] = {30, 0},
	[ITEM_CLEANSE_TAG] = {30, 0},
	[ITEM_COMET_SHARD] = {30, 0},
	[ITEM_DEEP_SEA_SCALE] = {30, 0},
	[ITEM_DRAGON_SCALE] = {30, 0},
	[ITEM_EJECT_BUTTON] = {30, 0},
	[ITEM_ESCAPE_ROPE] = {30, 0},
	[ITEM_EVERSTONE] = {30, 0},
	[ITEM_EXP_SHARE] = {30, 0},
	[ITEM_FIRE_STONE] = {30, 0},
	[ITEM_FLAME_ORB] = {30, MOVE_EFFECT_BURN},
	[ITEM_FLOAT_STONE] = {30, 0},
	[ITEM_FLUFFY_TAIL] = {30, 0},
	[ITEM_GOLD_BOTTLE_CAP] = {30, 0},
	[ITEM_HEART_SCALE] = {30, 0},
	[ITEM_HONEY] = {30, 0},
	[ITEM_ICE_STONE] = {30, 0},
	[ITEM_KINGS_ROCK] = {30, MOVE_EFFECT_FLINCH},
	[ITEM_LAVA_COOKIE] = {30, 0},
	[ITEM_LEAF_STONE] = {30, 0},
	[ITEM_LIFE_ORB] = {30, 0},
	[ITEM_LIGHT_BALL] = {30, MOVE_EFFECT_PARALYSIS},
	[ITEM_LIGHT_CLAY] = {30, 0},
	[ITEM_LUCKY_EGG] = {30, 0},
	[ITEM_LUMINOUS_MOSS] = {30, 0},
	[ITEM_LUMIOSE_GALETTE] = {30, 0},
	[ITEM_MAGNET] = {30, 0},
	[ITEM_MAX_REVIVE] = {30, 0},
	[ITEM_METAL_COAT] = {30, 0},
	[ITEM_METRONOME] = {30, 0},
	[ITEM_MIRACLE_SEED] = {30, 0},
	[ITEM_MOON_STONE] = {30, 0},
	[ITEM_MYSTIC_WATER] = {30, 0},
	[ITEM_NEVER_MELT_ICE] = {30, 0},
	[ITEM_NUGGET] = {30, 0},
	[ITEM_OLD_GATEAU] = {30, 0},
	[ITEM_PEARL_STRING] = {30, 0},
	[ITEM_PEARL] = {30, 0},
	[ITEM_POKE_DOLL] = {30, 0},
	[ITEM_PRISM_SCALE] = {30, 0},
	[ITEM_PROTECTIVE_PADS] = {30, 0},
	[ITEM_RAZOR_FANG] = {30, MOVE_EFFECT_FLINCH},
	[ITEM_RELIC_BAND] = {30, 0},
	[ITEM_RELIC_COPPER] = {30, 0},
	[ITEM_RELIC_CROWN] = {30, 0},
	[ITEM_RELIC_GOLD] = {30, 0},
	[ITEM_RELIC_SILVER] = {30, 0},
	[ITEM_RELIC_STATUE] = {30, 0},
	[ITEM_RELIC_VASE] = {30, 0},
	[ITEM_REVIVE] = {30, 0},
	[ITEM_SACRED_ASH] = {30, 0},
	[ITEM_SCOPE_LENS] = {30, 0},
	[ITEM_SHALOUR_SABLE] = {30, 0},
	[ITEM_SHELL_BELL] = {30, 0},
	[ITEM_SHOAL_SALT] = {30, 0},
	[ITEM_SHOAL_SHELL] = {30, 0},
	[ITEM_SMOKE_BALL] = {30, 0},
#ifdef UNBOUND
	[ITEM_SNOWBALL] = {30, MOVE_EFFECT_FREEZE},
#else
	[ITEM_SNOWBALL] = {30, 0},
#endif
	[ITEM_SOUL_DEW] = {30, 0},
	[ITEM_SPELL_TAG] = {30, 0},
	[ITEM_STAR_PIECE] = {30, 0},
	[ITEM_STARDUST] = {30, 0},
	[ITEM_SUN_STONE] = {30, 0},
	[ITEM_THUNDER_STONE] = {30, 0},
	[ITEM_TINY_MUSHROOM] = {30, 0},
	[ITEM_TOXIC_ORB] = {30, MOVE_EFFECT_TOXIC},
	[ITEM_TWISTED_SPOON] = {30, 0},
	[ITEM_UP_GRADE] = {30, 0},
	[ITEM_WATER_STONE] = {30, 0},
	[ITEM_EVIOLITE] = {40, 0},
	[ITEM_ICY_ROCK] = {40, 0},
	[ITEM_LUCKY_PUNCH] = {40, 0},
	[ITEM_FIGHTING_MEMORY] = {50, 0},
	[ITEM_FLYING_MEMORY] = {50, 0},
	[ITEM_POISON_MEMORY] = {50, 0},
	[ITEM_GROUND_MEMORY] = {50, 0},
	[ITEM_ROCK_MEMORY] = {50, 0},
	[ITEM_BUG_MEMORY] = {50, 0},
	[ITEM_GHOST_MEMORY] = {50, 0},
	[ITEM_STEEL_MEMORY] = {50, 0},
	[ITEM_FIRE_MEMORY] = {50, 0},
	[ITEM_WATER_MEMORY] = {50, 0},
	[ITEM_GRASS_MEMORY] = {50, 0},
	[ITEM_ELECTRIC_MEMORY] = {50, 0},
	[ITEM_PSYCHIC_MEMORY] = {50, 0},
	[ITEM_ICE_MEMORY] = {50, 0},
	[ITEM_DRAGON_MEMORY] = {50, 0},
	[ITEM_DARK_MEMORY] = {50, 0},
	[ITEM_FAIRY_MEMORY] = {50, 0},
	[ITEM_DUBIOUS_DISC] = {50, 0},
	[ITEM_SHARP_BEAK] = {50, 0},
	[ITEM_ADAMANT_ORB] = {60, 0},
	[ITEM_DAMP_ROCK] = {60, 0},
	[ITEM_GRISEOUS_ORB] = {60, 0},
	[ITEM_HEAT_ROCK] = {60, 0},
	[ITEM_LUSTROUS_ORB] = {60, 0},
	[ITEM_MACHO_BRACE] = {60, 0},
	[ITEM_ROCKY_HELMET] = {60, 0},
	[ITEM_LEEK] = {60, 0},
	[ITEM_TERRAIN_EXTENDER] = {60, 0},
	[ITEM_BURN_DRIVE] = {70, 0},
	[ITEM_DOUSE_DRIVE] = {70, 0},
	[ITEM_SHOCK_DRIVE] = {70, 0},
	[ITEM_CHILL_DRIVE] = {70, 0},
	[ITEM_DRAGON_FANG] = {70, 0},
	[ITEM_POISON_BARB] = {70, MOVE_EFFECT_POISON},
	[ITEM_POWER_ANKLET] = {70, 0},
	[ITEM_POWER_BAND] = {70, 0},
	[ITEM_POWER_BELT] = {70, 0},
	[ITEM_POWER_BRACER] = {70, 0},
	[ITEM_POWER_LENS] = {70, 0},
	[ITEM_POWER_WEIGHT] = {70, 0},
	[ITEM_ULTRANECROZIUM_Z] = {80, 0},
	[ITEM_VENUSAURITE] = {80, 0},
	[ITEM_CHARIZARDITE_X] = {80, 0},
	[ITEM_CHARIZARDITE_Y] = {80, 0},
	[ITEM_BLASTOISINITE] = {80, 0},
	[ITEM_BEEDRILLITE] = {80, 0},
	[ITEM_PIDGEOTITE] = {80, 0},
	[ITEM_ALAKAZITE] = {80, 0},
	[ITEM_SLOWBRONITE] = {80, 0},
	[ITEM_GENGARITE] = {80, 0},
	[ITEM_KANGASKHANITE] = {80, 0},
	[ITEM_PINSIRITE] = {80, 0},
	[ITEM_GYARADOSITE] = {80, 0},
	[ITEM_AERODACTYLITE] = {80, 0},
	[ITEM_MEWTWONITE_X] = {80, 0},
	[ITEM_MEWTWONITE_Y] = {80, 0},
	[ITEM_AMPHAROSITE] = {80, 0},
	[ITEM_STEELIXITE] = {80, 0},
	[ITEM_SCIZORITE] = {80, 0},
	[ITEM_HERACRONITE] = {80, 0},
	[ITEM_HOUNDOOMINITE] = {80, 0},
	[ITEM_TYRANITARITE] = {80, 0},
	[ITEM_SCEPTILITE] = {80, 0},
	[ITEM_BLAZIKENITE] = {80, 0},
	[ITEM_SWAMPERTITE] = {80, 0},
	[ITEM_GARDEVOIRITE] = {80, 0},
	[ITEM_SABLENITE] = {80, 0},
	[ITEM_MAWILITE] = {80, 0},
	[ITEM_AGGRONITE] = {80, 0},
	[ITEM_MEDICHAMITE] = {80, 0},
	[ITEM_MANECTITE] = {80, 0},
	[ITEM_SHARPEDONITE] = {80, 0},
	[ITEM_CAMERUPTITE] = {80, 0},
	[ITEM_ALTARIANITE] = {80, 0},
	[ITEM_BANETTITE] = {80, 0},
	[ITEM_ABSOLITE] = {80, 0},
	[ITEM_GLALITITE] = {80, 0},
	[ITEM_SALAMENCITE] = {80, 0},
	[ITEM_METAGROSSITE] = {80, 0},
	[ITEM_LATIASITE] = {80, 0},
	[ITEM_LATIOSITE] = {80, 0},
	[ITEM_LOPUNNITE] = {80, 0},
	[ITEM_GARCHOMPITE] = {80, 0},
	[ITEM_LUCARIONITE] = {80, 0},
	[ITEM_ABOMASITE] = {80, 0},
	[ITEM_GALLADITE] = {80, 0},
	[ITEM_AUDINITE] = {80, 0},
	[ITEM_DIANCITE] = {80, 0},
	[ITEM_ASSAULT_VEST] = {80, 0},
	[ITEM_DAWN_STONE] = {80, 0},
	[ITEM_DUSK_STONE] = {80, 0},
	[ITEM_ELECTIRIZER] = {80, 0},
	[ITEM_MAGMARIZER] = {80, 0},
	[ITEM_ODD_KEYSTONE] = {80, 0},
	[ITEM_OVAL_STONE] = {80, 0},
	[ITEM_PROTECTOR] = {80, 0},
	[ITEM_QUICK_CLAW] = {80, 0},
	[ITEM_RAZOR_CLAW] = {80, 0},
	[ITEM_SACHET] = {80, 0},
	[ITEM_SAFETY_GOGGLES] = {80, 0},
	[ITEM_SHINY_STONE] = {80, 0},
	[ITEM_STICKY_BARB] = {80, 0},
	[ITEM_WEAKNESS_POLICY] = {80, 0},
	[ITEM_WHIPPED_DREAM] = {80, 0},
	[ITEM_FIST_PLATE] = {90, 0},
	[ITEM_SKY_PLATE] = {90, 0},
	[ITEM_TOXIC_PLATE] = {90, 0},
	[ITEM_EARTH_PLATE] = {90, 0},
	[ITEM_STONE_PLATE] = {90, 0},
	[ITEM_INSECT_PLATE] = {90, 0},
	[ITEM_SPOOKY_PLATE] = {90, 0},
	[ITEM_IRON_PLATE] = {90, 0},
	[ITEM_FLAME_PLATE] = {90, 0},
	[ITEM_SPLASH_PLATE] = {90, 0},
	[ITEM_MEADOW_PLATE] = {90, 0},
	[ITEM_ZAP_PLATE] = {90, 0},
	[ITEM_MIND_PLATE] = {90, 0},
	[ITEM_ICICLE_PLATE] = {90, 0},
	[ITEM_DRACO_PLATE] = {90, 0},
	[ITEM_DREAD_PLATE] = {90, 0},
	[ITEM_PIXIE_PLATE] = {90, 0},
	[ITEM_DEEP_SEA_TOOTH] = {90, 0},
	[ITEM_GRIP_CLAW] = {90, 0},
	[ITEM_THICK_CLUB] = {90, 0},
	[ITEM_HELIX_FOSSIL] = {100, 0},
	[ITEM_DOME_FOSSIL] = {100, 0},
	[ITEM_ROOT_FOSSIL] = {100, 0},
	[ITEM_CLAW_FOSSIL] = {100, 0},
	[ITEM_SKULL_FOSSIL] = {100, 0},
	[ITEM_ARMOR_FOSSIL] = {100, 0},
	[ITEM_COVER_FOSSIL] = {100, 0},
	[ITEM_PLUME_FOSSIL] = {100, 0},
	[ITEM_JAW_FOSSIL] = {100, 0},
	[ITEM_SAIL_FOSSIL] = {100, 0},
	[ITEM_HARD_STONE] = {100, 0},
	[ITEM_RARE_BONE] = {100, 0},
	[ITEM_IRON_BALL] = {130, 0},
	[ITEM_BLACK_AUGURITE] = {30, 0},
	[ITEM_MALICIOUS_ARMOR] = {30, 0},
	[ITEM_AUSPICIOUS_ARMOR] = {30, 0},
	[ITEM_PEAT_BLOCK] = {30, 0},
	[ITEM_GIMMIGHOUL_COIN] = {30, 0},
	[ITEM_SYRUPY_APPLE] = {30, 0},
	[ITEM_METAL_ALLOY] = {30, 0},
	[ITEM_LEADERS_CREST] = {30, 0},
};

const u16 gBannedBattleEatBerries[] =
{
	ITEM_POMEG_BERRY,
	ITEM_KELPSY_BERRY,
	ITEM_QUALOT_BERRY,
	ITEM_HONDEW_BERRY,
	ITEM_GREPA_BERRY,
	ITEM_TAMATO_BERRY,
	ITEM_TABLES_TERMIN
};

const u8 gConsumableItemEffects[] =
{
	ITEM_EFFECT_RESTORE_HP,
	ITEM_EFFECT_CURE_PAR,
	ITEM_EFFECT_CURE_SLP,
	ITEM_EFFECT_CURE_PSN,
	ITEM_EFFECT_CURE_BRN,
	ITEM_EFFECT_CURE_FRZ,
	ITEM_EFFECT_RESTORE_PP,
	ITEM_EFFECT_CURE_CONFUSION,
	ITEM_EFFECT_CURE_STATUS,
	ITEM_EFFECT_CONFUSE_SPICY,
	ITEM_EFFECT_CONFUSE_DRY,
	ITEM_EFFECT_CONFUSE_SWEET,
	ITEM_EFFECT_CONFUSE_BITTER,
	ITEM_EFFECT_CONFUSE_SOUR,
	ITEM_EFFECT_ATTACK_UP,
	ITEM_EFFECT_DEFENSE_UP,
	ITEM_EFFECT_SPEED_UP,
	ITEM_EFFECT_SP_ATTACK_UP,
	ITEM_EFFECT_SP_DEFENSE_UP,
	ITEM_EFFECT_CRITICAL_UP,
	ITEM_EFFECT_RANDOM_STAT_UP,
	ITEM_EFFECT_RESTORE_STATS,
	ITEM_EFFECT_CURE_ATTRACT,
	ITEM_EFFECT_WEAKNESS_POLICY,
	ITEM_EFFECT_GEM,
	ITEM_EFFECT_WEAKNESS_BERRY,
	ITEM_EFFECT_CUSTAP_BERRY,
	ITEM_EFFECT_ABSORB_BULB,
	ITEM_EFFECT_AIR_BALLOON,
	ITEM_EFFECT_CELL_BATTERY,
	ITEM_EFFECT_EJECT_BUTTON,
	ITEM_EFFECT_LUMINOUS_MOSS,
	ITEM_EFFECT_RED_CARD,
	ITEM_EFFECT_SNOWBALL,
	ITEM_EFFECT_SEEDS,
	ITEM_EFFECT_JABOCA_ROWAP_BERRY,
	ITEM_EFFECT_KEE_BERRY,
	ITEM_EFFECT_MARANGA_BERRY,
	ITEM_EFFECT_ADRENALINE_ORB,
	ITEM_EFFECT_POWER_HERB,
	ITEM_EFFECT_MICLE_BERRY,
	ITEM_EFFECT_ENIGMA_BERRY,
	ITEM_EFFECT_EJECT_PACK,
	ITEM_EFFECT_ROOM_SERVICE,
	ITEM_EFFECT_BLUNDER_POLICY,
	ITEM_EFFECT_THROAT_SPRAY,
	0xFF,
};

const u16 gItemsByType[ITEMS_COUNT] =
{
	[ITEM_MAX_REPEL] = ITEM_TYPE_MAX_REPEL,
	[ITEM_SUPER_REPEL] = ITEM_TYPE_SUPER_REPEL,
	[ITEM_REPEL] = ITEM_TYPE_REPEL,
	[ITEM_ESCAPE_ROPE] = ITEM_TYPE_FIELD_USE,
	[ITEM_HONEY] = ITEM_TYPE_FIELD_USE,

	[ITEM_FULL_RESTORE] = ITEM_TYPE_FULL_RESTORE,
	[ITEM_MAX_POTION] = ITEM_TYPE_MAX_POTION,
	[ITEM_HYPER_POTION] = ITEM_TYPE_HYPER_POTION,
	[ITEM_SUPER_POTION] = ITEM_TYPE_SUPER_POTION,
	[ITEM_POTION] = ITEM_TYPE_POTION,
	[ITEM_REVIVE] = ITEM_TYPE_REVIVE,
	[ITEM_MAX_REVIVE] = ITEM_TYPE_REVIVE,
	[ITEM_SACRED_ASH] = ITEM_TYPE_REVIVE,
	[ITEM_FRESH_WATER] = ITEM_TYPE_FRESH_WATER,
	[ITEM_SODA_POP] = ITEM_TYPE_SODA_POP,
	[ITEM_LEMONADE] = ITEM_TYPE_LEMONADE,
	[ITEM_MOOMOO_MILK] = ITEM_TYPE_MOOMOO_MILK,
	[ITEM_ENERGY_POWDER] = ITEM_TYPE_HERB_HEAL,
	[ITEM_ENERGY_ROOT] = ITEM_TYPE_HERB_HEAL,
	[ITEM_REVIVAL_HERB] = ITEM_TYPE_HERB_HEAL,

	[ITEM_ANTIDOTE] = ITEM_TYPE_STATUS_RECOVERY,
	[ITEM_BURN_HEAL] = ITEM_TYPE_STATUS_RECOVERY,
	[ITEM_ICE_HEAL] = ITEM_TYPE_STATUS_RECOVERY,
	[ITEM_AWAKENING] = ITEM_TYPE_STATUS_RECOVERY,
	[ITEM_PARALYZE_HEAL] = ITEM_TYPE_STATUS_RECOVERY,
	[ITEM_FULL_HEAL] = ITEM_TYPE_FULL_HEAL,
	[ITEM_LAVA_COOKIE] = ITEM_TYPE_FOOD_STATUS_RECOVERY,
	[ITEM_BIG_MALASADA] = ITEM_TYPE_FOOD_STATUS_RECOVERY,
	[ITEM_CASTELIACONE] = ITEM_TYPE_FOOD_STATUS_RECOVERY,
	[ITEM_LUMIOSE_GALETTE] = ITEM_TYPE_FOOD_STATUS_RECOVERY,
	[ITEM_RAGE_CANDY_BAR] = ITEM_TYPE_FOOD_STATUS_RECOVERY,
	[ITEM_SHALOUR_SABLE] = ITEM_TYPE_FOOD_STATUS_RECOVERY,
	[ITEM_OLD_GATEAU] = ITEM_TYPE_FOOD_STATUS_RECOVERY,
	[ITEM_HEAL_POWDER] = ITEM_TYPE_FOOD_STATUS_RECOVERY,

	[ITEM_ETHER] = ITEM_TYPE_PP_RECOVERY,
	[ITEM_MAX_ETHER] = ITEM_TYPE_PP_RECOVERY,
	[ITEM_ELIXIR] = ITEM_TYPE_PP_RECOVERY,
	[ITEM_MAX_ELIXIR] = ITEM_TYPE_PP_RECOVERY,

	[ITEM_ABILITY_CAPSULE] = ITEM_TYPE_ABILITY_GIGANTAMAX_MODIFIER,
	[ITEM_DYNAMAX_CANDY] = ITEM_TYPE_ABILITY_GIGANTAMAX_MODIFIER,
	[ITEM_RARE_CANDY] = ITEM_TYPE_LEVEL_MODIFIER,
	[ITEM_PP_UP] = ITEM_TYPE_PP_UP,
	[ITEM_PP_MAX] = ITEM_TYPE_PP_MAX,
	[ITEM_HP_UP] = ITEM_TYPE_HP_UP,
	[ITEM_PROTEIN] = ITEM_TYPE_PROTEIN,
	[ITEM_IRON] = ITEM_TYPE_IRON,
	[ITEM_CALCIUM] = ITEM_TYPE_CALCIUM,
	[ITEM_ZINC] = ITEM_TYPE_ZINC,
	[ITEM_CARBOS] = ITEM_TYPE_CARBOS,

	[ITEM_HEALTH_WING] = ITEM_TYPE_HP_WING,
	[ITEM_MUSCLE_WING] = ITEM_TYPE_ATTACK_WING,
	[ITEM_RESIST_WING] = ITEM_TYPE_DEFENSE_WING,
	[ITEM_GENIUS_WING] = ITEM_TYPE_SPATK_WING,
	[ITEM_CLEVER_WING] = ITEM_TYPE_SPDEF_WING,
	[ITEM_SWIFT_WING] = ITEM_TYPE_SPEED_WING,
	[ITEM_PRETTY_WING] = ITEM_TYPE_SELLABLE,

	[ITEM_POWER_BRACER] = ITEM_TYPE_STAT_BOOST_HELD_ITEM,
	[ITEM_POWER_BELT] = ITEM_TYPE_STAT_BOOST_HELD_ITEM,
	[ITEM_POWER_LENS] = ITEM_TYPE_STAT_BOOST_HELD_ITEM,
	[ITEM_POWER_BAND] = ITEM_TYPE_STAT_BOOST_HELD_ITEM,
	[ITEM_POWER_ANKLET] = ITEM_TYPE_STAT_BOOST_HELD_ITEM,
	[ITEM_POWER_WEIGHT] = ITEM_TYPE_STAT_BOOST_HELD_ITEM,
	[ITEM_MACHO_BRACE] = ITEM_TYPE_STAT_BOOST_HELD_ITEM,

	[ITEM_SUN_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_MOON_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_FIRE_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_THUNDER_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_WATER_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_LEAF_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_DAWN_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_DUSK_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_SHINY_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_ICE_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_OVAL_STONE] = ITEM_TYPE_EVOLUTION_STONE,
	[ITEM_LINK_CABLE] = ITEM_TYPE_EVOLUTION_STONE,

	[ITEM_KINGS_ROCK] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_DEEP_SEA_TOOTH] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_DEEP_SEA_SCALE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_EVERSTONE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_METAL_COAT] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_DRAGON_SCALE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_UP_GRADE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_PROTECTOR] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_MAGMARIZER] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_PRISM_SCALE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_SACHET] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_WHIPPED_DREAM] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_RAZOR_CLAW] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_RAZOR_FANG] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_REAPER_CLOTH] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_DUBIOUS_DISC] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_ELECTIRIZER] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_SWEET_APPLE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_TART_APPLE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_CRACKED_POT] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_CHIPPED_POT] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_STRAWBERRY_SWEET] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_BERRY_SWEET] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_LOVE_SWEET] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_CLOVER_SWEET] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_FLOWER_SWEET] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_RIBBON_SWEET] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_STAR_SWEET] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_GALARICA_CUFF] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_GALARICA_WREATH] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_BLACK_AUGURITE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_MALICIOUS_ARMOR] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_AUSPICIOUS_ARMOR] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_PEAT_BLOCK] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_GIMMIGHOUL_COIN] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_SYRUPY_APPLE] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_METAL_ALLOY] = ITEM_TYPE_EVOLUTION_ITEM,
	[ITEM_LEADERS_CREST] = ITEM_TYPE_EVOLUTION_ITEM,

	[ITEM_X_ATTACK] = ITEM_TYPE_X_ATTACK,
	[ITEM_X_DEFEND] = ITEM_TYPE_X_DEFENSE,
	[ITEM_X_SPECIAL] = ITEM_TYPE_X_SP_ATTACK,
	[ITEM_X_SP_DEF] = ITEM_TYPE_X_SP_DEFENSE,
	[ITEM_X_SPEED] = ITEM_TYPE_X_SPEED,
	[ITEM_X_ACCURACY] = ITEM_TYPE_X_ACCURACY,
	[ITEM_GUARD_SPEC] = ITEM_TYPE_OTHER_STAT_BATTLE_ITEM,
	[ITEM_DIRE_HIT] = ITEM_TYPE_OTHER_STAT_BATTLE_ITEM,
	[ITEM_POKE_DOLL] = ITEM_TYPE_OTHER_BATTLE_ITEM,
	[ITEM_FLUFFY_TAIL] = ITEM_TYPE_OTHER_BATTLE_ITEM,

	[ITEM_BERRY_JUICE] = ITEM_TYPE_HELD_ITEM,
	[ITEM_BRIGHT_POWDER] = ITEM_TYPE_HELD_ITEM,
	[ITEM_WHITE_HERB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_EXP_SHARE] = ITEM_TYPE_HELD_ITEM,
	[ITEM_QUICK_CLAW] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SOOTHE_BELL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_MENTAL_HERB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_CHOICE_BAND] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SILVER_POWDER] = ITEM_TYPE_HELD_ITEM,
	[ITEM_AMULET_COIN] = ITEM_TYPE_HELD_ITEM,
	[ITEM_CLEANSE_TAG] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SOUL_DEW] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SMOKE_BALL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_FOCUS_BAND] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LUCKY_EGG] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SCOPE_LENS] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LEFTOVERS] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LIGHT_BALL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SOFT_SAND] = ITEM_TYPE_HELD_ITEM,
	[ITEM_HARD_STONE] = ITEM_TYPE_HELD_ITEM,
	[ITEM_MIRACLE_SEED] = ITEM_TYPE_HELD_ITEM,
	[ITEM_BLACK_GLASSES] = ITEM_TYPE_HELD_ITEM,
	[ITEM_BLACK_BELT] = ITEM_TYPE_HELD_ITEM,
	[ITEM_MAGNET] = ITEM_TYPE_HELD_ITEM,
	[ITEM_MYSTIC_WATER] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SHARP_BEAK] = ITEM_TYPE_HELD_ITEM,
	[ITEM_POISON_BARB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_NEVER_MELT_ICE] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SPELL_TAG] = ITEM_TYPE_HELD_ITEM,
	[ITEM_TWISTED_SPOON] = ITEM_TYPE_HELD_ITEM,
	[ITEM_CHARCOAL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_DRAGON_FANG] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SILK_SCARF] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SHELL_BELL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LUCKY_PUNCH] = ITEM_TYPE_HELD_ITEM,
	[ITEM_METAL_POWDER] = ITEM_TYPE_HELD_ITEM,
	[ITEM_THICK_CLUB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LEEK] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ADAMANT_ORB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LUSTROUS_ORB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_GRISEOUS_ORB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_EXPERT_BELT] = ITEM_TYPE_HELD_ITEM,
	[ITEM_POWER_HERB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_WIDE_LENS] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ZOOM_LENS] = ITEM_TYPE_HELD_ITEM,
	[ITEM_DESTINY_KNOT] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SMOOTH_ROCK] = ITEM_TYPE_HELD_ITEM,
	[ITEM_DAMP_ROCK] = ITEM_TYPE_HELD_ITEM,
	[ITEM_HEAT_ROCK] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ICY_ROCK] = ITEM_TYPE_HELD_ITEM,
	[ITEM_BIG_ROOT] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LIGHT_CLAY] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SAFETY_GOGGLES] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ROCKY_HELMET] = ITEM_TYPE_HELD_ITEM,
	[ITEM_WEAKNESS_POLICY] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ASSAULT_VEST] = ITEM_TYPE_HELD_ITEM,
	[ITEM_EVIOLITE] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ABSORB_BULB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_AIR_BALLOON] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ADRENALINE_ORB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_BINDING_BAND] = ITEM_TYPE_HELD_ITEM,
	[ITEM_CELL_BATTERY] = ITEM_TYPE_HELD_ITEM,
	[ITEM_EJECT_BUTTON] = ITEM_TYPE_HELD_ITEM,
	[ITEM_FLOAT_STONE] = ITEM_TYPE_HELD_ITEM,
	[ITEM_FOCUS_SASH] = ITEM_TYPE_HELD_ITEM,
	[ITEM_GRIP_CLAW] = ITEM_TYPE_HELD_ITEM,
	[ITEM_IRON_BALL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LAGGING_TAIL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LUMINOUS_MOSS] = ITEM_TYPE_HELD_ITEM,
	[ITEM_QUICK_POWDER] = ITEM_TYPE_HELD_ITEM,
	[ITEM_METRONOME] = ITEM_TYPE_HELD_ITEM,
	[ITEM_MUSCLE_BAND] = ITEM_TYPE_HELD_ITEM,
	[ITEM_PROTECTIVE_PADS] = ITEM_TYPE_HELD_ITEM,
	[ITEM_RED_CARD] = ITEM_TYPE_HELD_ITEM,
	[ITEM_RING_TARGET] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SHED_SHELL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_SNOWBALL] = ITEM_TYPE_HELD_ITEM,
	[ITEM_STICKY_BARB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_TERRAIN_EXTENDER] = ITEM_TYPE_HELD_ITEM,
	[ITEM_WISE_GLASSES] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ELECTRIC_SEED] = ITEM_TYPE_HELD_ITEM,
	[ITEM_GRASSY_SEED] = ITEM_TYPE_HELD_ITEM,
	[ITEM_MISTY_SEED] = ITEM_TYPE_HELD_ITEM,
	[ITEM_PSYCHIC_SEED] = ITEM_TYPE_HELD_ITEM,
	[ITEM_LIFE_ORB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_TOXIC_ORB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_FLAME_ORB] = ITEM_TYPE_HELD_ITEM,
	[ITEM_BLACK_SLUDGE] = ITEM_TYPE_HELD_ITEM,
	[ITEM_CHOICE_SPECS] = ITEM_TYPE_HELD_ITEM,
	[ITEM_CHOICE_SCARF] = ITEM_TYPE_HELD_ITEM,
	[ITEM_EJECT_PACK] = ITEM_TYPE_HELD_ITEM,
	[ITEM_ROOM_SERVICE] = ITEM_TYPE_HELD_ITEM,
	[ITEM_BLUNDER_POLICY] = ITEM_TYPE_HELD_ITEM,
	[ITEM_HEAVY_DUTY_BOOTS] = ITEM_TYPE_HELD_ITEM,
	[ITEM_UTILITY_UMBRELLA] = ITEM_TYPE_HELD_ITEM,
	[ITEM_THROAT_SPRAY] = ITEM_TYPE_HELD_ITEM,
	[ITEM_RUSTED_SWORD] = ITEM_TYPE_HELD_ITEM,
	[ITEM_RUSTED_SHIELD] = ITEM_TYPE_HELD_ITEM,

	[ITEM_FIST_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_SKY_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_TOXIC_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_EARTH_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_STONE_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_INSECT_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_SPOOKY_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_IRON_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_FLAME_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_SPLASH_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_MEADOW_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_ZAP_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_MIND_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_ICICLE_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_DRACO_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_DREAD_PLATE] = ITEM_TYPE_PLATE,
	[ITEM_PIXIE_PLATE] = ITEM_TYPE_PLATE,

	[ITEM_FIGHTING_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_FLYING_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_POISON_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_GROUND_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_ROCK_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_BUG_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_GHOST_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_STEEL_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_FIRE_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_WATER_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_GRASS_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_ELECTRIC_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_PSYCHIC_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_ICE_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_DRAGON_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_DARK_MEMORY] = ITEM_TYPE_MEMORY,
	[ITEM_FAIRY_MEMORY] = ITEM_TYPE_MEMORY,

	[ITEM_BURN_DRIVE] = ITEM_TYPE_DRIVE,
	[ITEM_DOUSE_DRIVE] = ITEM_TYPE_DRIVE,
	[ITEM_SHOCK_DRIVE] = ITEM_TYPE_DRIVE,
	[ITEM_CHILL_DRIVE] = ITEM_TYPE_DRIVE,

	[ITEM_NORMAL_GEM] = ITEM_TYPE_GEM,
	[ITEM_FIGHTING_GEM] = ITEM_TYPE_GEM,
	[ITEM_FLYING_GEM] = ITEM_TYPE_GEM,
	[ITEM_POISON_GEM] = ITEM_TYPE_GEM,
	[ITEM_GROUND_GEM] = ITEM_TYPE_GEM,
	[ITEM_ROCK_GEM] = ITEM_TYPE_GEM,
	[ITEM_BUG_GEM] = ITEM_TYPE_GEM,
	[ITEM_GHOST_GEM] = ITEM_TYPE_GEM,
	[ITEM_STEEL_GEM] = ITEM_TYPE_GEM,
	[ITEM_FIRE_GEM] = ITEM_TYPE_GEM,
	[ITEM_WATER_GEM] = ITEM_TYPE_GEM,
	[ITEM_GRASS_GEM] = ITEM_TYPE_GEM,
	[ITEM_ELECTRIC_GEM] = ITEM_TYPE_GEM,
	[ITEM_PSYCHIC_GEM] = ITEM_TYPE_GEM,
	[ITEM_ICE_GEM] = ITEM_TYPE_GEM,
	[ITEM_DRAGON_GEM] = ITEM_TYPE_GEM,
	[ITEM_DARK_GEM] = ITEM_TYPE_GEM,
	[ITEM_FAIRY_GEM] = ITEM_TYPE_GEM,

	[ITEM_SEA_INCENSE] = ITEM_TYPE_INCENSE,
	[ITEM_LAX_INCENSE] = ITEM_TYPE_INCENSE,
	[ITEM_LUCK_INCENSE] = ITEM_TYPE_INCENSE,
	[ITEM_FULL_INCENSE] = ITEM_TYPE_INCENSE,
	[ITEM_ODD_INCENSE] = ITEM_TYPE_INCENSE,
	[ITEM_PURE_INCENSE] = ITEM_TYPE_INCENSE,
	[ITEM_ROCK_INCENSE] = ITEM_TYPE_INCENSE,
	[ITEM_ROSE_INCENSE] = ITEM_TYPE_INCENSE,
	[ITEM_WAVE_INCENSE] = ITEM_TYPE_INCENSE,

	[ITEM_VENUSAURITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_CHARIZARDITE_X] = ITEM_TYPE_MEGA_STONE,
	[ITEM_CHARIZARDITE_Y] = ITEM_TYPE_MEGA_STONE,
	[ITEM_BLASTOISINITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_BEEDRILLITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_PIDGEOTITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_ALAKAZITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_SLOWBRONITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_GENGARITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_KANGASKHANITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_PINSIRITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_GYARADOSITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_AERODACTYLITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_MEWTWONITE_X] = ITEM_TYPE_MEGA_STONE,
	[ITEM_MEWTWONITE_Y] = ITEM_TYPE_MEGA_STONE,
	[ITEM_AMPHAROSITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_STEELIXITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_SCIZORITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_HERACRONITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_HOUNDOOMINITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_TYRANITARITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_SCEPTILITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_BLAZIKENITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_SWAMPERTITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_GARDEVOIRITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_SABLENITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_MAWILITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_AGGRONITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_MEDICHAMITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_MANECTITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_SHARPEDONITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_CAMERUPTITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_ALTARIANITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_BANETTITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_ABSOLITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_GLALITITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_SALAMENCITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_METAGROSSITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_LATIASITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_LATIOSITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_LOPUNNITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_GARCHOMPITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_LUCARIONITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_ABOMASITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_GALLADITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_AUDINITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_DIANCITE] = ITEM_TYPE_MEGA_STONE,
	[ITEM_RED_ORB] = ITEM_TYPE_PRIMAL_ORB,
	[ITEM_BLUE_ORB] = ITEM_TYPE_PRIMAL_ORB,

	[ITEM_NORMALIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_FIGHTINIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_FLYINIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_POISONIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_GROUNDIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_ROCKIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_BUGINIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_GHOSTIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_STEELIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_FIRIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_WATERIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_GRASSIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_ELECTRIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_PSYCHIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_ICIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_DRAGONIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_DARKINIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_FAIRIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_ALORAICHIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_DECIDIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_EEVIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_INCINIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_KOMMONIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_LUNALIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_LYCANIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_MARSHADIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_MEWNIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_MIMIKIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_PIKANIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_PIKASHUNIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_PRIMARIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_SNORLIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_SOLGANIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_TAPUNIUM_Z] = ITEM_TYPE_Z_CRYSTAL,
	[ITEM_ULTRANECROZIUM_Z] =  ITEM_TYPE_Z_CRYSTAL,

	[ITEM_BLUE_FLUTE] = ITEM_TYPE_FLUTE,
	[ITEM_YELLOW_FLUTE] = ITEM_TYPE_FLUTE,
	[ITEM_RED_FLUTE] = ITEM_TYPE_FLUTE,
	[ITEM_BLACK_FLUTE] = ITEM_TYPE_FLUTE,
	[ITEM_WHITE_FLUTE] = ITEM_TYPE_FLUTE,

	[ITEM_RED_NECTAR] = ITEM_TYPE_NECTAR,
	[ITEM_YELLOW_NECTAR] = ITEM_TYPE_NECTAR,
	[ITEM_PINK_NECTAR] = ITEM_TYPE_NECTAR,
	[ITEM_PURPLE_NECTAR] = ITEM_TYPE_NECTAR,

	[ITEM_HEART_SCALE] = ITEM_TYPE_SELLABLE,
	[ITEM_SHOAL_SALT] = ITEM_TYPE_SELLABLE,
	[ITEM_SHOAL_SHELL] = ITEM_TYPE_SELLABLE,
	[ITEM_TINY_MUSHROOM] = ITEM_TYPE_SELLABLE,
	[ITEM_BIG_MUSHROOM] = ITEM_TYPE_SELLABLE,
	[ITEM_PEARL] = ITEM_TYPE_SELLABLE,
	[ITEM_BIG_PEARL] = ITEM_TYPE_SELLABLE,
	[ITEM_STARDUST] = ITEM_TYPE_SELLABLE,
	[ITEM_STAR_PIECE] = ITEM_TYPE_SELLABLE,
	[ITEM_NUGGET] = ITEM_TYPE_SELLABLE,
	[ITEM_RARE_BONE] = ITEM_TYPE_SELLABLE,
	[ITEM_PEARL_STRING] = ITEM_TYPE_SELLABLE,
	[ITEM_BIG_NUGGET] = ITEM_TYPE_SELLABLE,
	[ITEM_COMET_SHARD] = ITEM_TYPE_SELLABLE,
	[ITEM_BALM_MUSHROOM] = ITEM_TYPE_SELLABLE,
	[ITEM_ODD_KEYSTONE] = ITEM_TYPE_SELLABLE,
	[ITEM_BOTTLE_CAP] = ITEM_TYPE_SELLABLE,
	[ITEM_GOLD_BOTTLE_CAP] = ITEM_TYPE_SELLABLE,
	[ITEM_WISHING_PIECE] = ITEM_TYPE_SELLABLE,

	[ITEM_RELIC_COPPER] = ITEM_TYPE_RELIC,
	[ITEM_RELIC_SILVER] = ITEM_TYPE_RELIC,
	[ITEM_RELIC_GOLD] = ITEM_TYPE_RELIC,
	[ITEM_RELIC_VASE] = ITEM_TYPE_RELIC,
	[ITEM_RELIC_BAND] = ITEM_TYPE_RELIC,
	[ITEM_RELIC_STATUE] = ITEM_TYPE_RELIC,
	[ITEM_RELIC_CROWN] = ITEM_TYPE_RELIC,

	[ITEM_RED_SHARD] = ITEM_TYPE_SHARD,
	[ITEM_BLUE_SHARD] = ITEM_TYPE_SHARD,
	[ITEM_YELLOW_SHARD] = ITEM_TYPE_SHARD,
	[ITEM_GREEN_SHARD] = ITEM_TYPE_SHARD,

	[ITEM_HELIX_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_DOME_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_OLD_AMBER] = ITEM_TYPE_FOSSIL,
	[ITEM_ROOT_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_CLAW_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_SKULL_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_ARMOR_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_COVER_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_PLUME_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_JAW_FOSSIL] = ITEM_TYPE_FOSSIL,
	[ITEM_SAIL_FOSSIL] = ITEM_TYPE_FOSSIL,

	[ITEM_ORANGE_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_HARBOR_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_GLITTER_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_MECH_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_WOOD_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_WAVE_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_BEAD_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_SHADOW_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_TROPIC_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_DREAM_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_FAB_MAIL] = ITEM_TYPE_MAIL,
	[ITEM_RETRO_MAIL] = ITEM_TYPE_MAIL,
};


#ifdef EXPANDED_NEW_ITEMS
const u32 *const gItemGraphicsTable[ITEMS_COUNT + 1][2] =
{
	{gBag_InterrogationTiles, gBag_InterrogationPal},
	{gBag_MasterBallTiles, gBag_MasterBallPal},
    {gBag_UltraBallTiles, gBag_UltraBallPal},
	{gBag_GreatBallTiles, gBag_GreatBallPal},
	{gBag_PokeBallTiles, gBag_PokeBallPal},
	{gBag_SafariBallTiles, gBag_SafariBallPal},
	{gBag_NetBallTiles, gBag_NetBallPal},
	{gBag_DiveBallTiles, gBag_DiveBallPal},
	{gBag_NestBallTiles, gBag_NestBallPal},
	{gBag_RepeatBallTiles, gBag_RepeatBallPal},
	{gBag_TimerBallTiles, gBag_TimerBallTPal},
	{gBag_LuxuryBallTiles, gBag_LuxuryBallPal},
	{gBag_PremierBallTiles, gBag_PremierBallPal},
	{gBag_PotionTiles, gBag_PotionPal},
	{gBag_AntidoteTiles, gBag_AntidotePal},
	{gBag_BurnHealTiles, gBag_BurnHealTPal},
	{gBag_IceHealTiles, gBag_IceHealPal},
	{gBag_AwakeningTiles, gBag_AwakeningPal},
	{gBag_ParalyzHealTiles, gBag_ParalyzHealPal},
	{gBag_FullRestoreTiles, gBag_FullRestorePal},
	{gBag_MaxPotionTiles, gBag_MaxPotionPal},
	{gBag_HyperPotionTiles, gBag_HyperPotionPal},
	{gBag_SuperPotionTiles, gBag_SuperPotionPal},
	{gBag_FullHealTiles, gBag_FullHealPal},
	{gBag_ReviveTiles, gBag_RevivePal},
	{gBag_MaxReviveTiles, gBag_MaxRevivePal},
	{gBag_FreshWaterTiles, gBag_FreshWaterPal},
	{gBag_SodaPopTiles, gBag_SodaPopPal},
	{gBag_LemonadeTiles, gBag_LemonadePal},
	{gBag_MoomooMilkTiles, gBag_MoomooMilkPal},
	{gBag_EnergyPowderTiles, gBag_EnergyPowderPal},
	{gBag_EnergyRootTiles, gBag_EnergyRootPal},
	{gBag_HealPowderTiles, gBag_HealPowderPal},
	{gBag_RevivalHerbTiles, gBag_RevivalHerbPal},
	{gBag_EtherTiles, gBag_EtherPal},
	{gBag_MaxEtherTiles, gBag_MaxEtherPal},
	{gBag_ElixirTiles, gBag_ElixirPal},
	{gBag_MaxElixirTiles, gBag_MaxElixirPal},
	{gBag_LavaCookieTiles, gBag_LavaCookiePal},
	{gBag_BlueFluteTiles, gBag_BlueFlutePal},
	{gBag_YellowFluteTiles, gBag_YellowFlutePal},
	{gBag_RedFluteTiles, gBag_RedFlutePal},
	{gBag_BlackFluteTiles, gBag_BlackFlutePal},
	{gBag_WhiteFluteTiles, gBag_WhiteFlutePal},
	{gBag_BerryJuiceTiles, gBag_BerryJuicePal},
	{gBag_SacredAshTiles, gBag_SacredAshPal},
	{gBag_ShoalSaltTiles, gBag_ShoalSaltPal},
	{gBag_ShoalShellTiles, gBag_ShoalShellPal},
	{gBag_RedShardTiles, gBag_RedShardPal},
	{gBag_BlueShardTiles, gBag_BlueShardPal},
	{gBag_YellowShardTiles, gBag_YellowShardPal},
	{gBag_GreenShardTiles, gBag_GreenShardPal},
	{gItemSprite_Rage_Candy_BarTiles, gItemSprite_Rage_Candy_BarPal},
	{gItemSprite_Old_GateauTiles, gItemSprite_Old_GateauPal},
	{gItemSprite_CasteliaconeTiles, gItemSprite_CasteliaconePal},
	{gBag_LumioseGaletteTiles, gBag_LumioseGalettePal},
	{gBag_ShalourSableTiles, gBag_ShalourSablePal},
	{gBag_HealthWingTiles, gBag_HealthWingPal},
	{gBag_MuscleWingTiles, gBag_MuscleWingPal},
	{gBag_ResistWingTiles, gBag_ResistWingPal},
	{gBag_GeniusWingTiles, gBag_GeniusWingPal},
	{gBag_CleverWingTiles, gBag_CleverWingPal},
	{gBag_SwiftWingTiles, gBag_SwiftWingPal},
	{gBag_HPUpTiles, gBag_HPUpPal},
	{gBag_ProteinTiles, gBag_ProteinPal},
	{gBag_IronTiles, gBag_IronPal},
	{gBag_CarbosTiles, gBag_CarbosPal},
	{gBag_CalciumTiles, gBag_CalciumPal},
	{gBag_RareCandyTiles, gBag_RareCandyPal},
	{gBag_PPUpTiles, gBag_PPUpPal},
	{gBag_ZincTiles, gBag_ZincPal},
	{gBag_PPMaxTiles, gBag_PPMaxPal},
	{gBag_MaxCandyTiles, gBag_MaxCandyPal},
	{gBag_GuardSpecTiles, gBag_GuardSpecPal},
	{gBag_DireHitTiles, gBag_DireHitPal},
	{gBag_XAttackTiles, gBag_XAttackPal},
	{gBag_XDefendTiles, gBag_XDefendPal},
	{gBag_XSpeedTiles, gBag_XSpeedPal},
	{gBag_XAccuracyTiles, gBag_XAccuracyPal},
	{gBag_XSpAtkTiles, gBag_XSpAtkPal},
	{gBag_PokeDollTiles, gBag_PokeDollPal},
	{gBag_FluffyTailTiles, gBag_FluffyTailPal},
	{gBag_BigMalasadaTiles, gBag_BigMalasadaPal},
	{gBag_SuperRepelTiles, gBag_SuperRepelPal},
	{gBag_MaxRepelTiles, gBag_MaxRepelPal},
	{gBag_EscapeRopeTiles, gBag_EscapeRopePal},
	{gBag_RepelTiles, gBag_RepelPal},
	{gBag_LinkCableTiles, gBag_LinkCablePal},
	{gBag_ProtectorTiles, gBag_ProtectorPal},
	{gBag_ElectirizerTiles, gBag_ElectirizerPal},
	{gBag_MagmarizerTiles, gBag_MagmarizerPal},
	{gBag_DubiousDiskTiles, gBag_DubiousDiskPal},
	{gBag_ReaperClothTiles, gBag_ReaperClothPal},
	{gBag_SunStoneTiles, gBag_SunStonePal},
	{gBag_MoonStoneTiles, gBag_MoonStonePal},
	{gBag_FireStoneTiles, gBag_FireStonePal},
	{gBag_ThunderStoneTiles, gBag_ThunderStonePal},
	{gBag_WaterStoneTiles, gBag_WaterStonePal},
	{gBag_LeafStoneTiles, gBag_LeafStonePal},
	{gBag_ShinyStoneTiles, gBag_ShinyStonePal},
	{gBag_DuskStoneTiles, gBag_DuskStonePal},
	{gBag_DawnStoneTiles, gBag_DawnStonePal},
	{gBag_IceStoneTiles, gBag_IceStonePal},
	{gBag_TinyMushroomTiles, gBag_TinyMushroomPal},
	{gBag_BigMushroomTiles, gBag_BigMushroomPal},
	{gBag_BalmMushroomTiles, gBag_BalmMushroomPal},
	{gBag_PearlTiles, gBag_PearlPal},
	{gBag_BigPearlTiles, gBag_BigPearlPal},
	{gBag_StardustTiles, gBag_StardustPal},
	{gBag_StarPieceTiles, gBag_StarPiecePal},
	{gBag_NuggetTiles, gBag_NuggetPal},
	{gBag_HeartScaleTiles, gBag_HeartScalePal},
	{gBag_RareBoneTiles, gBag_RareBonePal},
	{gBag_PearlStringTiles, gBag_PearlStringPal},
	{gBag_CometShardTiles, gBag_CometShardPal},
	{gBag_BigNuggetTiles, gBag_BigNuggetPal},
	{gBag_HoneyTiles, gBag_HoneyPal},
	{gBag_PrettyWingTiles, gBag_PrettyWingPal},
	{gBag_OvalStoneTiles, gBag_OvalStonePal},
	{gBag_RazorClawTiles, gBag_RazorClawPal},
	{gBag_RazorFangTiles, gBag_RazorFangPal},
	{gBag_OrangeMailTiles, gBag_OrangeMailPal},
	{gBag_HarborMailTiles, gBag_HarborMailPal},
	{gBag_GlitterMailTiles, gBag_GlitterMailPal},
	{gBag_MechMailTiles, gBag_MechMailPal},
	{gBag_WoodMailTiles, gBag_WoodMailPal},
	{gBag_WaveMailTiles, gBag_WaveMailPal},
	{gBag_BeadMailTiles, gBag_BeadMailPal},
	{gBag_ShadowMailTiles, gBag_ShadowMailPal},
	{gBag_TropicMailTiles, gBag_TropicMailPal},
	{gBag_DreamMailTiles, gBag_DreamMailPal},
	{gBag_FabMailTiles, gBag_FabMailPal},
	{gBag_RetroMailTiles, gBag_RetroMailPal},
	{gBag_CheriBerryTiles, gBag_CheriBerryPal},
	{gBag_ChestoBerryTiles, gBag_ChestoBerryPal},
	{gBag_PechaBerryTiles, gBag_PechaBerryPal},
	{gBag_RawstBerryTiles, gBag_RawstBerryPal},
	{gBag_AspearBerryTiles, gBag_AspearBerryPal},
	{gBag_LeppaBerryTiles, gBag_LeppaBerryPal},
	{gBag_OranBerryTiles, gBag_OranBerryPal},
	{gBag_PersimBerryTiles, gBag_PersimBerryPal},
	{gBag_LumBerryTiles, gBag_LumBerryPal},
	{gBag_SitrusBerryTiles, gBag_SitrusBerryPal},
	{gBag_FigyBerryTiles, gBag_FigyBerryPal},
	{gBag_WikiBerryTiles, gBag_WikiBerryPal},
	{gBag_MagoBerryTiles, gBag_MagoBerryPal},
	{gBag_AguavBerryTiles, gBag_AguavBerryPal},
	{gBag_IapapaBerryTiles, gBag_IapapaBerryPal},
	{gBag_RazzBerryTiles, gBag_RazzBerryPal},
	{gBag_BlukBerryTiles, gBag_BlukBerryPal},
	{gBag_NanabBerryTiles, gBag_NanabBerryPal},
	{gBag_WepearBerryTiles, gBag_WepearBerryPal},
	{gBag_PinapBerryTiles, gBag_PinapBerryPal},
	{gBag_PomegBerryTiles, gBag_PomegBerryPal},
	{gBag_KelpsyBerryTiles, gBag_KelpsyBerryPal},
	{gBag_QualotBerryTiles, gBag_QualotBerryPal},
	{gBag_HondewBerryTiles, gBag_HondewBerryPal},
	{gBag_GrepaBerryTiles, gBag_GrepaBerryPal},
	{gBag_TamatoBerryTiles, gBag_TamatoBerryPal},
	{gBag_CornnBerryTiles, gBag_CornnBerryPal},
	{gBag_MagostBerryTiles, gBag_MagostBerryPal},
	{gBag_RabutaBerryTiles, gBag_RabutaBerryPal},
	{gBag_NomelBerryTiles, gBag_NomelBerryPal},
	{gBag_SpelonBerryTiles, gBag_SpelonBerryPal},
	{gBag_PamtreBerryTiles, gBag_PamtreBerryPal},
	{gBag_WatmelBerryTiles, gBag_WatmelBerryPal},
	{gBag_DurinBerryTiles, gBag_DurinBerryPal},
	{gBag_BelueBerryTiles, gBag_BelueBerryPal},
	{gBag_LiechiBerryTiles, gBag_LiechiBerryPal},
	{gBag_GanlonBerryTiles, gBag_GanlonBerryPal},
	{gBag_SalacBerryTiles, gBag_SalacBerryPal},
	{gBag_PetayaBerryTiles, gBag_PetayaBerrypal},
	{gBag_ApicotBerryTiles, gBag_ApicotBerryPal},
	{gBag_LansatBerryTiles, gBag_LansatBerryPal},
	{gBag_StarfBerryTiles, gBag_StarfBerryPal},
	{gBag_EnigmaBerryTiles, gBag_EnigmaBerryPal},
	{gBag_PrismScaleTiles, gBag_PrismScalePal},
	{gBag_SachetTiles, gBag_SachetPal},
	{gBag_WhipDreamTiles, gBag_WhipDreamPal},
    {gBag_BrightPowderTiles, gBag_BrightPowderPal},
    {gBag_WhiteHerbTiles, gBag_WhiteHerbPal},
    {gBag_MachoBraceTiles, gBag_MachoBracePal},
    {gBag_ExpShareTiles, gBag_ExpSharePal},
    {gBag_QuickClawTiles, gBag_QuickClawPal},
    {gBag_SootheBellTiles, gBag_SootheBellPal},
    {gBag_MentalHerbTiles, gBag_MentalHerbPal},
    {gBag_ChoiceBandTiles, gBag_ChoiceBandPal},
    {gBag_KingsRockTiles, gBag_KingsRockPal},
    {gBag_SilverPowderTiles, gBag_SilverPowderPal},
    {gBag_AmuletCoinTiles, gBag_AmuletCoinPal},
    {gBag_CleanseTagTiles, gBag_CleanseTagPal},
    {gBag_SoulDewTiles, gBag_SoulDewPal},
    {gBag_DeepSeaToothTiles, gBag_DeepSeaToothPal},
    {gBag_DeepSeaScaleTiles, gBag_DeepSeaScalePal},
    {gBag_SmokeBallTiles, gBag_SmokeBallPal},
    {gBag_EverstoneTiles, gBag_EverstonePal},
    {gBag_FocusBandTiles, gBag_FocusBandPal},
    {gBag_LuckyEggTiles, gBag_LuckyEggPal},
    {gBag_ScopeLensTiles, gBag_ScopeLensPal},
    {gBag_MetalCoatTiles, gBag_MetalCoatPal},
    {gBag_LeftoversTiles, gBag_LeftoversPal},
    {gBag_DragonScaleTiles, gBag_DragonScalePal},
    {gBag_LightBallTiles, gBag_LightBallPal},
    {gBag_SoftSandTiles, gBag_SoftSandPal},
    {gBag_HardStoneTiles, gBag_HardStonePal},
    {gBag_MiracleSeedTiles, gBag_MiracleSeedPal},
    {gBag_BlackGlassesTiles, gBag_BlackGlassesPal},
    {gBag_BlackBeltTiles, gBag_BlackBeltPal},
    {gBag_MagnetTiles, gBag_MagnetPal},
    {gBag_MysticWaterTiles, gBag_MysticWaterPal},
    {gBag_SharpBeakTiles, gBag_SharpBeakPal},
    {gBag_PoisonBarbTiles, gBag_PoisonBarbPal},
    {gBag_NevermelticeTiles, gBag_NevermelticePal},
    {gBag_SpellTagTiles, gBag_SpellTagPal},
    {gBag_TwistedSpoonTiles, gBag_TwistedSpoonPal},
    {gBag_CharcoalTiles, gBag_CharcoalPal},
    {gBag_DragonFangTiles, gBag_DragonFangPal},
    {gBag_SilkScarfTiles, gBag_SilkScarfPal},
    {gBag_UpgradeTiles, gBag_UpgradePal},
    {gBag_ShellBellTiles, gBag_ShellBellPal},
    {gBag_SeaIncenseTiles, gBag_SeaIncensePal},
    {gBag_LaxIncenseTiles, gBag_LaxIncensePal},
    {gBag_LuckyPunchTiles, gBag_LuckyPunchPal},
    {gBag_MetalPowderTiles, gBag_MetalPowderPal},
    {gBag_ThickClubTiles, gBag_ThickClubPal},
    {gBag_LeekTiles, gBag_LeekPal},
	{gBag_StrawberrySweetTiles, gBag_StrawberrySweetPal},
	{gBag_BlueBerrySweetTiles, gBag_BlueBerrySweetPal},
	{gBag_LoveSweetTiles, gBag_LoveSweetPal},
	{gBag_CloverSweetTiles, gBag_CloverSweetPal},
	{gBag_FlowerSweetTiles, gBag_FlowerSweetPal},
	{gBag_RibbonSweetTiles, gBag_RibbonSweetPal},
	{gBag_StarSweetTiles, gBag_StarSweetPal},
	{gBag_SweetAppleTiles, gBag_SweetApplePal},
	{gBag_TartAppleTiles, gBag_TartApplePal},
	{gBagCrackedPotTiles, gBagCrackedPotPal},
	{gBag_ChippedPotTiles, gBag_ChippedPotPal},
	{gBag_GalarianCuffTiles, gBag_GalarianCuffPal},
	{gBag_GalarianCrownTiles, gBag_GalarianCrownPal},
	{gBag_CherishBallTiles, gBag_CherishBallPal},
	{gBag_DuskBallTiles, gBag_DuskBallPal},
	{gBag_HealBallTiles, gBag_HealBallPal},
	{gBag_QuickBallTiles, gBag_QuickBallPal},
	{gBag_FastBallTiles, gBag_FastBallPal},
	{gBag_LevelBallTiles, gBag_LevelBallPal},
	{gBag_LureBallTiles, gBag_LureBallPal},
	{gBag_HeavyBallTiles, gBag_HeavyBallPal},
	{gBag_LoveBallTiles, gBag_LoveBallPal},
	{gBag_FriendBallTiles, gBag_FriendBallPal},
	{gBag_MoonBallTiles, gBag_MoonBallPal},
	{gBag_SportBallTiles, gBag_SportBallPal},
	{gBag_BeastBallTiles, gBag_BeastBallPal},
	{gBag_DreamBallTiles, gBag_DreamBallPal},
	{gBag_ParkBallTiles, gBag_ParkBallPal},
	{gBag_RedScarfTiles, gBag_RedScarfPal},
	{gBag_BlueScarfTiles, gBag_BlueScarfPal},
	{gBag_PinkScarfTiles, gBag_PinkScarfPal},
	{gBag_GreenScarfTiles, gBag_GreenScarfPal},
	{gBag_YellowScarfTiles, gBag_YellowScarfPal},
	{gBag_MachBikeTiles, gBag_MachBikePal},
	{gBag_CoinCaseTiles, gBag_CoinCasePal},
	{gBag_ItemfinderTiles, gBag_ItemfinderPal},
	{gBag_OldRodTiles, gBag_OldRodPal},
	{gBag_GoodRodTiles, gBag_GoodRodPal},
	{gBag_SuperRodTiles, gBag_SuperRodPal},
	{gBag_SSTicketTiles, gBag_SSTicketPal},
	{gBag_ContestPassTiles, gBag_ContestPassPal},
	{gBag_ZPowerRingTiles, gBag_ZPowerRingPal},
	{gBag_WailmerPailTiles, gBag_WailmerPailPal},
	{gBag_DevonGoodsTiles, gBag_DevonGoodsPal},
	{gBag_SootSackTiles, gBag_SootSackPal},
	{gBag_BasementKeyTiles, gBag_BasementKeyPal},
	{gBag_AcroBikeTiles, gBag_AcroBikePal},
	{gBag_PoKeBlLoCkCASETiles, gBag_PoKeBlLoCkCASEPal},
	{gBag_LetterTiles, gBag_LetterPal},
	{gBag_EonTicketTiles, gBag_EonTicketPal},
	{gBag_RedOrbTiles, gBag_RedOrbPal},
	{gBag_BlueOrbTiles, gBag_BlueOrbPal},
	{gBag_ScannerTiles, gBag_ScannerPal},
	{gBag_GoGogglesTiles, gBag_GoGogglesPal},
	{gBag_MeteoriteTiles, gBag_MeteoritePal},
	{gBag_RM1KeyTiles, gBag_RM1KeyPal},
	{gBag_RM2KeyTiles, gBag_RM2KeyPal},
	{gBag_RM4KeyTiles, gBag_RM4KeyPal},
	{gBag_RM6KeyTiles, gBag_RM6KeyPal},
	{gBag_StorageKeyTiles, gBag_StorageKeyPal},
	{gBag_RootFossilTiles, gBag_RootFossilPal},
	{gBag_ClawFossilTiles, gBag_ClawFossilPal},
	{gBag_DevonScopeTiles, gBag_DevonScopePal},
	{gBag_TMTiles, gBag_TMFightingPal},  // ITEM_TM01_FOCUS_PUNCH - Lutador
	{gBag_TMTiles, gBag_TMDragonPal},    // ITEM_TM02_DRAGON_CLAW - Dragão
	{gBag_TMTiles, gBag_TMWaterPal},     // ITEM_TM03_WATER_PULSE - Água
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM04_CALM_MIND - Psíquico
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM05_ROAR - Normal
	{gBag_TMTiles, gBag_TMPoisonPal},    // ITEM_TM06_TOXIC - Veneno
	{gBag_TMTiles, gBag_TMIcePal},       // ITEM_TM07_HAIL - Gelo
	{gBag_TMTiles, gBag_TMFightingPal},  // ITEM_TM08_BULK_UP - Lutador
	{gBag_TMTiles, gBag_TMGrassPal},     // ITEM_TM09_BULLET_SEED - Grama
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM10_HIDDEN_POWER - Normal
	{gBag_TMTiles, gBag_TMFirePal},      // ITEM_TM11_SUNNY_DAY - Fogo
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM12_TAUNT - Sombrio
	{gBag_TMTiles, gBag_TMIcePal},       // ITEM_TM13_ICE_BEAM - Gelo
	{gBag_TMTiles, gBag_TMIcePal},       // ITEM_TM14_BLIZZARD - Gelo
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM15_HYPER_BEAM - Normal
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM16_LIGHT_SCREEN - Psíquico
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM17_PROTECT - Normal
	{gBag_TMTiles, gBag_TMWaterPal},     // ITEM_TM18_RAIN_DANCE - Água
	{gBag_TMTiles, gBag_TMGrassPal},     // ITEM_TM19_GIGA_DRAIN - Grama
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM20_SAFEGUARD - Normal
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM21_FRUSTRATION - Normal
	{gBag_TMTiles, gBag_TMGrassPal},     // ITEM_TM22_SOLARBEAM - Grama
	{gBag_TMTiles, gBag_TMSteelPal},     // ITEM_TM23_IRON_TAIL - Aço
	{gBag_TMTiles, gBag_TMElectricPal},  // ITEM_TM24_THUNDERBOLT - Elétrico
	{gBag_TMTiles, gBag_TMElectricPal},  // ITEM_TM25_THUNDER - Elétrico
	{gBag_TMTiles, gBag_TMGroundPal},    // ITEM_TM26_EARTHQUAKE - Terra
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM27_RETURN - Normal
	{gBag_TMTiles, gBag_TMGroundPal},    // ITEM_TM28_DIG - Terra
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM29_PSYCHIC - Psíquico
	{gBag_TMTiles, gBag_TMGhostPal},     // ITEM_TM30_SHADOW_BALL - Fantasma
	{gBag_TMTiles, gBag_TMFightingPal},  // ITEM_TM31_BRICK_BREAK - Lutador
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM32_DOUBLE_TEAM - Normal
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM33_REFLECT - Psíquico
	{gBag_TMTiles, gBag_TMElectricPal},  // ITEM_TM34_SHOCK_WAVE - Elétrico
	{gBag_TMTiles, gBag_TMFirePal},      // ITEM_TM35_FLAMETHROWER - Fogo
	{gBag_TMTiles, gBag_TMPoisonPal},    // ITEM_TM36_SLUDGE_BOMB - Veneno
	{gBag_TMTiles, gBag_TMRockPal},      // ITEM_TM37_SANDSTORM - Pedra
	{gBag_TMTiles, gBag_TMFirePal},      // ITEM_TM38_FIRE_BLAST - Fogo
	{gBag_TMTiles, gBag_TMRockPal},      // ITEM_TM39_ROCK_TOMB - Pedra
	{gBag_TMTiles, gBag_TMFlyingPal},    // ITEM_TM40_AERIAL_ACE - Voador
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM41_TORMENT - Sombrio
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM42_FACADE - Normal
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM43_SECRET_POWER - Normal
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM44_REST - Psíquico
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM45_ATTRACT - Normal
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM46_THIEF - Sombrio
	{gBag_TMTiles, gBag_TMFlyingPal},    // ITEM_TM47_STEEL_WING - Voador/Aço
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM48_SKILL_SWAP - Psíquico
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM49_SNATCH - Sombrio
	{gBag_TMTiles, gBag_TMFirePal},      // ITEM_TM50_OVERHEAT - Fogo
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_HM01_CUT - Normal
	{gBag_TMTiles, gBag_TMFlyingPal},    // ITEM_HM02_FLY - Voador
	{gBag_TMTiles, gBag_TMWaterPal},     // ITEM_HM03_SURF - Água
	{gBag_TMTiles, gBag_TMNormalPal}, 	 // ITEM_HM04_STRENGTH - Normal
	{gBag_TMTiles, gBag_TMWaterPal},     // ITEM_HM05_DIVE - Água
	{gBag_TMTiles, gBag_TMFightingPal},  // ITEM_HM06_ROCK_SMASH - Lutador
	{gBag_TMTiles, gBag_TMWaterPal},     // ITEM_HM07_WATERFALL - Água
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_HM08_ROCK_CLIMB - Normal
	{gBag_DynamaxBandTiles, gBag_DynamaxBandPal},
	{gBag_GoldTeethTiles, gBag_GoldTeethPal},
	{gBag_OaksParcelTiles, gBag_OaksParcelPal},
	{gBag_PokeFluteTiles, gBag_PokeFlutePal},
	{gBag_SecretKeyTiles, gBag_SecretKeyPal},
	{gBag_BikeVoucherTiles, gBag_BikeVoucherPal},
	{gBag_MegaRingTiles, gBag_MegaRingPal},
	{gBag_OldAmberTiles, gBag_OldAmberPal},
	{gBag_CardKeyTiles, gBag_CardKeyPal},
	{gBag_LiftKeyTiles, gBag_LiftKeyPal},
	{gBag_HelixFossilTiles, gBag_HelixFossilPal},
	{gBag_DomeFossilTiles, gBag_DomeFossilPal},
	{gBag_SilphScopeTiles, gBag_SilphScopePal},
	{gBag_BicycleTiles, gBag_BicyclePal},
	{gBag_TownMapTiles, gBag_TownMapPal},
	{gBag_VSSeekerTiles, gBag_VSSeekerPal},
	{gBag_FameCheckerTiles, gBag_FameCheckerPal},
	{gBag_TMCaseTiles, gBag_TMCasePal},
	{gBag_BerryPouchTiles, gBag_BerryPouchPal},
	{gBag_TeachyTVTiles, gBag_TeachyTVPal},
	{gBag_TriPassTiles, gBag_TriPassPal},
	{gBag_RainbowPassTiles, gBag_RainbowPassPal},
	{gBag_TeaTiles, gBag_TeaPal},
	{gBag_MysticTicketTiles, gBag_MysticTicketPal},
	{gBag_AuroraTicketTiles, gBag_AuroraTicketPal},
	{gBag_PowderJarTiles, gBag_PowderJarPal},
	{gBag_RubyTiles, gBag_RubyPal},
	{gBag_SapphireTiles, gBag_SapphirePal},
	{gBag_InterrogationTiles, gBag_InterrogationPal},
	{gBag_TMTiles, gBag_TMFlyingPal},  	 // ITEM_TM51_MOVE_ROOST - Voador
	{gBag_TMTiles, gBag_TMFightingPal},  // ITEM_TM52_MOVE_FOCUSBLAST - Lutador
	{gBag_TMTiles, gBag_TMGrassPal}, 	 // ITEM_TM53_MOVE_ENERGYBALL - Grama
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM54_MOVE_FALSESWIPE - Normal
	{gBag_TMTiles, gBag_TMWaterPal},  	 // ITEM_TM55_MOVE_BRINE - Agua
	{gBag_TMTiles, gBag_TMDarkPal}, 	 // ITEM_TM56_MOVE_HONECLAWS - Noturno
	{gBag_TMTiles, gBag_TMElectricPal},  // ITEM_TM57_MOVE_CHARGEBEAM - Eletrico
	{gBag_TMTiles, gBag_TMNormalPal},  	 // ITEM_TM58_MOVE_ENDURE - Normal
	{gBag_TMTiles, gBag_TMDragonPal},    // ITEM_TM59_DRAGON_PULSE - Dragão
	{gBag_TMTiles, gBag_TMFightingPal},  // ITEM_TM60_DRAIN_PUNCH - Lutador
	{gBag_TMTiles, gBag_TMFirePal},   	 // ITEM_TM61_WILL_O_WISP - Fogo
	{gBag_TMTiles, gBag_TMBugPal},       // ITEM_TM62_SILVER_WIND - Inseto
	{gBag_TMTiles, gBag_TMPoisonPal},    // ITEM_TM63_VENOSHOCK - Veneno
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM64_EXPLOSION - Normal
	{gBag_TMTiles, gBag_TMGhostPal},     // ITEM_TM65_SHADOW_CLAW - Fantasma
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM66_PAYBACK - Sombrio
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM67_RECYCLE - Normal
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM68_GIGA_IMPACT - Normal
	{gBag_TMTiles, gBag_TMRockPal},      // ITEM_TM69_ROCK_POLISH - Pedra
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM70_FLASH - Normal
	{gBag_TMTiles, gBag_TMRockPal},      // ITEM_TM71_STONE_EDGE - Pedra
	{gBag_TMTiles, gBag_TMIcePal},       // ITEM_TM72_AVALANCHE - Gelo
	{gBag_TMTiles, gBag_TMElectricPal},  // ITEM_TM73_THUNDER_WAVE - Elétrico
	{gBag_TMTiles, gBag_TMSteelPal},     // ITEM_TM74_GYRO_BALL - Aço
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM75_SWORDS_DANCE - Normal
	{gBag_TMTiles, gBag_TMRockPal},      // ITEM_TM76_STEALTH_ROCK - Pedra
	{gBag_TMTiles, gBag_TMFirePal},      // ITEM_TM77_FLAME_CHARGE - Fogo
	{gBag_TMTiles, gBag_TMFightingPal},  // ITEM_TM78_LOW_SWEEP - Lutador
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM79_DARK_PULSE - Sombrio
	{gBag_TMTiles, gBag_TMRockPal},      // ITEM_TM80_ROCK_SLIDE - Pedra
	{gBag_TMTiles, gBag_TMBugPal},       // ITEM_TM81_X_SCISSOR - Inseto
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM82_SLEEP_TALK - Normal
	{gBag_TMTiles, gBag_TMWaterPal},     // ITEM_TM83_SCALD - Água
	{gBag_TMTiles, gBag_TMPoisonPal},    // ITEM_TM84_POISON_JAB - Veneno
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM85_DREAM_EATER - Psíquico
	{gBag_TMTiles, gBag_TMGrassPal},     // ITEM_TM86_GRASS_KNOT - Grama
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM87_SWAGGER - Normal
	{gBag_TMTiles, gBag_TMFlyingPal},    // ITEM_TM88_PLUCK - Voador
	{gBag_TMTiles, gBag_TMBugPal},       // ITEM_TM89_U_TURN - Inseto
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM90_SUBSTITUTE - Normal
	{gBag_TMTiles, gBag_TMSteelPal},     // ITEM_TM91_FLASH_CANNON - Aço
	{gBag_TMTiles, gBag_TMElectricPal},  // ITEM_TM92_VOLT_SWITCH - Elétrico
	{gBag_TMTiles, gBag_TMDragonPal},    // ITEM_TM93_DRAGON_TAIL - Dragão
	{gBag_TMTiles, gBag_TMFirePal},      // ITEM_TM94_INCINERATE - Fogo
	{gBag_TMTiles, gBag_TMBugPal},       // ITEM_TM95_STRUGGLE_BUG - Inseto
	{gBag_TMTiles, gBag_TMGroundPal},    // ITEM_TM96_BULLDOZE - Terra
	{gBag_TMTiles, gBag_TMIcePal},       // ITEM_TM97_FROST_BREATH - Gelo
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM98_WORK_UP - Normal
	{gBag_TMTiles, gBag_TMElectricPal},  // ITEM_TM99_WILD_CHARGE - Elétrico
	{gBag_TMTiles, gBag_TMBugPal},       // ITEM_TM100_INFESTATION - Inseto
	{gBag_TMTiles, gBag_TMFightingPal},  // ITEM_TM101_POWER_UP_PUNCH - Lutador
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM102_DAZZLING_GLEAM - Fada
	{gBag_TMTiles, gBag_TMPoisonPal},    // ITEM_TM103_SLUDGE_WAVE - Veneno
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM104_PSYSHOCK - Psíquico
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM105_BRUTAL_SWING - Sombrio
	{gBag_TMTiles, gBag_TMSteelPal},     // ITEM_TM106_SMART_STRIKE - Aço
	{gBag_TMTiles, gBag_TMFlyingPal},    // ITEM_TM107_ACROBATICS - Voador
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM108_SNARL - Sombrio
	{gBag_TMTiles, gBag_TMFlyingPal},    // ITEM_TM109_DEFOG - Voador
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM110_CAPTIVATE - Normal
	{gBag_TMTiles, gBag_TMRockPal},      // ITEM_TM111_SMACK_DOWN - Pedra
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM112_ROUND - Normal
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM113_ECHOED_VOICE - Normal
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM114_NATURAL_GIFT - Normal
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM115_QUASH - Sombrio
	{gBag_TMTiles, gBag_TMPsychicPal},   // ITEM_TM116_TRICK_ROOM - Psíquico
	{gBag_TMTiles, gBag_TMDarkPal},      // ITEM_TM117_FLING - Sombrio
	{gBag_TMTiles, gBag_TMIcePal},       // ITEM_TM118_AURORA_VEIL - Gelo
	{gBag_TMTiles, gBag_TMFlyingPal},    // ITEM_TM119_SKY_DROP - Voador
	{gBag_TMTiles, gBag_TMNormalPal},    // ITEM_TM120_NATURE_POWER - Normal
	{gBag_OccaBerryTiles, gBag_OccaBerryPal},
	{gBag_PasshoBerryTiles, gBag_PasshoBerryPal},
	{gBag_WacanBerryTiles, gBag_WacanBerryPal},
	{gBag_RindoBerryTiles, gBag_RindoBerryPal},
	{gBag_YacheBerryTiles, gBag_YacheBerryPal},
	{gBag_ChopleBerryTiles, gBag_ChopleBerryPal},
	{gBag_KebiaBerryTiles, gBag_KebiaBerryPal},
	{gBag_ShucaBerryTiles, gBag_ShucaBerryPal},
	{gBag_CobaBerryTiles, gBag_CobaBerryPal},
	{gBag_PapayaBerryTiles, gBag_PapayaBerryPal},
	{gBag_TangaBerryTiles, gBag_TangaBerryPal},
	{gBag_ChartiBerryTiles, gBag_ChartiBerryPal},
	{gBag_KasibBerryTiles, gBag_KasibBerryPal},
	{gBag_HabanBerryTiles, gBag_HabanBerryPal},
	{gBag_ColburBerryTiles, gBag_ColburBerryPal},
	{gBag_RabiriBerryTiles, gBag_RabiriBerryPal},
	{gBag_ChilanBerryTiles, gBag_ChilanBerryPal},
	{gBag_MicleBerryTiles, gBag_MicleBerryPal},
	{gBag_CustapBerryTiles, gBag_CustapBerryPal},
	{gBag_JacobaBerryTiles, gBag_JacobaBerryPal},
	{gBag_RowapBerryTiles, gBag_RowapBerryPal},
	{gBag_RoseliBerryTiles, gBag_RoseliBerryPal},
	{gBag_KeeBerryTiles, gBag_KeeBerryPal},
	{gBag_MarangBerryTiles, gBag_MarangBerryPal},
	{gBag_OvalCharmTiles, gBag_OvalCharmPal},
	{gBag_ShinyCharmTiles, gBag_ShinyCharmPal},
	{gBag_RainbowWingTiles, gBag_RainbowWingPal},
	{gBag_SilverWingTiles, gBag_SilverWingPal},
	{gBag_MagmaStoneTiles, gBag_MagmaStonePal},
	{gBag_LightStoneTiles, gBag_LightStonePal},
	{gBag_DarkStoneTiles, gBag_DarkStonePal},
	{gBag_SunFluteTiles, gBag_SunFlutePal},
	{gBag_MoonFluteTiles, gBag_MoonFlutePal},
	{gBag_GracideaTiles, gBag_GracideaPal},
	{gBag_DNASplicesTiles, gBag_DNASplicesPal},
	{gBag_RevealGlassTiles, gBag_RevealGlassPal},
	{gBag_PrisonBottleTiles, gBag_PrisonBottlePal},
	{gBag_NSolarizerTiles, gBag_NSolarizerPal},
	{gBag_NLunarizerTiles, gBag_NLunarizerPal},
	{gBag_RustedSwordTiles, gBag_RustedSwordPal},
	{gBag_RustedShieldTiles, gBag_RustedShieldPal},
	{gBag_AdamantOrbTiles, gBag_AdamantOrbPal},
	{gBag_LustrousOrbTiles, gBag_LustrousOrbPal},
	{gBag_GriseousOrbTiles, gBag_GriseousOrbPal},
	{gBag_FistPlateTiles, gBag_FistPlatePal},
	{gBag_SkyPlateTiles, gBag_SkyPlatePal},
	{gBag_ToxicPlateTiles, gBag_ToxicPlatePal},
	{gBag_EarthPlateTiles, gBag_EarthPlatePal},
	{gBag_StonePlateTiles, gBag_StonePlatePal},
	{gBag_InsectPlateTiles, gBag_InsectPlatePal},
	{gBag_SpookyPlateTiles, gBag_SpookyPlatePal},
	{gBag_IronPlateTiles, gBag_IronPlatePal},
	{gBag_FlamePlateTiles, gBag_FlamePlatePal},
	{gBag_SplashPlateTiles, gBag_SplashPlatePal},
	{gBag_MeadowPlateTiles, gBag_MeadowPlatePal},
	{gBag_ZapPlateTiles, gBag_ZapPlatePal},
	{gBag_MindPlateTiles, gBag_MindPlatePal},
	{gBag_IciclePlateTiles, gBag_IciclePlatePal},
	{gBag_DracoPlateTiles, gBag_DracoPlatePal},
	{gBag_DreadPlateTiles, gBag_DreadPlatePal},
	{gBag_PixiePlateTiles, gBag_PixiePlatePal},
	{gBag_FightingMemoryTiles, gBag_FightingMemoryPal},
	{gBag_FlyingMemoryTiles, gBag_FlyingMemoryPal},
	{gBag_PoisonMemoryTiles, gBag_PoisonMemoryPal},
	{gBag_GroundMemoryTiles, gBag_GroundMemoryPal},
	{gBag_RockMemoryTiles, gBag_RockMemoryPal},
	{gBag_BugMemoryTiles, gBag_BugMemoryPal},
	{gBag_GhostMemoryTiles, gBag_GhostMemoryPal},
	{gBag_SteelMemoryTiles, gBag_SteelMemoryPal},
	{gBag_FireMemoryTiles, gBag_FireMemoryPal},
	{gBag_WaterMemoryTiles, gBag_WaterMemoryPal},
	{gBag_GrassMemoryTiles, gBag_GrassMemoryPal},
	{gBag_ElectricMemoryTiles, gBag_ElectricMemoryPal},
	{gBag_PsychicMemoryTiles, gBag_PsychicMemoryPal},
	{gBag_IceMemoryTiles, gBag_IceMemoryPal},
	{gBag_DragonMemoryTiles, gBag_DragonMemoryPal},
	{gBag_DarkMemoryTiles, gBag_DarkMemoryPal},
	{gBag_FairyMemoryTiles, gBag_FairyMemoryPal},
	{gBag_BurnDriveTiles, gBag_BurnDrivePal},
	{gBag_DouseDriveTiles, gBag_DouseDrivePal},
	{gBag_ShockDriveTiles, gBag_ShockDrivePal},
	{gBag_ChillDriveTiles, gBag_ChillDrivePal},
	{gBag_RedNectarTiles, gBag_RedNectarPal},
	{gBag_YellowNectarTiles, gBag_YellowNectarPal},
	{gBag_PinkNectarTiles, gBag_PinkNectarPal},
	{gBag_PurpleNectarTiles, gBag_PurpleNectarPal},
	{gBag_NecroziumZTiles, gBag_NecroziumZPal},
	{gBag_VenusauriteTiles, gBag_VenusauritePal},
	{gBag_CharizarditeXTiles, gBag_CharizarditeXPal},
	{gBag_CharizarditeYTiles, gBag_CharizarditeYPal},
	{gBag_BlastoiseniteTiles, gBag_BlastoisenitePal},
	{gBag_BeedrilliteTiles, gBag_BeedrillitePal},
	{gBag_PidgeotiteTiles, gBag_PidgeotitePal},
	{gBag_AlakaziteTiles, gBag_AlakazitePal},
	{gBag_SlowbroniteTiles, gBag_SlowbronitePal},
	{gBag_GengariteTiles, gBag_GengaritePal},
	{gBag_KangaskaniteTiles, gBag_KangaskanitePal},
	{gBag_PinsiriteTiles, gBag_PinsiritePal},
	{gBag_GyaradositeTiles, gBag_GyaradositePal},
	{gBag_AerodactliteTiles, gBag_AerodactlitePal},
	{gBag_MewtwoniteXTiles, gBag_MewtwoniteXPal},
	{gBag_MewtwoniteYTiles, gBag_MewtwoniteYPal},
	{gBag_AmpharositeTiles, gBag_AmpharositePal},
	{gBag_SteelixiteTiles, gBag_SteelixitePal},
	{gBag_ScizoriteTiles, gBag_ScizoritePal},
	{gBag_HeracroniteTiles, gBag_HeracronitePal},
	{gBag_HoundoomniteTiles, gBag_HoundoomnitePal},
	{gBag_TyranitariteTiles, gBag_TyranitaritePal},
	{gBag_SceptiliteTiles, gBag_SceptilitePal},
	{gBag_BlazikeniteTiles, gBag_BlazikenitePal},
	{gBag_SwampertiteTiles, gBag_SwampertitePal},
	{gBag_GardevoiriteTiles, gBag_GardevoiritePal},
	{gBag_SableniteTiles, gBag_SablenitePal},
	{gBag_MawiliteTiles, gBag_MawilitePal},
	{gBag_AggroniteTiles, gBag_AggronitePal},
	{gBag_MedichamiteTiles, gBag_MedichamitePal},
	{gBag_ManectiteTiles, gBag_ManectitePal},
	{gBag_SharpedoniteTiles, gBag_SharpedonitePal},
	{gBag_CameruptiteTiles, gBag_CameruptitePal},
	{gBag_AltarianiteTiles, gBag_AltarianitePal},
	{gBag_BanettiteTiles, gBag_BanettitePal},
	{gBag_AbsoliteTiles, gBag_AbsolitePal},
	{gBag_GlalititeTiles, gBag_GlalititePal},
	{gBag_SalamenciteTiles, gBag_SalamencitePal},
	{gBag_MetagrossiteTiles, gBag_MetagrossitePal},
	{gBag_LatiasiteTiles, gBag_LatiasitePal},
	{gBag_LatiositeTiles, gBag_LatiositePal},
	{gBag_LopunniteTiles, gBag_LopunnitePal},
	{gBag_GarchompiteTiles, gBag_GarchompitePal},
	{gBag_LucarioniteTiles, gBag_LucarionitePal},
	{gBag_AbomasiteTiles, gBag_AbomasitePal},
	{gBag_GalladiteTiles, gBag_GalladitePal},
	{gBag_AudiniteTiles, gBag_AudinitePal},
	{gBag_DianciteTiles, gBag_DiancitePal},
	{gBag_NormaliumZTiles, gBag_NormaliumZPal},
	{gBag_FightiumZTiles, gBag_FightiumZPal},
	{gBag_FlyiniumZTiles, gBag_FlyiniumZPal},
	{gBag_PoisoniumZTiles, gBag_PoisoniumZPal},
	{gBag_GroundiumZTiles, gBag_GroundiumZPal},
	{gBag_RockiumZTiles, gBag_RockiumZPal},
	{gBag_BuginiumZTiles, gBag_BuginiumZPal},
	{gBag_GhostiumZTiles, gBag_GhostiumZPal},
	{gBag_SteeliumZTiles, gBag_SteeliumZPal},
	{gBag_FiriumZTiles, gBag_FiriumZPal},
	{gBag_WateriumZTiles, gBag_WateriumZPal},
	{gBag_GrassiumZTiles, gBag_GrassiumZPal},
	{gBag_ElectriumZTiles, gBag_ElectriumZPal},
	{gBag_PsychiumZTiles, gBag_PsychiumZPal},
	{gBag_IciumZTiles, gBag_IciumZPal},
	{gBag_DragoniumZTiles, gBag_DragoniumZPal},
	{gBag_DarkiniumZTiles, gBag_DarkiniumZPal},
	{gBag_FairiumZTiles, gBag_FairiumZPal},
	{gBag_AlorichiumZTiles, gBag_AlorichiumZPal},
	{gBag_DecidiumZTiles, gBag_DecidiumZPal},
	{gBag_EeviumZTiles, gBag_EeviumZPal},
	{gBag_InciniumZTiles, gBag_InciniumZPal},
	{gBag_KommoniumZTiles, gBag_KommoniumZPal},
	{gBag_LunaliumZTiles, gBag_LunaliumZPal},
	{gBag_LycaniumZTiles, gBag_LycaniumZPal},
	{gBag_MarshadiumZTiles, gBag_MarshadiumZPal},
	{gBag_MewniumZTiles, gBag_MewniumZPal},
	{gBag_MimikiumZTiles, gBag_MimikiumZPal},
	{gBag_PikaniumZTiles, gBag_PikaniumZPal},
	{gBag_PikashuniumZTiles, gBag_PikashuniumZPal},
	{gBag_PrimariumZTiles, gBag_PrimariumZPal},
	{gBag_SnorliumZTiles, gBag_SnorliumZPal},
	{gBag_SolganiumZTiles, gBag_SolganiumZPal},
	{gBag_TapuniumZTiles, gBag_TapuniumZPal},
	{gBag_Black_AppricornTiles, gBag_Black_AppricornPal},
	{gBag_Blue_AppricornTiles, gBag_Blue_AppricornPal},
	{gBag_Green_AppricornTiles, gBag_Green_AppricornPal},
	{gBag_Pink_AppricornTiles, gBag_Pink_AppricornPal},
	{gBag_RedApricornTiles, gBag_RedApricornPal},
	{gBag_White_AppricornTiles, gBag_White_AppricornPal},
	{gBag_YellowApricornTiles, gBag_YellowApricornPal},
	{gBag_RelicCooperTiles, gBag_RelicCooperPal},
	{gBag_RelicSilverTiles, gBag_RelicSilverPal},
	{gBag_RelicGoldTiles, gBag_RelicGoldPal},
	{gBag_RelicVaseTiles, gBag_RelicVasePal},
	{gBag_RelicBandTiles, gBag_RelicBandPal},
	{gBag_RelicStatueTiles, gBag_RelicStatuePal},
	{gBag_RelicCrownTiles, gBag_RelicCrownPal},
	{gBag_SkullFossilTiles, gBag_SkullFossilPal},
	{gBag_ArmorFossilTiles, gBag_ArmorFossilPal},
	{gBag_CoverFossilTiles, gBag_CoverFossilPal},
	{gBag_PlumeFossilTiles, gBag_PlumeFossilPal},
	{gBag_JawFossilTiles, gBag_JawFossilPal},
	{gBag_SailFossilTiles, gBag_SailFossilPal},
	{gBag_BirdFossilTiles, gBag_BirdFossilPal},
	{gBag_FishFossilTiles, gBag_FishFossilPal},
	{gBag_DrakeFossilTiles, gBag_DrakeFossilPal},
	{gBag_DinoFossilTiles, gBag_DinoFossilPal},
	{gBag_OddKeystoneTiles, gBag_OddKeystonePal},
	{gBag_BottleCapTiles, gBag_BottleCapPal},
	{gBag_GoldBottleCapTiles, gBag_GoldBottleCapPal},
	{gBag_WishPieceTiles, gBag_WishPiecePal},
	{gBag_PowerBracerTiles, gBag_PowerBracerPal},
	{gBag_PowerBeltTiles, gBag_PowerBeltPal},
	{gBag_PowerLensTiles, gBag_PowerLensPal},
	{gBag_PowerBandTiles, gBag_PowerBandPal},
	{gBag_PowerAnkletTiles, gBag_PowerAnkletPal},
	{gBag_PowerWeighTiles, gBag_PowerWeightPal},
	{gBag_LuckIncenseTiles, gBag_LuckIncensePal},
	{gBag_FullIncenseTiles, gBag_FullIncensePal},
	{gBag_OddIncenseTiles, gBag_OddIncensePal},
	{gBag_PureIncenseTiles, gBag_PureIncensePal},
	{gBag_RockIncenseTiles, gBag_RockIncensePal},
	{gBag_RoseIncenseTiles, gBag_RoseIncensePal},
	{gBag_WaveIncenseTiles, gBag_WaveIncensePal},
	{gBag_NormalGemTiles, gBag_NormalGemPal},
	{gBag_FightingGemTiles, gBag_FightingGemPal},
	{gBag_FlyingGemTiles, gBag_FlyingGemPal},
	{gBag_PoisonGemTiles, gBag_PoisonGemPal},
	{gBag_GroundGemTiles, gBag_GroundGemPal},
	{gBag_RockGemTiles, gBag_RockGemPal},
	{gBag_BugGemTiles, gBag_BugGemPal},
	{gBag_GhostGemTiles, gBag_GhostGemPal},
	{gBag_SteelGemTiles, gBag_SteelGemPal},
	{gBag_FireGemTiles, gBag_FireGemPal},
	{gBag_WaterGemTiles, gBag_WaterGemPal},
	{gBag_GrassGemTiles, gBag_GrassGemPal},
	{gBag_ElectricGemTiles, gBag_ElectricGemPal},
	{gBag_PsychicGemTiles, gBag_PsychicGemPal},
	{gBag_IceGemTiles, gBag_IceGemPal},
	{gBag_DragonGemTiles, gBag_DragonGemPal},
	{gBag_DarkGemTiles, gBag_DarkGemPal},
	{gBag_FairyGemTiles, gBag_FairyGemPal},
	{gBag_WideLensTiles, gBag_WideLensPal},
	{gBag_MuscleBandTiles, gBag_MuscleBandPal},
	{gBag_WiseGlassesTiles, gBag_WiseGlassesPal},
	{gBag_ExpertBeltTiles, gBag_ExpertBeltPal},
	{gBag_LightClayTiles, gBag_LightClayPal},
	{gBag_LifeOrbTiles, gBag_LifeOrbPal},
	{gBag_PowerHerbTiles, gBag_PowerHerbPal},
	{gBag_ToxicOrbTiles, gBag_ToxicOrbPal},
	{gBag_FlameOrbTiles, gBag_FlameOrbPal},
	{gBag_QuickPowderTiles, gBag_QuickPowderPal},
	{gBag_FocusSashTiles, gBag_FocusSashPal},
	{gBag_ZoomLensTiles, gBag_ZoomLensPal},
	{gBag_MetronomeTiles, gBag_MetronomePal},
	{gBag_IronBallTiles, gBag_IronBallPal},
	{gBag_LaggingTailTiles, gBag_LaggingTailPal},
	{gBag_DestinyKnotTiles, gBag_DestinyKnotPal},
	{gBag_BlackSludgeTiles, gBag_BlackSludgePal},
	{gBag_IcyRockTiles, gBag_IcyRockPal},
	{gBag_SmoothRockTiles, gBag_SmoothRockPal},
	{gBag_HeatRockTiles, gBag_HeatRockPal},
	{gBag_DampRockTiles, gBag_DampRockPal},
	{gBag_GripClawTiles, gBag_GripClawPal},
	{gBag_ChoiceScarfTiles, gBag_ChoiceScarfPal},
	{gBag_ChoiceSpecsTiles, gBag_ChoiceSpecsPal},
	{gBag_StickyBarbTiles, gBag_StickyBarbPal},
	{gBag_ShedShellTiles, gBag_ShedShellPal},
	{gBag_BigRootTiles, gBag_BigRootPal},
	{gBag_EvioliteTiles, gBag_EviolitePal},
	{gBag_FloatStoneTiles, gBag_FloatStonePal},
	{gBag_RockyHelmetTiles, gBag_RockyHelmetPal},
	{gBag_AirBalloomTiles, gBag_AirBalloomPal},
	{gBag_RedCardTiles, gBag_RedCardPal},
	{gBag_RingTargetTiles, gBag_RingTargetPal},
	{gBag_BindingBandTiles, gBag_BindingBandPal},
	{gBag_AbsorbBulbTiles, gBag_AbsorbBulbPal},
	{gBag_CellBatteryTiles, gBag_CellBatteryPal},
	{gBag_EjectButtonTiles, gBag_EjectButtonPal},
	{gBag_WeaknessPolicyTiles, gBag_WeaknessPolicyPal},
	{gBag_AssaultVestTiles, gBag_AssaultVestPal},
	{gBag_LuminousMossTiles, gBag_LuminousMossPal},
	{gBag_SnowballTiles, gBag_SnowballPal},
	{gBag_SafeGogglesTiles, gBag_SafeGogglesPal},
	{gBag_AdrenalOrbTiles, gBag_AdrenalOrbPal},
	{gBag_TerrainExtenderTiles, gBag_TerrainExtenderPal},
	{gBag_ProtectivePadsTiles, gBag_ProtectivePadsPal},
	{gBag_ElectricSeedTiles, gBag_ElectricSeedPal},
	{gBag_GrassySeedTiles, gBag_GrassySeedPal},
	{gBag_MistySeedTiles, gBag_MistySeedPal},
	{gBag_PsychicSeedTiles, gBag_PsychicSeedPal},
	{gBag_EjectPackTiles, gBag_EjectPackPal},
	{gBag_RoomServiceTiles, gBag_RoomServicePal},
	{gBag_BlunderPolicyTiles, gBag_BlunderPolicyPal},
	{gBag_HeavyDBootsTiles, gBag_HeavyDBootsPal},
	{gBag_UtUmbrellaTiles, gBag_UtUmbrellaPal},
	{gBag_ThroatSprayTiles, gBag_ThroatSprayPal},
	{gBag_AbilityPotionTiles, gBag_AbilityPotionPal},
	{gBag_AbilityPatchTiles, gBag_AbilityPatchPal},
	{Bag_Auspicious_Armor_SpriteTiles, Bag_Auspicious_Armor_SpritePal},
	{Bag_Malicious_Armor_SpriteTiles, Bag_Malicious_Armor_SpritePal},
	{Bag_Black_Augurite_SpriteTiles, Bag_Black_Augurite_SpritePal},
	{Bag_Metal_AlloyTiles, Bag_Metal_AlloyPal},
	{Bag_Peat_Block_SpriteTiles, Bag_Peat_Block_SpritePal},
	{gBagItem_Leaders_CrestTiles, gBagItem_Leaders_CrestPal},
	{gBagItem_Gimmi_CoinTiles, gBagItem_Gimmi_CoinPal},
	{Bag_Syrypy_AppleTiles, Bag_Syrypy_ApplePal},
	{gBagItem_Clear_AmuletTiles, gBagItem_Clear_AmuletPal},
	{gBagItem_Covert_CloakTiles, gBagItem_Covert_CloakPal},
	{gBagItem_Loaded_DiceTiles, gBagItem_Loaded_DicePal},
	{gBagItem_Puching_GlovesTiles, gBagItem_Puching_GlovesPal},
	{gBagItem_Reins_of_UnityTiles, gBagItem_Reins_of_UnityPal},
	{gBagItem_Booster_EnergyTiles, gBagItem_Booster_EnergyPal},
	{gBagItem_Wellspring_MaskTiles, gBagItem_Wellspring_MaskPal},
	{gBagItem_Hearthflame_MaskTiles, gBagItem_Hearthflame_MaskPal},
	{gBagItem_Cornerstone_MaskTiles, gBagItem_Cornerstone_MaskPal},
	{gBagItem_Red_MintTiles, gBagItem_Red_MintPal},
	{gBagItem_Red_MintTiles, gBagItem_Red_MintPal},
	{gBagItem_Red_MintTiles, gBagItem_Red_MintPal},
	{gBagItem_Red_MintTiles, gBagItem_Red_MintPal},
	{gBagItem_Purple_MintTiles, gBagItem_Purple_MintPal},
	{gBagItem_Purple_MintTiles, gBagItem_Purple_MintPal},
	{gBagItem_Purple_MintTiles, gBagItem_Purple_MintPal},
	{gBagItem_Purple_MintTiles, gBagItem_Purple_MintPal},
	{gBagItem_Blue_MintTiles, gBagItem_Blue_MintPal},
	{gBagItem_Blue_MintTiles, gBagItem_Blue_MintPal},
	{gBagItem_Blue_MintTiles, gBagItem_Blue_MintPal},
	{gBagItem_Blue_MintTiles, gBagItem_Blue_MintPal},
	{gBagItem_Pink_MintTiles, gBagItem_Pink_MintPal},
	{gBagItem_Pink_MintTiles, gBagItem_Pink_MintPal},
	{gBagItem_Pink_MintTiles, gBagItem_Pink_MintPal},
	{gBagItem_Pink_MintTiles, gBagItem_Pink_MintPal},
	{gBagItem_Green_MintTiles, gBagItem_Green_MintPal},
	{gBagItem_Green_MintTiles, gBagItem_Green_MintPal},
	{gBagItem_Green_MintTiles, gBagItem_Green_MintPal},
	{gBagItem_Green_MintTiles, gBagItem_Green_MintPal},
	{gBagItem_Yellow_MintTiles, gBagItem_Yellow_MintPal},
	{gBagItem_PokevialTiles, gBagItem_PokevialPal},
	{gBag_FameCheckerTiles, gBag_TriPassPal},
	{gBag_InterrogationTiles, gBag_InterrogationPal},
	{gBag_InterrogationTiles, gBag_InterrogationPal},
	{gBag_InterrogationTiles, gBag_InterrogationPal},
	
};

const struct Item gItemData[] =
{
	{
		.name = {_QUESTION, _QUESTION, _QUESTION, _QUESTION, _QUESTION, _QUESTION, _QUESTION, _QUESTION, _END},
		.itemId = ITEM_NONE,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = gText_ItemNone,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _s, _t, _e, _r, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_MASTER_BALL,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_MASTER_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 0,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_U, _l, _t, _r, _a, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_ULTRA_BALL,
		.price = 800,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ULTRA_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 1,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 1
	},
	{
		.name = {_G, _r, _e, _a, _t, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_GREAT_BALL,
		.price = 600,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GREAT_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 2,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 2
	},
	{
		.name = {_P, _o, _k, _eACUTE, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_POKE_BALL,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_POKE_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 3,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 3
	},
	{
		.name = {_S, _a, _f, _a, _r, _i, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_SAFARI_BALL,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SAFARI_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 4,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 4
	},
	{
		.name = {_N, _e, _t, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_NET_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_NET_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 5,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 5
	},
	{
		.name = {_D, _i, _v, _e, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_DIVE_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DIVE_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 6,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 6
	},
	{
		.name = {_N, _e, _s, _t, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_NEST_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_NEST_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 7,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 7
	},
	{
		.name = {_R, _e, _p, _e, _a, _t, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_REPEAT_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_REPEAT_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 8,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 8
	},
	{
		.name = {_T, _i, _m, _e, _r, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_TIMER_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_TIMER_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 9,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 9
	},
	{
		.name = {_L, _u, _x, _u, _r, _y, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_LUXURY_BALL,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LUXURY_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 10,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 10
	},
	{
		.name = {_P, _r, _e, _m, _i, _e, _r, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_PREMIER_BALL,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PREMIER_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 11,
		.fieldUseFunc = 0,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 11
	},
	{
		.name = {_P, _o, _t, _i, _o, _n, _END},
		.itemId = ITEM_POTION,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 20,
		.description = DESC_POTION,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_A, _n, _t, _i, _d, _o, _t, _e, _END},
		.itemId = ITEM_ANTIDOTE,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ANTIDOTE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_B, _u, _r, _n, _SPACE, _H, _e, _a, _l, _END},
		.itemId = ITEM_BURN_HEAL,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BURN_HEAL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_I, _c, _e, _SPACE, _H, _e, _a, _l, _END},
		.itemId = ITEM_ICE_HEAL,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ICE_HEAL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_A, _w, _a, _k, _e, _n, _i, _n, _g, _END},
		.itemId = ITEM_AWAKENING,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_AWAKENING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_P, _a, _r, _a, _l, _y, _z, _SPACE, _H, _e, _a, _l, _END, _END},
		.itemId = ITEM_PARALYZE_HEAL,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PARALYZE_HEAL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_F, _u, _l, _l, _SPACE, _R, _e, _s, _t, _o, _r, _e, _END},
		.itemId = ITEM_FULL_RESTORE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 255,
		.description = DESC_FULL_RESTORE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _x, _SPACE, _P, _o, _t, _i, _o, _n, _END},
		.itemId = ITEM_MAX_POTION,
		.price = 2500,
		.holdEffect = 0,
        .holdEffectParam = 255,
		.description = DESC_MAX_POTION,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_H, _y, _p, _e, _r, _SPACE, _P, _o, _t, _i, _o, _n, _END},
		.itemId = ITEM_HYPER_POTION,
		.price = 1500,
		.holdEffect = 0,
        .holdEffectParam = 200,
		.description = DESC_HYPER_POTION,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_S, _u, _p, _e, _r, _SPACE, _P, _o, _t, _i, _o, _n, _END},
		.itemId = ITEM_SUPER_POTION,
		.price = 700,
		.holdEffect = 0,
        .holdEffectParam = 50,
		.description = DESC_SUPER_POTION,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_F, _u, _l, _l, _SPACE, _H, _e, _a, _l, _END},
		.itemId = ITEM_FULL_HEAL,
		.price = 400,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_FULL_HEAL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _v, _i, _v, _e, _END},
		.itemId = ITEM_REVIVE,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_REVIVE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _x, _SPACE, _R, _e, _v, _i, _v, _e, _END},
		.itemId = ITEM_MAX_REVIVE,
		.price = 4000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_MAX_REVIVE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_F, _r, _e, _s, _h, _SPACE, _W, _a, _t, _e, _r, _END},
		.itemId = ITEM_FRESH_WATER,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 50,
		.description = DESC_FRESH_WATER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_S, _o, _d, _a, _SPACE, _P, _o, _p, _END},
		.itemId = ITEM_SODA_POP,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 60,
		.description = DESC_SODA_POP,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_L, _e, _m, _o, _n, _a, _d, _e, _END},
		.itemId = ITEM_LEMONADE,
		.price = 400,
		.holdEffect = 0,
        .holdEffectParam = 80,
		.description = DESC_LEMONADE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_M, _o, _o, _m, _o, _o, _SPACE, _M, _i, _l, _k, _END},
		.itemId = ITEM_MOOMOO_MILK,
		.price = 600,
		.holdEffect = 0,
        .holdEffectParam = 100,
		.description = DESC_MOOMOO_MILK,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_E, _n, _e, _r, _g, _y, _SPACE, _P, _o, _w, _d, _e, _r, _END},
		.itemId = ITEM_ENERGY_POWDER,
		.price = 500,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ENERGY_POWDER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_E, _n, _e, _r, _g, _y, _SPACE, _R, _o, _o, _t, _END},
		.itemId = ITEM_ENERGY_ROOT,
		.price = 1200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ENERGY_ROOT,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_H, _e, _a, _l, _SPACE, _P, _o, _w, _d, _e, _r, _END},
		.itemId = ITEM_HEAL_POWDER,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_HEAL_POWDER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _v, _i, _v, _a, _l, _SPACE, _H, _e, _r, _b, _END},
		.itemId = ITEM_REVIVAL_HERB,
		.price = 2800,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_REVIVAL_HERB,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_E, _t, _h, _e, _r, _END},
		.itemId = ITEM_ETHER,
		.price = 1200,
		.holdEffect = 0,
        .holdEffectParam = 10,
		.description = DESC_ETHER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Ether,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Ether,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _x, _SPACE, _E, _t, _h, _e, _r, _END},
		.itemId = ITEM_MAX_ETHER,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 255,
		.description = DESC_MAX_ETHER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Ether,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Ether,
        .secondaryId = 0
	},
	{
		.name = {_E, _l, _i, _x, _i, _r, _END},
		.itemId = ITEM_ELIXIR,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 10,
		.description = DESC_ELIXIR,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Ether,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Ether,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _x, _SPACE, _E, _l, _i, _x, _i, _r, _END},
		.itemId = ITEM_MAX_ELIXIR,
		.price = 4500,
		.holdEffect = 0,
        .holdEffectParam = 255,
		.description = DESC_MAX_ELIXIR,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Ether,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Ether,
        .secondaryId = 0
	},
	{
		.name = {_L, _a, _v, _a, _SPACE, _C, _o, _o, _k, _i, _e, _END},
		.itemId = ITEM_LAVA_COOKIE,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LAVA_COOKIE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_B, _l, _u, _e, _SPACE, _F, _l, _u, _t, _e, _END},
		.itemId = ITEM_BLUE_FLUTE,
		.price = 100,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BLUE_FLUTE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_Y, _e, _l, _l, _o, _w, _SPACE, _F, _l, _u, _t, _e, _END},
		.itemId = ITEM_YELLOW_FLUTE,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_YELLOW_FLUTE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _d, _SPACE, _F, _l, _u, _t, _e, _END},
		.itemId = ITEM_RED_FLUTE,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RED_FLUTE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_B, _l, _a, _c, _k, _SPACE, _F, _l, _u, _t, _e, _END},
		.itemId = ITEM_BLACK_FLUTE,
		.price = 400,
		.holdEffect = 0,
        .holdEffectParam = 50,
		.description = DESC_BLACK_FLUTE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_BlackWhiteFlute,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _h, _i, _t, _e, _SPACE, _F, _l, _u, _t, _e, _END},
		.itemId = ITEM_WHITE_FLUTE,
		.price = 500,
		.holdEffect = 0,
        .holdEffectParam = 150,
		.description = DESC_WHITE_FLUTE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_BlackWhiteFlute,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _e, _r, _r, _y, _SPACE, _J, _u, _i, _c, _e, _END},
		.itemId = ITEM_BERRY_JUICE,
		.price = 100,
		.holdEffect = 1,
        .holdEffectParam = 20,
		.description = DESC_BERRY_JUICE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_S, _a, _c, _r, _e, _d, _SPACE, _A, _s, _h, _END},
		.itemId = ITEM_SACRED_ASH,
		.price = 200,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SACRED_ASH,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_SacredAsh,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _h, _o, _a, _l, _SPACE, _S, _a, _l, _t, _END},
		.itemId = ITEM_SHOAL_SALT,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SHOAL_SALT,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _h, _o, _a, _l, _SPACE, _S, _h, _e, _l, _l, _END},
		.itemId = ITEM_SHOAL_SHELL,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SHOAL_SHELL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _d, _SPACE, _S, _h, _a, _r, _d, _END},
		.itemId = ITEM_RED_SHARD,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RED_SHARD,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _l, _u, _e, _SPACE, _S, _h, _a, _r, _d, _END},
		.itemId = ITEM_BLUE_SHARD,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BLUE_SHARD,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_Y, _e, _l, _l, _o, _w, _SPACE, _S, _h, _a, _r, _d, _END},
		.itemId = ITEM_YELLOW_SHARD,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_YELLOW_SHARD,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _r, _e, _e, _n, _SPACE, _S, _h, _a, _r, _d, _END},
		.itemId = ITEM_GREEN_SHARD,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GREEN_SHARD,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _a, _g, _e, _C, _a, _n, _d, _y, _B, _a, _r, _END},
		.itemId = ITEM_RAGE_CANDY_BAR,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RAGE_CANDY_BAR,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_O, _l, _d, _SPACE, _G, _a, _t, _e, _a, _u, _END},
		.itemId = ITEM_OLD_GATEAU,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_OLD_GATEAU,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_C, _a, _s, _t, _e, _l, _i, _a, _c, _o, _n, _e, _END},
		.itemId = ITEM_CASTELIACONE,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CASTELIACONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_L, _u, _m, _i, _o, _s, _e, _SPACE, _G, _a, _l, _PERIOD, _END},
		.itemId = ITEM_LUMIOSE_GALETTE,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LUMIOSE_GALETTE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_S, _h, _a, _l, _o, _u, _r, _S, _a, _b, _l, _e, _END},
		.itemId = ITEM_SHALOUR_SABLE,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SHALOUR_SABLE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_H, _e, _a, _l, _t, _h, _SPACE, _W, _i, _n, _g, _END},
		.itemId = ITEM_HEALTH_WING,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_HEALTH_WING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _u, _s, _c, _l, _e, _SPACE, _W, _i, _n, _g, _END},
		.itemId = ITEM_MUSCLE_WING,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_MUSCLE_WING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _s, _i, _s, _t, _SPACE, _W, _i, _n, _g, _END},
		.itemId = ITEM_RESIST_WING,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RESIST_WING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _e, _n, _i, _u, _s, _SPACE, _W, _i, _n, _g, _END},
		.itemId = ITEM_GENIUS_WING,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GENIUS_WING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _l, _e, _v, _e, _r, _SPACE, _W, _i, _n, _g, _SPACE, _END, 0xDC},
		.itemId = ITEM_CLEVER_WING,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CLEVER_WING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _w, _i, _f, _t, _SPACE, _W, _i, _n, _g, _SPACE, _SPACE, _END, 0xD9},
		.itemId = ITEM_SWIFT_WING,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SWIFT_WING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_H, _P, _SPACE, _U, _p, _END},
		.itemId = ITEM_HP_UP,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_HP_UP,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _r, _o, _t, _e, _i, _n, _END},
		.itemId = ITEM_PROTEIN,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PROTEIN,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_I, _r, _o, _n, _END},
		.itemId = ITEM_IRON,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_IRON,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _a, _r, _b, _o, _s, _END},
		.itemId = ITEM_CARBOS,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CARBOS,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _a, _l, _c, _i, _u, _m, _END},
		.itemId = ITEM_CALCIUM,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CALCIUM,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _a, _r, _e, _SPACE, _C, _a, _n, _d, _y, _END},
		.itemId = ITEM_RARE_CANDY,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RARE_CANDY,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_RareCandy,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _P, _SPACE, _U, _p, _END},
		.itemId = ITEM_PP_UP,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PP_UP,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_PpUp,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_Z, _i, _n, _c, _END},
		.itemId = ITEM_ZINC,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ZINC,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _P, _SPACE, _M, _a, _x, _END},
		.itemId = ITEM_PP_MAX,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PP_MAX,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_PpUp,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _x, _SPACE, _C, _a, _n, _d, _y, _END},
		.itemId = ITEM_DYNAMAX_CANDY,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DYNAMAX_CANDY,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_MaxPowder,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _u, _a, _r, _d, _SPACE, _S, _p, _e, _c, _END},
		.itemId = ITEM_GUARD_SPEC,
		.price = 700,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GUARD_SPEC,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_StatBooster,
        .secondaryId = 0
	},
	{
		.name = {_D, _i, _r, _e, _SPACE, _H, _i, _t, _END},
		.itemId = ITEM_DIRE_HIT,
		.price = 650,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DIRE_HIT,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_StatBooster,
        .secondaryId = 0
	},
	{
		.name = {_X, _SPACE, _A, _t, _t, _a, _c, _k, _END},
		.itemId = ITEM_X_ATTACK,
		.price = 800,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_X_ATTACK,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_StatBooster,
        .secondaryId = 0
	},
	{
		.name = {_X, _SPACE, _D, _e, _f, _e, _n, _d, _END},
		.itemId = ITEM_X_DEFEND,
		.price = 550,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_X_DEFEND,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_StatBooster,
        .secondaryId = 0
	},
	{
		.name = {_X, _SPACE, _S, _p, _e, _e, _d, _END},
		.itemId = ITEM_X_SPEED,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_X_SPEED,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_StatBooster,
        .secondaryId = 0
	},
	{
		.name = {_X, _SPACE, _A, _c, _c, _u, _r, _a, _c, _y, _END},
		.itemId = ITEM_X_ACCURACY,
		.price = 950,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_X_ACCURACY,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_StatBooster,
        .secondaryId = 0
	},
	{
		.name = {_X, _SPACE, _S, _p, _PERIOD, _SPACE, _A, _t, _k, _END},
		.itemId = ITEM_X_SPECIAL,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_X_SPECIAL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_StatBooster,
        .secondaryId = 0
	},
	{
		.name = {_P, _o, _k, _eACUTE, _SPACE, _D, _o, _l, _l, _END},
		.itemId = ITEM_POKE_DOLL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_POKE_DOLL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeDoll,
        .secondaryId = 0
	},
	{
		.name = {_F, _l, _u, _f, _f, _y, _SPACE, _T, _a, _i, _l, _END},
		.itemId = ITEM_FLUFFY_TAIL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_FLUFFY_TAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeDoll,
        .secondaryId = 0
	},
	{
		.name = {_B, _i, _g, _SPACE, _M, _a, _l, _a, _s, _a, _d, _a, _END},
		.itemId = ITEM_BIG_MALASADA,
		.price = 350,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BIG_MALASADA,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_S, _u, _p, _e, _r, _SPACE, _R, _e, _p, _e, _l, _END},
		.itemId = ITEM_SUPER_REPEL,
		.price = 700,
		.holdEffect = 0,
        .holdEffectParam = 200,
		.description = DESC_SUPER_REPEL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_Repel,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _x, _SPACE, _R, _e, _p, _e, _l, _END},
		.itemId = ITEM_MAX_REPEL,
		.price = 900,
		.holdEffect = 0,
        .holdEffectParam = 250,
		.description = DESC_MAX_REPEL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_Repel,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_E, _s, _c, _a, _p, _e, _SPACE, _R, _o, _p, _e, _END},
		.itemId = ITEM_ESCAPE_ROPE,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ESCAPE_ROPE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = ItemUseOutOfBattle_EscapeRope,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _p, _e, _l, _END},
		.itemId = ITEM_REPEL,
		.price = 400,
		.holdEffect = 0,
        .holdEffectParam = 100,
		.description = DESC_REPEL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_Repel,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _i, _n, _k, _SPACE, _C, _a, _b, _l, _e, _END},
		.itemId = ITEM_LINK_CABLE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LINK_CABLE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _r, _o, _t, _e, _c, _t, _o, _r, _END},
		.itemId = ITEM_PROTECTOR,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PROTECTOR,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_E, _l, _e, _c, _t, _i, _r, _i, _z, _e, _r, _END},
		.itemId = ITEM_ELECTIRIZER,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ELECTIRIZER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _g, _m, _a, _r, _i, _z, _e, _r, _END},
		.itemId = ITEM_MAGMARIZER,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_MAGMARIZER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _u, _b, _i, _o, _u, _s, _SPACE, _D, _i, _s, _c, _END},
		.itemId = ITEM_DUBIOUS_DISC,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DUBIOUS_DISC,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _a, _p, _e, _r, _SPACE, _C, _l, _o, _t, _h, _END},
		.itemId = ITEM_REAPER_CLOTH,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_REAPER_CLOTH,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _u, _n, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_SUN_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SUN_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _o, _o, _n, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_MOON_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_MOON_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_F, _i, _r, _e, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_FIRE_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_FIRE_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_T, _h, _u, _n, _d, _e, _r, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_THUNDER_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_THUNDER_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _a, _t, _e, _r, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_WATER_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_WATER_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _e, _a, _f, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_LEAF_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LEAF_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _h, _i, _n, _y, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_SHINY_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SHINY_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _u, _s, _k, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_DUSK_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DUSK_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _a, _w, _n, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_DAWN_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DAWN_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_I, _c, _e, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_ICE_STONE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ICE_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_T, _i, _n, _y, _M, _u, _s, _h, _r, _o, _o, _m, _END},
		.itemId = ITEM_TINY_MUSHROOM,
		.price = 500,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_TINY_MUSHROOM,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _i, _g, _SPACE, _M, _u, _s, _h, _r, _o, _o, _m, _END},
		.itemId = ITEM_BIG_MUSHROOM,
		.price = 5000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BIG_MUSHROOM,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _a, _l, _m, _M, _u, _s, _h, _r, _o, _o, _m, _END},
		.itemId = ITEM_BALM_MUSHROOM,
		.price = 15000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BALM_MUSHROOM,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _e, _a, _r, _l, _END},
		.itemId = ITEM_PEARL,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PEARL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _i, _g, _SPACE, _P, _e, _a, _r, _l, _END},
		.itemId = ITEM_BIG_PEARL,
		.price = 8000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BIG_PEARL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _t, _a, _r, _d, _u, _s, _t, _END},
		.itemId = ITEM_STARDUST,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_STARDUST,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _t, _a, _r, _SPACE, _P, _i, _e, _c, _e, _END},
		.itemId = ITEM_STAR_PIECE,
		.price = 12000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_STAR_PIECE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_N, _u, _g, _g, _e, _t, _END},
		.itemId = ITEM_NUGGET,
		.price = 10000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_NUGGET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_H, _e, _a, _r, _t, _SPACE, _S, _c, _a, _l, _e, _END},
		.itemId = ITEM_HEART_SCALE,
		.price = 100,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_HEART_SCALE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _a, _r, _e, _SPACE, _B, _o, _n, _e, _END},
		.itemId = ITEM_RARE_BONE,
		.price = 5000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RARE_BONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _e, _a, _r, _l, _SPACE, _S, _t, _r, _i, _n, _g, _END},
		.itemId = ITEM_PEARL_STRING,
		.price = 20000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PEARL_STRING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _o, _m, _e, _t, _SPACE, _S, _h, _a, _r, _d, _END},
		.itemId = ITEM_COMET_SHARD,
		.price = 25000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_COMET_SHARD,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _i, _g, _SPACE, _N, _u, _g, _g, _e, _t, _END},
		.itemId = ITEM_BIG_NUGGET,
		.price = 25000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BIG_NUGGET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_H, _o, _n, _e, _y, _END},
		.itemId = ITEM_HONEY,
		.price = 900,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_HONEY,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_Honey,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _r, _e, _t, _t, _y, _SPACE, _W, _i, _n, _g, _END},
		.itemId = ITEM_PRETTY_WING,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PRETTY_WING,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_O, _v, _a, _l, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_OVAL_STONE,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_OVAL_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _a, _z, _o, _r, _SPACE, _C, _l, _a, _w, _END},
		.itemId = ITEM_RAZOR_CLAW,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_SCOPE_LENS,
        .holdEffectParam = 0,
		.description = DESC_RAZOR_CLAW,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _a, _z, _o, _r, _SPACE, _F, _a, _n, _g, _END},
		.itemId = ITEM_RAZOR_FANG,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_FLINCH,
        .holdEffectParam = 10,
		.description = DESC_RAZOR_FANG,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_O, _r, _a, _n, _g, _e, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_ORANGE_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ORANGE_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_H, _a, _r, _b, _o, _r, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_HARBOR_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_HARBOR_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _l, _i, _t, _t, _e, _r, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_GLITTER_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GLITTER_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _e, _c, _h, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_MECH_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_MECH_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _o, _o, _d, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_WOOD_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_WOOD_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _a, _v, _e, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_WAVE_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_WAVE_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _e, _a, _d, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_BEAD_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BEAD_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _h, _a, _d, _o, _w, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_SHADOW_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SHADOW_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_T, _r, _o, _p, _i, _c, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_TROPIC_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_TROPIC_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _r, _e, _a, _m, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_DREAM_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DREAM_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_F, _a, _b, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_FAB_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_FAB_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _t, _r, _o, _SPACE, _M, _a, _i, _l, _END},
		.itemId = ITEM_RETRO_MAIL,
		.price = 50,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RETRO_MAIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_MAIL,
		.fieldUseFunc = FieldUseFunc_Mail,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _h, _e, _r, _i, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_CHERI_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CURE_PAR,
        .holdEffectParam = 0,
		.description = DESC_CHERI_BERRY,
		.importance = 0,
        .unk19 = 1,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_C, _h, _e, _s, _t, _o, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_CHESTO_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CURE_SLP,
        .holdEffectParam = 0,
		.description = DESC_CHESTO_BERRY,
		.importance = 0,
        .unk19 = 2,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_P, _e, _c, _h, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_PECHA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CURE_PSN,
        .holdEffectParam = 0,
		.description = DESC_PECHA_BERRY,
		.importance = 0,
        .unk19 = 3,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_R, _a, _w, _s, _t, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_RAWST_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CURE_BRN,
        .holdEffectParam = 0,
		.description = DESC_RAWST_BERRY,
		.importance = 0,
        .unk19 = 4,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_A, _s, _p, _e, _a, _r, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_ASPEAR_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CURE_FRZ,
        .holdEffectParam = 0,
		.description = DESC_ASPEAR_BERRY,
		.importance = 0,
        .unk19 = 5,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_L, _e, _p, _p, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_LEPPA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_RESTORE_PP,
        .holdEffectParam = 10,
		.description = DESC_LEPPA_BERRY,
		.importance = 0,
        .unk19 = 6,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Ether,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Ether,
        .secondaryId = 0
	},
	{
		.name = {_O, _r, _a, _n, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_ORAN_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_RESTORE_HP,
        .holdEffectParam = 10,
		.description = DESC_ORAN_BERRY,
		.importance = 0,
        .unk19 = 7,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_P, _e, _r, _s, _i, _m, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_PERSIM_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CURE_CONFUSION,
        .holdEffectParam = 0,
		.description = DESC_PERSIM_BERRY,
		.importance = 0,
        .unk19 = 8,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_L, _u, _m, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_LUM_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CURE_STATUS,
        .holdEffectParam = 0,
		.description = DESC_LUM_BERRY,
		.importance = 0,
        .unk19 = 9,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_S, _i, _t, _r, _u, _s, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_SITRUS_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_RESTORE_HP,
        .holdEffectParam = 30,
		.description = DESC_SITRUS_BERRY,
		.importance = 0,
        .unk19 = 10,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_Medicine,
		.battleUsage = 1,
        .battleUseFunc = BattleUseFunc_Medicine,
        .secondaryId = 0
	},
	{
		.name = {_F, _i, _g, _y, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_FIGY_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CONFUSE_SPICY,
        .holdEffectParam = 8,
		.description = DESC_FIGY_BERRY,
		.importance = 0,
        .unk19 = 11,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _i, _k, _i, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_WIKI_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CONFUSE_DRY,
        .holdEffectParam = 8,
		.description = DESC_WIKI_BERRY,
		.importance = 0,
        .unk19 = 12,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _g, _o, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_MAGO_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CONFUSE_SWEET,
        .holdEffectParam = 8,
		.description = DESC_MAGO_BERRY,
		.importance = 0,
        .unk19 = 13,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_A, _g, _u, _a, _v, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_AGUAV_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CONFUSE_BITTER,
        .holdEffectParam = 8,
		.description = DESC_AGUAV_BERRY,
		.importance = 0,
        .unk19 = 14,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_I, _a, _p, _a, _p, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_IAPAPA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CONFUSE_SOUR,
        .holdEffectParam = 8,
		.description = DESC_IAPAPA_BERRY,
		.importance = 0,
        .unk19 = 15,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _a, _z, _z, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_RAZZ_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 16,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _l, _u, _k, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_BLUK_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 17,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_N, _a, _n, _a, _b, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_NANAB_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 18,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _e, _p, _e, _a, _r, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_WEPEAR_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 19,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _i, _n, _a, _p, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_PINAP_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 20,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _o, _m, _e, _g, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_POMEG_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_POMEG_BERRY,
		.importance = 0,
        .unk19 = 21,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EVReducingBerry,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_K, _e, _l, _p, _s, _y, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_KELPSY_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 1,
		.description = DESC_KELPSY_BERRY,
		.importance = 0,
        .unk19 = 22,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EVReducingBerry,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_Q, _u, _a, _l, _o, _t, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_QUALOT_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 2,
		.description = DESC_QUALOT_BERRY,
		.importance = 0,
        .unk19 = 23,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EVReducingBerry,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_H, _o, _n, _d, _e, _w, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_HONDEW_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 4,
		.description = DESC_HONDEW_BERRY,
		.importance = 0,
        .unk19 = 24,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EVReducingBerry,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _r, _e, _p, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_GREPA_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 5,
		.description = DESC_GREPA_BERRY,
		.importance = 0,
        .unk19 = 25,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EVReducingBerry,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_T, _a, _m, _a, _t, _o, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_TAMATO_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 3,
		.description = DESC_TAMATO_BERRY,
		.importance = 0,
        .unk19 = 26,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EVReducingBerry,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _o, _r, _n, _n, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_CORNN_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 27,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _g, _o, _s, _t, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_MAGOST_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 28,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _a, _b, _u, _t, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_RABUTA_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 29,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_N, _o, _m, _e, _l, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_NOMEL_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 30,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _p, _e, _l, _o, _n, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_SPELON_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 31,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _a, _m, _t, _r, _e, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_PAMTRE_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 32,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _a, _t, _m, _e, _l, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_WATMEL_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 33,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _u, _r, _i, _n, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_DURIN_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 34,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _e, _l, _u, _e, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_BELUE_BERRY,
		.price = 20,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 35,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _i, _e, _c, _h, _i, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_LIECHI_BERRY,
		.price = 500,
		.holdEffect = ITEM_EFFECT_ATTACK_UP,
        .holdEffectParam = 4,
		.description = DESC_LIECHI_BERRY,
		.importance = 0,
        .unk19 = 53,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _a, _n, _l, _o, _n, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_GANLON_BERRY,
		.price = 500,
		.holdEffect = ITEM_EFFECT_DEFENSE_UP,
        .holdEffectParam = 4,
		.description = DESC_GANLON_BERRY,
		.importance = 0,
        .unk19 = 54,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _a, _l, _a, _c, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_SALAC_BERRY,
		.price = 500,
		.holdEffect = ITEM_EFFECT_SPEED_UP,
        .holdEffectParam = 4,
		.description = DESC_SALAC_BERRY,
		.importance = 0,
        .unk19 = 55,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _e, _t, _a, _y, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_PETAYA_BERRY,
		.price = 500,
		.holdEffect = ITEM_EFFECT_SP_ATTACK_UP,
        .holdEffectParam = 4,
		.description = DESC_PETAYA_BERRY,
		.importance = 0,
        .unk19 = 56,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_A, _p, _i, _c, _o, _t, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_APICOT_BERRY,
		.price = 500,
		.holdEffect = ITEM_EFFECT_SP_DEFENSE_UP,
        .holdEffectParam = 4,
		.description = DESC_APICOT_BERRY,
		.importance = 0,
        .unk19 = 57,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _a, _n, _s, _a, _t, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_LANSAT_BERRY,
		.price = 500,
		.holdEffect = ITEM_EFFECT_CRITICAL_UP,
        .holdEffectParam = 4,
		.description = DESC_LANSAT_BERRY,
		.importance = 0,
        .unk19 = 58,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _t, _a, _r, _f, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_STARF_BERRY,
		.price = 500,
		.holdEffect = ITEM_EFFECT_RANDOM_STAT_UP,
        .holdEffectParam = 4,
		.description = DESC_STARF_BERRY,
		.importance = 0,
        .unk19 = 59,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_E, _n, _i, _g, _m, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_ENIGMA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_ENIGMA_BERRY,
        .holdEffectParam = 0,
		.description = DESC_INGREDIENT_BERRY,
		.importance = 0,
        .unk19 = 60,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = ItemUseOutOfBattle_EnigmaBerry,
		.battleUsage = 1,
        .battleUseFunc = ItemUseInBattle_EnigmaBerry,
        .secondaryId = 0
	},
	{
		.name = {_P, _r, _i, _s, _m, _SPACE, _S, _c, _a, _l, _e, _END},
		.itemId = ITEM_PRISM_SCALE,
		.price = 500,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PRISM_SCALE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _a, _c, _h, _e, _t, _END},
		.itemId = ITEM_SACHET,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SACHET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _h, _i, _p, _SPACE, _D, _r, _e, _a, _m, _END},
		.itemId = ITEM_WHIPPED_DREAM,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_WHIPPED_DREAM,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _r, _i, _g, _h, _t, _P, _o, _w, _d, _e, _r, _END},
		.itemId = ITEM_BRIGHT_POWDER,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_EVASION_UP,
        .holdEffectParam = 10,
		.description = DESC_BRIGHT_POWDER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _h, _i, _t, _e, _SPACE, _H, _e, _r, _b, _END},
		.itemId = ITEM_WHITE_HERB,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_RESTORE_STATS,
        .holdEffectParam = 0,
		.description = DESC_WHITE_HERB,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _c, _h, _o, _SPACE, _B, _r, _a, _c, _e, _END},
		.itemId = ITEM_MACHO_BRACE,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_MACHO_BRACE,
        .holdEffectParam = 0,
		.description = DESC_MACHO_BRACE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_E, _x, _p, _PERIOD, _SPACE, _S, _h, _a, _r, _e, _END},
		.itemId = ITEM_EXP_SHARE,
		.price = 5000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_EXP_SHARE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_ExpShare,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_Q, _u, _i, _c, _k, _SPACE, _C, _l, _a, _w, _END},
		.itemId = ITEM_QUICK_CLAW,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_QUICK_CLAW,
        .holdEffectParam = 20,
		.description = DESC_QUICK_CLAW,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _o, _o, _t, _h, _e, _SPACE, _B, _e, _l, _l, _END},
		.itemId = ITEM_SOOTHE_BELL,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_HAPPINESS_UP,
        .holdEffectParam = 0,
		.description = DESC_SOOTHE_BELL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _e, _n, _t, _a, _l, _SPACE, _H, _e, _r, _b, _END},
		.itemId = ITEM_MENTAL_HERB,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_CURE_ATTRACT,
        .holdEffectParam = 0,
		.description = DESC_MENTAL_HERB,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _h, _o, _i, _c, _e, _SPACE, _B, _a, _n, _d, _END},
		.itemId = ITEM_CHOICE_BAND,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_CHOICE_BAND,
        .holdEffectParam = 0,
		.description = DESC_CHOICE_BAND,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_K, _i, _n, _g, _APOSTROPHE, _s, _SPACE, _R, _o, _c, _k, _END},
		.itemId = ITEM_KINGS_ROCK,
		.price = 5000,
		.holdEffect = ITEM_EFFECT_FLINCH,
        .holdEffectParam = 10,
		.description = DESC_KINGS_ROCK,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _i, _l, _v, _e, _r, _P, _o, _w, _d, _e, _r, _END},
		.itemId = ITEM_SILVER_POWDER,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_BUG_POWER,
        .holdEffectParam = 10,
		.description = DESC_SILVER_POWDER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_A, _m, _u, _l, _e, _t, _SPACE, _C, _o, _i, _n, _END},
		.itemId = ITEM_AMULET_COIN,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_DOUBLE_PRIZE,
        .holdEffectParam = 10,
		.description = DESC_AMULET_COIN,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _l, _e, _a, _n, _s, _e, _SPACE, _T, _a, _g, _END},
		.itemId = ITEM_CLEANSE_TAG,
		.price = 5000,
		.holdEffect = ITEM_EFFECT_REPEL,
        .holdEffectParam = 0,
		.description = DESC_CLEANSE_TAG,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _o, _u, _l, _SPACE, _D, _e, _w, _END},
		.itemId = ITEM_SOUL_DEW,
		.price = 20000,
		.holdEffect = ITEM_EFFECT_SOUL_DEW,
        .holdEffectParam = 0,
		.description = DESC_SOUL_DEW,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _e, _e, _p, _S, _e, _a, _T, _o, _o, _t, _h, _END},
		.itemId = ITEM_DEEP_SEA_TOOTH,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_DEEP_SEA_TOOTH,
        .holdEffectParam = 0,
		.description = DESC_DEEP_SEA_TOOTH,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _e, _e, _p, _S, _e, _a, _S, _c, _a, _l, _e, _END},
		.itemId = ITEM_DEEP_SEA_SCALE,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_DEEP_SEA_SCALE,
        .holdEffectParam = 0,
		.description = DESC_DEEP_SEA_SCALE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _m, _o, _k, _e, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_SMOKE_BALL,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_CAN_ALWAYS_RUN,
        .holdEffectParam = 0,
		.description = DESC_SMOKE_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_E, _v, _e, _r, _s, _t, _o, _n, _e, _END},
		.itemId = ITEM_EVERSTONE,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_PREVENT_EVOLVE,
        .holdEffectParam = 0,
		.description = DESC_EVERSTONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_F, _o, _c, _u, _s, _SPACE, _B, _a, _n, _d, _END},
		.itemId = ITEM_FOCUS_BAND,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_FOCUS_BAND,
        .holdEffectParam = 10,
		.description = DESC_FOCUS_BAND,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _u, _c, _k, _y, _SPACE, _E, _g, _g, _END},
		.itemId = ITEM_LUCKY_EGG,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_LUCKY_EGG,
        .holdEffectParam = 0,
		.description = DESC_LUCKY_EGG,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _c, _o, _p, _e, _SPACE, _L, _e, _n, _s, _END},
		.itemId = ITEM_SCOPE_LENS,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_SCOPE_LENS,
        .holdEffectParam = 0,
		.description = DESC_SCOPE_LENS,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _e, _t, _a, _l, _SPACE, _C, _o, _a, _t, _END},
		.itemId = ITEM_METAL_COAT,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_STEEL_POWER,
        .holdEffectParam = 20,
		.description = DESC_METAL_COAT,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _e, _f, _t, _o, _v, _e, _r, _s, _END},
		.itemId = ITEM_LEFTOVERS,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_LEFTOVERS,
        .holdEffectParam = 10,
		.description = DESC_LEFTOVERS,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _r, _a, _g, _o, _n, _SPACE, _S, _c, _a, _l, _e, _END},
		.itemId = ITEM_DRAGON_SCALE,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_DRAGON_SCALE,
        .holdEffectParam = 0,
		.description = DESC_DRAGON_SCALE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _i, _g, _h, _t, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_LIGHT_BALL,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_LIGHT_BALL,
        .holdEffectParam = 0,
		.description = DESC_LIGHT_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _o, _f, _t, _SPACE, _S, _a, _n, _d, _END},
		.itemId = ITEM_SOFT_SAND,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GROUND_POWER,
        .holdEffectParam = 20,
		.description = DESC_SOFT_SAND,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_H, _a, _r, _d, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_HARD_STONE,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_ROCK_POWER,
        .holdEffectParam = 20,
		.description = DESC_HARD_STONE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _i, _r, _a, _c, _l, _e, _SPACE, _S, _e, _e, _d, _END},
		.itemId = ITEM_MIRACLE_SEED,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GRASS_POWER,
        .holdEffectParam = 20,
		.description = DESC_MIRACLE_SEED,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _l, _a, _c, _k, _G, _l, _a, _s, _s, _e, _s, _END},
		.itemId = ITEM_BLACK_GLASSES,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_DARK_POWER,
        .holdEffectParam = 20,
		.description = DESC_BLACK_GLASSES,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _l, _a, _c, _k, _SPACE, _B, _e, _l, _t, _END},
		.itemId = ITEM_BLACK_BELT,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_FIGHTING_POWER,
        .holdEffectParam = 20,
		.description = DESC_BLACK_BELT,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _g, _n, _e, _t, _END},
		.itemId = ITEM_MAGNET,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_ELECTRIC_POWER,
        .holdEffectParam = 20,
		.description = DESC_MAGNET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _y, _s, _t, _i, _c, _SPACE, _W, _a, _t, _e, _r, _END},
		.itemId = ITEM_MYSTIC_WATER,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_WATER_POWER,
        .holdEffectParam = 20,
		.description = DESC_MYSTIC_WATER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _h, _a, _r, _p, _SPACE, _B, _e, _a, _k, _END},
		.itemId = ITEM_SHARP_BEAK,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_FLYING_POWER,
        .holdEffectParam = 20,
		.description = DESC_SHARP_BEAK,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _o, _i, _s, _o, _n, _SPACE, _B, _a, _r, _b, _END},
		.itemId = ITEM_POISON_BARB,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_POISON_POWER,
        .holdEffectParam = 20,
		.description = DESC_POISON_BARB,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_N, _e, _v, _e, _r, _m, _e, _l, _t, _i, _c, _e, _END},
		.itemId = ITEM_NEVER_MELT_ICE,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_ICE_POWER,
        .holdEffectParam = 20,
		.description = DESC_NEVER_MELT_ICE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _p, _e, _l, _l, _SPACE, _T, _a, _g, _END},
		.itemId = ITEM_SPELL_TAG,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GHOST_POWER,
        .holdEffectParam = 20,
		.description = DESC_SPELL_TAG,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_T, _w, _i, _s, _t, _e, _d, _S, _p, _o, _o, _n, _END},
		.itemId = ITEM_TWISTED_SPOON,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_PSYCHIC_POWER,
        .holdEffectParam = 20,
		.description = DESC_TWISTED_SPOON,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _h, _a, _r, _c, _o, _a, _l, _END},
		.itemId = ITEM_CHARCOAL,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_FIRE_POWER,
        .holdEffectParam = 20,
		.description = DESC_CHARCOAL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _r, _a, _g, _o, _n, _SPACE, _F, _a, _n, _g, _END},
		.itemId = ITEM_DRAGON_FANG,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_DRAGON_POWER,
        .holdEffectParam = 20,
		.description = DESC_DRAGON_FANG,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _i, _l, _k, _SPACE, _S, _c, _a, _r, _f, _END},
		.itemId = ITEM_SILK_SCARF,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_NORMAL_POWER,
        .holdEffectParam = 20,
		.description = DESC_SILK_SCARF,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_U, _p, _g, _r, _a, _d, _e, _END},
		.itemId = ITEM_UP_GRADE,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_UP_GRADE,
        .holdEffectParam = 0,
		.description = DESC_UP_GRADE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _h, _e, _l, _l, _SPACE, _B, _e, _l, _l, _END},
		.itemId = ITEM_SHELL_BELL,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_SHELL_BELL,
        .holdEffectParam = 8,
		.description = DESC_SHELL_BELL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _e, _a, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_SEA_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_WATER_POWER,
        .holdEffectParam = 10,
		.description = DESC_SEA_INCENSE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _a, _x, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_LAX_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_EVASION_UP,
        .holdEffectParam = 10,
		.description = DESC_LAX_INCENSE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _u, _c, _k, _y, _SPACE, _P, _u, _n, _c, _h, _END},
		.itemId = ITEM_LUCKY_PUNCH,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_LUCKY_PUNCH,
        .holdEffectParam = 0,
		.description = DESC_LUCKY_PUNCH,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _e, _t, _a, _l, _SPACE, _P, _o, _w, _d, _e, _r, _END},
		.itemId = ITEM_METAL_POWDER,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_METAL_POWDER,
        .holdEffectParam = 0,
		.description = DESC_METAL_POWDER,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_T, _h, _i, _c, _k, _SPACE, _C, _l, _u, _b, _END},
		.itemId = ITEM_THICK_CLUB,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_THICK_CLUB,
        .holdEffectParam = 0,
		.description = DESC_THICK_CLUB,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _e, _e, _k, _END},
		.itemId = ITEM_LEEK,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_STICK,
        .holdEffectParam = 0,
		.description = DESC_LEEK,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _t, _r, _a, _w, _PERIOD, _SPACE, _S, _w, _e, _e, _t, _END},
		.itemId = ITEM_STRAWBERRY_SWEET,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_STRAWBERRY_SWEET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _e, _r, _r, _y, _SPACE, _S, _w, _e, _e, _t, _END},
		.itemId = ITEM_BERRY_SWEET,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BERRY_SWEET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _o, _v, _e, _SPACE, _S, _w, _e, _e, _t, _END},
		.itemId = ITEM_LOVE_SWEET,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LOVE_SWEET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _l, _o, _v, _e, _r, _SPACE, _S, _w, _e, _e, _t, _END},
		.itemId = ITEM_CLOVER_SWEET,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CLOVER_SWEET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_F, _l, _o, _w, _e, _r, _SPACE, _S, _w, _e, _e, _t, _END},
		.itemId = ITEM_FLOWER_SWEET,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_FLOWER_SWEET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _i, _b, _b, _o, _n, _SPACE, _S, _w, _e, _e, _t, _END},
		.itemId = ITEM_RIBBON_SWEET,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RIBBON_SWEET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _t, _a, _r, _SPACE, _S, _w, _e, _e, _t, _END},
		.itemId = ITEM_STAR_SWEET,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_STAR_SWEET,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _w, _e, _e, _t, _SPACE, _A, _p, _p, _l, _e, _END},
		.itemId = ITEM_SWEET_APPLE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SWEET_APPLE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_T, _a, _r, _t, _SPACE, _A, _p, _p, _l, _e, _END},
		.itemId = ITEM_TART_APPLE,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_TART_APPLE,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _r, _a, _c, _k, _e, _d, _SPACE, _P, _o, _t, _END},
		.itemId = ITEM_CRACKED_POT,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CRACKED_POT,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _h, _i, _p, _p, _e, _d, _SPACE, _P, _o, _t, _END},
		.itemId = ITEM_CHIPPED_POT,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CHIPPED_POT,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _a, _l, _a, _r, _SPACE, _C, _u, _f, _f, _END},
		.itemId = ITEM_GALARICA_CUFF,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GALARICA_CUFF,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _a, _l, _a, _r, _SPACE, _W, _r, _e, _a, _t, _h, _END},
		.itemId = ITEM_GALARICA_WREATH,
		.price = 3000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GALARICA_WREATH,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_EvoItem,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _h, _e, _r, _i, _s, _h, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_CHERISH_BALL,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CHERISH_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 15,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_D, _u, _s, _k, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_DUSK_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DUSK_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 12,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_H, _e, _a, _l, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_HEAL_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_HEAL_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 13,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_Q, _u, _i, _c, _k, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_QUICK_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_QUICK_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 14,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_F, _a, _s, _t, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_FAST_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_FAST_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 17,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_L, _e, _v, _e, _l, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_LEVEL_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LEVEL_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 18,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_L, _u, _r, _e, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_LURE_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LURE_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 19,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_H, _e, _a, _v, _y, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_HEAVY_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_HEAVY_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 20,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_L, _o, _v, _e, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_LOVE_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LOVE_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 21,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_F, _r, _i, _e, _n, _d, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_FRIEND_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_FRIEND_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 22,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_M, _o, _o, _n, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_MOON_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_MOON_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 23,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_S, _p, _o, _r, _t, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_SPORT_BALL,
		.price = 300,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SPORT_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 24,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_B, _e, _a, _s, _t, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_BEAST_BALL,
		.price = 2000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BEAST_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 25,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_D, _r, _e, _a, _m, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_DREAM_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DREAM_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 26,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_P, _a, _r, _k, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_PARK_BALL,
		.price = 1000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PARK_BALL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_POKE_BALLS,
		.type = 16,
		.fieldUseFunc = NULL,
		.battleUsage = 2,
        .battleUseFunc = BattleUseFunc_PokeBallEtc,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _d, _SPACE, _S, _c, _a, _r, _f, _END},
		.itemId = ITEM_RED_SCARF,
		.price = 100,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_RED_SCARF,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _l, _u, _e, _SPACE, _S, _c, _a, _r, _f, _END},
		.itemId = ITEM_BLUE_SCARF,
		.price = 100,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BLUE_SCARF,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_P, _i, _n, _k, _SPACE, _S, _c, _a, _r, _f, _END},
		.itemId = ITEM_PINK_SCARF,
		.price = 100,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_PINK_SCARF,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _r, _e, _e, _n, _SPACE, _S, _c, _a, _r, _f, _END},
		.itemId = ITEM_GREEN_SCARF,
		.price = 100,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GREEN_SCARF,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_Y, _e, _l, _l, _o, _w, _SPACE, _S, _c, _a, _r, _f, _END},
		.itemId = ITEM_YELLOW_SCARF,
		.price = 100,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_YELLOW_SCARF,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _a, _c, _h, _SPACE, _B, _i, _k, _e, _END},
		.itemId = ITEM_MACH_BIKE,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_MACH_BIKE,
		.importance = 1,
        .unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_Bike,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _o, _i, _n, _SPACE, _C, _a, _s, _e, _END},
		.itemId = ITEM_COIN_CASE,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_COIN_CASE,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_CoinCase,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_I, _t, _e, _m, _f, _i, _n, _d, _e, _r, _END},
		.itemId = ITEM_ITEMFINDER,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ITEMFINDER,
		.importance = 1,
        .unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = ItemUseOutOfBattle_Itemfinder,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_O, _l, _d, _SPACE, _R, _o, _d, _END},
		.itemId = ITEM_OLD_ROD,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_OLD_ROD,
		.importance = 1,
        .unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_Rod,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _o, _o, _d, _SPACE, _R, _o, _d, _END},
		.itemId = ITEM_GOOD_ROD,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GOOD_ROD,
		.importance = 1,
        .unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_Rod,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 1
	},
	{
		.name = {_S, _u, _p, _e, _r, _SPACE, _R, _o, _d, _END},
		.itemId = ITEM_SUPER_ROD,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SUPER_ROD,
		.importance = 1,
        .unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_Rod,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 2
	},
	{
		.name = {_S, _PERIOD, _S, _PERIOD, _SPACE, _T, _i, _c, _k, _e, _t, _END},
		.itemId = ITEM_SS_TICKET,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SS_TICKET,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _o, _n, _t, _e, _s, _t, _SPACE, _P, _a, _s, _s, _END},
		.itemId = ITEM_CONTEST_PASS,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CONTEST_PASS,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_Z, _HYPHEN, _P, _o, _w, _e, _r, _SPACE, _R, _i, _n, _g, _END},
		.itemId = ITEM_Z_POWER_RING,
		.price = 25000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_Z_POWER_RING,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_W, _a, _i, _l, _m, _e, _r, _SPACE, _P, _a, _i, _l, _END},
		.itemId = ITEM_WAILMER_PAIL,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_WAILMER_PAIL,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _e, _v, _o, _n, _SPACE, _G, _o, _o, _d, _s, _END},
		.itemId = ITEM_DEVON_GOODS,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DEVON_GOODS,
		.importance = 2,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _o, _o, _t, _SPACE, _S, _a, _c, _k, _END},
		.itemId = ITEM_SOOT_SACK,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SOOT_SACK,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _a, _s, _e, _m, _e, _n, _t, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_BASEMENT_KEY,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BASEMENT_KEY,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_A, _c, _r, _o, _SPACE, _B, _i, _k, _e, _END},
		.itemId = ITEM_ACRO_BIKE,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ACRO_BIKE,
		.importance = 1,
        .unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_Bike,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 1
	},
	{
		.name = {_PO, _KE, _BL, _OC, _OK, _SPACE, _C, _A, _S, _E, _END},
		.itemId = ITEM_POKEBLOCK_CASE,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_POKEBLOCK_CASE,
		.importance = 1,
        .unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_L, _e, _t, _t, _e, _r, _END},
		.itemId = ITEM_LETTER,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_LETTER,
		.importance = 2,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_E, _o, _n, _SPACE, _T, _i, _c, _k, _e, _t, _END},
		.itemId = ITEM_EON_TICKET,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_EON_TICKET,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _e, _d, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_RED_ORB,
		.price = 20000,
		.holdEffect = ITEM_EFFECT_PRIMAL_ORB,
        .holdEffectParam = 0,
		.description = DESC_RED_ORB,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_B, _l, _u, _e, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_BLUE_ORB,
		.price = 20000,
		.holdEffect = ITEM_EFFECT_PRIMAL_ORB,
        .holdEffectParam = 1,
		.description = DESC_BLUE_ORB,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _c, _a, _n, _n, _e, _r, _END},
		.itemId = ITEM_SCANNER,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_SCANNER,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_G, _o, _HYPHEN, _G, _o, _g, _g, _l, _e, _s, _END},
		.itemId = ITEM_GO_GOGGLES,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_GO_GOGGLES,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_M, _e, _t, _e, _o, _r, _i, _t, _e, _END},
		.itemId = ITEM_METEORITE,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_METEORITE,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _M, _PERIOD, _SPACE, _1, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_ROOM_1_KEY,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ROOM_1_KEY,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _M, _PERIOD, _SPACE, _2, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_ROOM_2_KEY,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ROOM_2_KEY,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _M, _PERIOD, _SPACE, _4, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_ROOM_4_KEY,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ROOM_4_KEY,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _M, _PERIOD, _SPACE, _6, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_ROOM_6_KEY,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ROOM_6_KEY,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_S, _t, _o, _r, _a, _g, _e, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_STORAGE_KEY,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_STORAGE_KEY,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_R, _o, _o, _t, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_ROOT_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_ROOT_FOSSIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_C, _l, _a, _w, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_CLAW_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_CLAW_FOSSIL,
		.importance = 0,
        .unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_D, _e, _v, _o, _n, _SPACE, _S, _c, _o, _p, _e, _END},
		.itemId = ITEM_DEVON_SCOPE,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_DEVON_SCOPE,
		.importance = 1,
        .unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
		{
		.name = {_T, _M, _0, _1, _END},
		.itemId = ITEM_TM01,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM01,
		.importance = 0,
		.unk19 = 1,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _0, _2, _END},
		.itemId = ITEM_TM02,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM02,
		.importance = 0,
		.unk19 = 2,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _0, _3, _END},
		.itemId = ITEM_TM03,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM03,
		.importance = 0,
		.unk19 = 3,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _0, _4, _END},
		.itemId = ITEM_TM04,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM04,
		.importance = 0,
		.unk19 = 4,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _0, _5, _END},
		.itemId = ITEM_TM05,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM05,
		.importance = 0,
		.unk19 = 5,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _0, _6, _END},
		.itemId = ITEM_TM06,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM06,
		.importance = 0,
		.unk19 = 6,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _0, _7, _END},
		.itemId = ITEM_TM07,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM07,
		.importance = 0,
		.unk19 = 7,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _0, _8, _END},
		.itemId = ITEM_TM08,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM08,
		.importance = 0,
		.unk19 = 8,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _0, _9, _END},
		.itemId = ITEM_TM09,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM09,
		.importance = 0,
		.unk19 = 9,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _END},
		.itemId = ITEM_TM10,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM10,
		.importance = 0,
		.unk19 = 10,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _END},
		.itemId = ITEM_TM11,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM11,
		.importance = 0,
		.unk19 = 11,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _2, _END},
		.itemId = ITEM_TM12,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM12,
		.importance = 0,
		.unk19 = 12,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _3, _END},
		.itemId = ITEM_TM13,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM13,
		.importance = 0,
		.unk19 = 13,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _4, _END},
		.itemId = ITEM_TM14,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM14,
		.importance = 0,
		.unk19 = 14,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _5, _END},
		.itemId = ITEM_TM15,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM15,
		.importance = 0,
		.unk19 = 15,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _6, _END},
		.itemId = ITEM_TM16,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM16,
		.importance = 0,
		.unk19 = 16,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _7, _END},
		.itemId = ITEM_TM17,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM17,
		.importance = 0,
		.unk19 = 17,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _8, _END},
		.itemId = ITEM_TM18,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM18,
		.importance = 0,
		.unk19 = 18,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _9, _END},
		.itemId = ITEM_TM19,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM19,
		.importance = 0,
		.unk19 = 19,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _0, _END},
		.itemId = ITEM_TM20,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM20,
		.importance = 0,
		.unk19 = 20,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _1, _END},
		.itemId = ITEM_TM21,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM21,
		.importance = 0,
		.unk19 = 21,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _2, _END},
		.itemId = ITEM_TM22,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM22,
		.importance = 0,
		.unk19 = 22,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _3, _END},
		.itemId = ITEM_TM23,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM23,
		.importance = 0,
		.unk19 = 23,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _4, _END},
		.itemId = ITEM_TM24,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM24,
		.importance = 0,
		.unk19 = 24,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _5, _END},
		.itemId = ITEM_TM25,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM25,
		.importance = 0,
		.unk19 = 25,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _6, _END},
		.itemId = ITEM_TM26,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM26,
		.importance = 0,
		.unk19 = 26,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _7, _END},
		.itemId = ITEM_TM27,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM27,
		.importance = 0,
		.unk19 = 27,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _8, _END},
		.itemId = ITEM_TM28,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM28,
		.importance = 0,
		.unk19 = 28,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _2, _9, _END},
		.itemId = ITEM_TM29,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM29,
		.importance = 0,
		.unk19 = 29,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _0, _END},
		.itemId = ITEM_TM30,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM30,
		.importance = 0,
		.unk19 = 30,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _1, _END},
		.itemId = ITEM_TM31,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM31,
		.importance = 0,
		.unk19 = 31,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _2, _END},
		.itemId = ITEM_TM32,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM32,
		.importance = 0,
		.unk19 = 32,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _3, _END},
		.itemId = ITEM_TM33,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM33,
		.importance = 0,
		.unk19 = 33,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _4, _END},
		.itemId = ITEM_TM34,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM34,
		.importance = 0,
		.unk19 = 34,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _5, _END},
		.itemId = ITEM_TM35,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM35,
		.importance = 0,
		.unk19 = 35,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _6, _END},
		.itemId = ITEM_TM36,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM36,
		.importance = 0,
		.unk19 = 36,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _7, _END},
		.itemId = ITEM_TM37,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM37,
		.importance = 0,
		.unk19 = 37,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _8, _END},
		.itemId = ITEM_TM38,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM38,
		.importance = 0,
		.unk19 = 38,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _3, _9, _END},
		.itemId = ITEM_TM39,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM39,
		.importance = 0,
		.unk19 = 39,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _0, _END},
		.itemId = ITEM_TM40,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM40,
		.importance = 0,
		.unk19 = 40,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _1, _END},
		.itemId = ITEM_TM41,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM41,
		.importance = 0,
		.unk19 = 41,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _2, _END},
		.itemId = ITEM_TM42,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM42,
		.importance = 0,
		.unk19 = 42,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _3, _END},
		.itemId = ITEM_TM43,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM43,
		.importance = 0,
		.unk19 = 43,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _4, _END},
		.itemId = ITEM_TM44,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM44,
		.importance = 0,
		.unk19 = 44,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _5, _END},
		.itemId = ITEM_TM45,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM45,
		.importance = 0,
		.unk19 = 45,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _6, _END},
		.itemId = ITEM_TM46,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM46,
		.importance = 0,
		.unk19 = 46,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _7, _END},
		.itemId = ITEM_TM47,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM47,
		.importance = 0,
		.unk19 = 47,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _8, _END},
		.itemId = ITEM_TM48,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM48,
		.importance = 0,
		.unk19 = 48,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _4, _9, _END},
		.itemId = ITEM_TM49,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM49,
		.importance = 0,
		.unk19 = 49,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _0, _END},
		.itemId = ITEM_TM50,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM50,
		.importance = 0,
		.unk19 = 50,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _M, _0, _1, _END},
		.itemId = ITEM_HM01_CUT,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HM01,
		.importance = 0,
		.unk19 = 121,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _M, _0, _2, _END},
		.itemId = ITEM_HM02_FLY,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HM02,
		.importance = 0,
		.unk19 = 122,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _M, _0, _3, _END},
		.itemId = ITEM_HM03_SURF,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HM03,
		.importance = 0,
		.unk19 = 123,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _M, _0, _4, _END},
		.itemId = ITEM_HM04_STRENGTH,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HM04,
		.importance = 0,
		.unk19 = 124,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _M, _0, _5, _END},
		.itemId = ITEM_HM05_DIVE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HM05,
		.importance = 0,
		.unk19 = 125,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _M, _0, _6, _END},
		.itemId = ITEM_HM06_ROCK_SMASH,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HM06,
		.importance = 0,
		.unk19 = 126,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _M, _0, _7, _END},
		.itemId = ITEM_HM07_WATERFALL,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HM07,
		.importance = 0,
		.unk19 = 127,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _M, _0, _8, _END},
		.itemId = ITEM_HM08_ROCK_CLIMB,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HM08,
		.importance = 0,
		.unk19 = 128,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _y, _n, _a, _m, _a, _x, _SPACE, _B, _a, _n, _d, _END},
		.itemId = ITEM_DYNAMAX_BAND,
		.price = 25000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_DYNAMAX_BAND,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _o, _l, _d, _SPACE, _T, _e, _e, _t, _h, _END},
		.itemId = ITEM_GOLD_TEETH,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_GOLD_TEETH,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_O, _a, _k, _APOSTROPHE, _s, _SPACE, _P, _a, _r, _c, _e, _l, _END},
		.itemId = ITEM_OAKS_PARCEL,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_OAKS_PARCEL,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _k, _eACUTE, _SPACE, _F, _l, _u, _t, _e, _END},
		.itemId = ITEM_POKE_FLUTE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_POKE_FLUTE,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_PokeFlute,
		.battleUsage = 2,
		.battleUseFunc = BattleUseFunc_PokeFlute,
		.secondaryId = 0
	},
	{
		.name = {_S, _e, _c, _r, _e, _t, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_SECRET_KEY,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_SECRET_KEY,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _i, _k, _e, _SPACE, _V, _o, _u, _c, _h, _e, _r, _END},
		.itemId = ITEM_BIKE_VOUCHER,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_BIKE_VOUCHER,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _e, _g, _a, _SPACE, _R, _i, _n, _g, _END},
		.itemId = ITEM_MEGA_RING,
		.price = 25000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_MEGA_RING,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_O, _l, _d, _SPACE, _A, _m, _b, _e, _r, _END},
		.itemId = ITEM_OLD_AMBER,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_OLD_AMBER,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _a, _r, _d, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_CARD_KEY,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_CARD_KEY,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _i, _f, _t, _SPACE, _K, _e, _y, _END},
		.itemId = ITEM_LIFT_KEY,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_LIFT_KEY,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _e, _l, _i, _x, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_HELIX_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_HELIX_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _o, _m, _e, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_DOME_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_DOME_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _i, _l, _p, _h, _SPACE, _S, _c, _o, _p, _e, _END},
		.itemId = ITEM_SILPH_SCOPE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_SILPH_SCOPE,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _i, _c, _y, _c, _l, _e, _END},
		.itemId = ITEM_BICYCLE,
		.price = 0,
		.holdEffect = 0,
        .holdEffectParam = 0,
		.description = DESC_BICYCLE,
		.importance = 1,
        .unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_Bike,
		.battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
	},
	{
		.name = {_T, _o, _w, _n, _SPACE, _M, _a, _p, _END},
		.itemId = ITEM_TOWN_MAP,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TOWN_MAP,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_TownMap,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_V, _S, _SPACE, _S, _e, _e, _k, _e, _r, _END},
		.itemId = ITEM_VS_SEEKER,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_VS_SEEKER,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_FIELD,
		.fieldUseFunc = FieldUseFunc_VsSeeker,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _a, _m, _e, _SPACE, _C, _h, _e, _c, _k, _e, _r, _END},
		.itemId = ITEM_FAME_CHECKER,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_FAME_CHECKER,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_FameChecker,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _SPACE, _C, _a, _s, _e, _END},
		.itemId = ITEM_TM_CASE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM_CASE,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_TmCase,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _e, _r, _r, _y, _SPACE, _P, _o, _u, _c, _h, _END},
		.itemId = ITEM_BERRY_POUCH,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_BERRY_POUCH,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_BerryPouch,
		.battleUsage = 3,
		.battleUseFunc = BattleUseFunc_BerryPouch,
		.secondaryId = 0
	},
	{
		.name = {_T, _e, _a, _c, _h, _y, _SPACE, _T, _V, _END},
		.itemId = ITEM_TEACHY_TV,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TEACHY_TV,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_TeachyTv,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _r, _i, _HYPHEN, _P, _a, _s, _s, _END},
		.itemId = ITEM_TRI_PASS,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TRI_PASS,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _a, _i, _n, _b, _o, _w, _SPACE, _P, _a, _s, _s, _END},
		.itemId = ITEM_RAINBOW_PASS,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RAINBOW_PASS,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _e, _a, _END},
		.itemId = ITEM_TEA,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TEA,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _y, _s, _t, _i, _c, _SPACE, _T, _i, _c, _k, _e, _t, _END},
		.itemId = ITEM_MYSTIC_TICKET,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_MYSTIC_TICKET,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _u, _r, _o, _r, _a, _SPACE, _T, _i, _c, _k, _e, _t, _END},
		.itemId = ITEM_AURORA_TICKET,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_AURORA_TICKET,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _w, _d, _e, _r, _SPACE, _J, _a, _r, _END},
		.itemId = ITEM_POWDER_JAR,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_POWDER_JAR,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_PowderJar,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _u, _b, _y, _END},
		.itemId = ITEM_RUBY,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RUBY,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _a, _p, _p, _h, _i, _r, _e, _END},
		.itemId = ITEM_SAPPHIRE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_SAPPHIRE,
		.importance = 1,
		.unk19 = 1,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_HYPHEN, _D, _O, _N, _T, _SPACE, _U, _S, _E, _HYPHEN, _END},
		.itemId = ITEM_NONE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = gText_ItemNone,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _1, _END},
		.itemId = ITEM_TM51,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM51,
		.importance = 0,
		.unk19 = 51,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _2, _END},
		.itemId = ITEM_TM52,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM52,
		.importance = 0,
		.unk19 = 52,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _3, _END},
		.itemId = ITEM_TM53,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM53,
		.importance = 0,
		.unk19 = 53,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _4, _END},
		.itemId = ITEM_TM54,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM54,
		.importance = 0,
		.unk19 = 54,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _5, _END},
		.itemId = ITEM_TM55,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM55,
		.importance = 0,
		.unk19 = 55,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _6, _END},
		.itemId = ITEM_TM56,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM56,
		.importance = 0,
		.unk19 = 56,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _7, _END},
		.itemId = ITEM_TM57,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM57,
		.importance = 0,
		.unk19 = 57,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _8, _END},
		.itemId = ITEM_TM58,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM58,
		.importance = 0,
		.unk19 = 58,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _5, _9, _END},
		.itemId = ITEM_TM59,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM59,
		.importance = 0,
		.unk19 = 59,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _0, _END},
		.itemId = ITEM_TM60,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM60,
		.importance = 0,
		.unk19 = 60,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _1, _END},
		.itemId = ITEM_TM61,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM61,
		.importance = 0,
		.unk19 = 61,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _2, _END},
		.itemId = ITEM_TM62,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM62,
		.importance = 0,
		.unk19 = 62,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _3, _END},
		.itemId = ITEM_TM63,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM63,
		.importance = 0,
		.unk19 = 63,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _4, _END},
		.itemId = ITEM_TM64,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM64,
		.importance = 0,
		.unk19 = 64,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _5, _END},
		.itemId = ITEM_TM65,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM65,
		.importance = 0,
		.unk19 = 65,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _6, _END},
		.itemId = ITEM_TM66,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM66,
		.importance = 0,
		.unk19 = 66,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _7, _END},
		.itemId = ITEM_TM67,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM67,
		.importance = 0,
		.unk19 = 67,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _8, _END},
		.itemId = ITEM_TM68,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM68,
		.importance = 0,
		.unk19 = 68,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _6, _9, _END},
		.itemId = ITEM_TM69,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM69,
		.importance = 0,
		.unk19 = 69,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _0, _END},
		.itemId = ITEM_TM70,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM70,
		.importance = 0,
		.unk19 = 70,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _1, _END},
		.itemId = ITEM_TM71,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM71,
		.importance = 0,
		.unk19 = 71,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _2, _END},
		.itemId = ITEM_TM72,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM72,
		.importance = 0,
		.unk19 = 72,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _3, _END},
		.itemId = ITEM_TM73,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM73,
		.importance = 0,
		.unk19 = 73,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _4, _END},
		.itemId = ITEM_TM74,
		.price = 8000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM74,
		.importance = 0,
		.unk19 = 74,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _5, _END},
		.itemId = ITEM_TM75,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM75,
		.importance = 0,
		.unk19 = 75,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _6, _END},
		.itemId = ITEM_TM76,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM76,
		.importance = 0,
		.unk19 = 76,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _7, _END},
		.itemId = ITEM_TM77,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM77,
		.importance = 0,
		.unk19 = 77,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _8, _END},
		.itemId = ITEM_TM78,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM78,
		.importance = 0,
		.unk19 = 78,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _7, _9, _END},
		.itemId = ITEM_TM79,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM79,
		.importance = 0,
		.unk19 = 79,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _0, _END},
		.itemId = ITEM_TM80,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM80,
		.importance = 0,
		.unk19 = 80,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _1, _END},
		.itemId = ITEM_TM81,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM81,
		.importance = 0,
		.unk19 = 81,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _2, _END},
		.itemId = ITEM_TM82,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM82,
		.importance = 0,
		.unk19 = 82,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _3, _END},
		.itemId = ITEM_TM83,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM83,
		.importance = 0,
		.unk19 = 83,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _4, _END},
		.itemId = ITEM_TM84,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM84,
		.importance = 0,
		.unk19 = 84,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _5, _END},
		.itemId = ITEM_TM85,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM85,
		.importance = 0,
		.unk19 = 85,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _6, _END},
		.itemId = ITEM_TM86,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM86,
		.importance = 0,
		.unk19 = 86,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _7, _END},
		.itemId = ITEM_TM87,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM87,
		.importance = 0,
		.unk19 = 87,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _8, _END},
		.itemId = ITEM_TM88,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM88,
		.importance = 0,
		.unk19 = 88,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _8, _9, _END},
		.itemId = ITEM_TM89,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM89,
		.importance = 0,
		.unk19 = 89,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _0, _END},
		.itemId = ITEM_TM90,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM90,
		.importance = 0,
		.unk19 = 90,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _1, _END},
		.itemId = ITEM_TM91,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM91,
		.importance = 0,
		.unk19 = 91,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _2, _END},
		.itemId = ITEM_TM92,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM92,
		.importance = 0,
		.unk19 = 92,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _3, _END},
		.itemId = ITEM_TM93,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM93,
		.importance = 0,
		.unk19 = 93,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _4, _END},
		.itemId = ITEM_TM94,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM94,
		.importance = 0,
		.unk19 = 94,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _5, _END},
		.itemId = ITEM_TM95,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM95,
		.importance = 0,
		.unk19 = 95,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _6, _END},
		.itemId = ITEM_TM96,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM96,
		.importance = 0,
		.unk19 = 96,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _7, _END},
		.itemId = ITEM_TM97,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM97,
		.importance = 0,
		.unk19 = 97,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _8, _END},
		.itemId = ITEM_TM98,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM98,
		.importance = 0,
		.unk19 = 98,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _9, _9, _END},
		.itemId = ITEM_TM99,
		.price = 12000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM99,
		.importance = 0,
		.unk19 = 99,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _0, _END},
		.itemId = ITEM_TM100,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM100,
		.importance = 0,
		.unk19 = 100,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _1, _END},
		.itemId = ITEM_TM101,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM101,
		.importance = 0,
		.unk19 = 101,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _2, _END},
		.itemId = ITEM_TM102,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM102,
		.importance = 0,
		.unk19 = 102,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _3, _END},
		.itemId = ITEM_TM103,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM103,
		.importance = 0,
		.unk19 = 103,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _4, _END},
		.itemId = ITEM_TM104,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM104,
		.importance = 0,
		.unk19 = 104,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _5, _END},
		.itemId = ITEM_TM105,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM105,
		.importance = 0,
		.unk19 = 105,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _6, _END},
		.itemId = ITEM_TM106,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM106,
		.importance = 0,
		.unk19 = 106,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _7, _END},
		.itemId = ITEM_TM107,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM107,
		.importance = 0,
		.unk19 = 107,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _8, _END},
		.itemId = ITEM_TM108,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM108,
		.importance = 0,
		.unk19 = 108,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _0, _9, _END},
		.itemId = ITEM_TM109,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM109,
		.importance = 0,
		.unk19 = 109,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _0, _END},
		.itemId = ITEM_TM110,
		.price = 15000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM110,
		.importance = 0,
		.unk19 = 110,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _1, _END},
		.itemId = ITEM_TM111,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM111,
		.importance = 0,
		.unk19 = 111,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _2, _END},
		.itemId = ITEM_TM112,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM112,
		.importance = 0,
		.unk19 = 112,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _3, _END},
		.itemId = ITEM_TM113,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM113,
		.importance = 0,
		.unk19 = 113,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _4, _END},
		.itemId = ITEM_TM114,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM114,
		.importance = 0,
		.unk19 = 114,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _5, _END},
		.itemId = ITEM_TM115,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM115,
		.importance = 0,
		.unk19 = 115,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _6, _END},
		.itemId = ITEM_TM116,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM116,
		.importance = 0,
		.unk19 = 116,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _7, _END},
		.itemId = ITEM_TM117,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM117,
		.importance = 0,
		.unk19 = 117,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _8, _END},
		.itemId = ITEM_TM118,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM118,
		.importance = 0,
		.unk19 = 118,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _1, _9, _END},
		.itemId = ITEM_TM119,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM119,
		.importance = 0,
		.unk19 = 119,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _M, _1, _2, _0, _END},
		.itemId = ITEM_TM120,
		.price = 25000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_TM120,
		.importance = 0,
		.unk19 = 120,
		.pocket = POCKET_TM_CASE,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = NULL,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_O, _c, _c, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_OCCA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 10,
		.description = DESC_OCCA_BERRY,
		.importance = 0,
		.unk19 = 36,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _a, _s, _s, _h, _o, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_PASSHO_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 11,
		.description = DESC_PASSHO_BERRY,
		.importance = 0,
		.unk19 = 37,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _a, _c, _a, _n, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_WACAN_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 13,
		.description = DESC_WACAN_BERRY,
		.importance = 0,
		.unk19 = 38,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _i, _n, _d, _o, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_RINDO_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 12,
		.description = DESC_RINDO_BERRY,
		.importance = 0,
		.unk19 = 39,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_Y, _a, _c, _h, _e, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_YACHE_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 15,
		.description = DESC_YACHE_BERRY,
		.importance = 0,
		.unk19 = 40,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _h, _o, _p, _l, _e, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_CHOPLE_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 1,
		.description = DESC_CHOPLE_BERRY,
		.importance = 0,
		.unk19 = 41,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_K, _e, _b, _i, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_KEBIA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 3,
		.description = DESC_KEBIA_BERRY,
		.importance = 0,
		.unk19 = 42,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _h, _u, _c, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_SHUCA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 4,
		.description = DESC_SHUCA_BERRY,
		.importance = 0,
		.unk19 = 43,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _o, _b, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_COBA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 2,
		.description = DESC_COBA_BERRY,
		.importance = 0,
		.unk19 = 44,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _a, _y, _a, _p, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_PAYAPA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 14,
		.description = DESC_PAYAPA_BERRY,
		.importance = 0,
		.unk19 = 45,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _a, _n, _g, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_TANGA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 6,
		.description = DESC_TANGA_BERRY,
		.importance = 0,
		.unk19 = 46,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _h, _a, _r, _t, _i, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_CHARTI_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 5,
		.description = DESC_CHARTI_BERRY,
		.importance = 0,
		.unk19 = 47,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_K, _a, _s, _i, _b, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_KASIB_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 7,
		.description = DESC_KASIB_BERRY,
		.importance = 0,
		.unk19 = 48,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _a, _b, _a, _n, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_HABAN_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 16,
		.description = DESC_HABAN_BERRY,
		.importance = 0,
		.unk19 = 49,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _o, _l, _b, _u, _r, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_COLBUR_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 17,
		.description = DESC_COLBUR_BERRY,
		.importance = 0,
		.unk19 = 50,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _a, _b, _i, _r, _i, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_BABIRI_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 8,
		.description = DESC_BABIRI_BERRY,
		.importance = 0,
		.unk19 = 51,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _h, _i, _l, _a, _n, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_CHILAN_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 0,
		.description = DESC_CHILAN_BERRY,
		.importance = 0,
		.unk19 = 52,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _i, _c, _l, _e, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_MICLE_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_MICLE_BERRY,
		.holdEffectParam = 0,
		.description = DESC_MICLE_BERRY,
		.importance = 0,
		.unk19 = 61,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _u, _s, _t, _a, _p, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_CUSTAP_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_CUSTAP_BERRY,
		.holdEffectParam = 0,
		.description = DESC_CUSTAP_BERRY,
		.importance = 0,
		.unk19 = 62,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_J, _a, _b, _o, _c, _a, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_JABOCA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_JABOCA_ROWAP_BERRY,
		.holdEffectParam = 0,
		.description = DESC_JABOCA_BERRY,
		.importance = 0,
		.unk19 = 63,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _o, _w, _a, _p, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_ROWAP_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_JABOCA_ROWAP_BERRY,
		.holdEffectParam = 1,
		.description = DESC_ROWAP_BERRY,
		.importance = 0,
		.unk19 = 64,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _o, _s, _e, _l, _i, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_ROSELI_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_WEAKNESS_BERRY,
		.holdEffectParam = 23,
		.description = DESC_ROSELI_BERRY,
		.importance = 0,
		.unk19 = 65,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_K, _e, _e, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_KEE_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_KEE_BERRY,
		.holdEffectParam = 0,
		.description = DESC_KEE_BERRY,
		.importance = 0,
		.unk19 = 66,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _a, _r, _a, _n, _g, _SPACE, _B, _e, _r, _r, _y, _END},
		.itemId = ITEM_MARANGA_BERRY,
		.price = 20,
		.holdEffect = ITEM_EFFECT_MARANGA_BERRY,
		.holdEffectParam = 0,
		.description = DESC_MARANG_BERRY,
		.importance = 0,
		.unk19 = 67,
		.pocket = POCKET_BERRY_POUCH,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_O, _v, _a, _l, _SPACE, _C, _h, _a, _r, _m, _END},
		.itemId = ITEM_OVAL_CHARM,
		.price = 30000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_OVAL_CHARM,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _h, _i, _n, _y, _SPACE, _C, _h, _a, _r, _m, _END},
		.itemId = ITEM_SHINY_CHARM,
		.price = 50000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_SHINY_CHARM,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _a, _i, _n, _b, _o, _w, _SPACE, _W, _i, _n, _g, _END},
		.itemId = ITEM_RAINBOW_WING,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RAINBOW_WING,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _i, _l, _v, _e, _r, _SPACE, _W, _i, _n, _g, _END},
		.itemId = ITEM_SILVER_WING,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_SILVER_WING,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _a, _g, _m, _a, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_MAGMA_STONE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_MAGMA_STONE,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _i, _g, _h, _t, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_LIGHT_STONE,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_LIGHT_STONE,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _a, _r, _k, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_DARK_STONE,
		.price = 20000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_DARK_STONE,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _u, _n, _SPACE, _F, _l, _u, _t, _e, _END},
		.itemId = ITEM_SUN_FLUTE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_SUN_FLUTE,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _o, _o, _n, _SPACE, _F, _l, _u, _t, _e, _END},
		.itemId = ITEM_MOON_FLUTE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_MOON_FLUTE,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _a, _c, _i, _d, _e, _a, _END},
		.itemId = ITEM_GRACIDEA,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_GRACIDEA,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _N, _A, _SPACE, _S, _p, _l, _i, _c, _e, _r, _s, _END},
		.itemId = ITEM_DNA_SPLICERS,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_DNA_SPLICERS,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _v, _e, _a, _l, _SPACE, _G, _l, _a, _s, _s, _END},
		.itemId = ITEM_REVEAL_GLASS,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_REVEAL_GLASS,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _r, _i, _s, _o, _n, _B, _o, _t, _t, _l, _e, _END},
		.itemId = ITEM_PRISON_BOTTLE,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_PRISON_BOTTLE,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_N, _HYPHEN, _S, _o, _l, _a, _r, _i, _z, _e, _r, _END},
		.itemId = ITEM_N_SOLARIZER,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_N_SOLARIZER,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_N, _HYPHEN, _L, _u, _n, _a, _r, _i, _z, _e, _r, _END},
		.itemId = ITEM_N_LUNARIZER,
		.price = 0,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_N_LUNARIZER,
		.importance = 1,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _u, _s, _t, _y, _SPACE, _S, _w, _o, _r, _d, _END},
		.itemId = ITEM_RUSTED_SWORD,
		.price = 0,
		.holdEffect = ITEM_EFFECT_RUSTED_SWORD,
		.holdEffectParam = 0,
		.description = DESC_RUSTED_SWORD,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _u, _s, _t, _y, _SPACE, _S, _h, _i, _e, _l, _d, _END},
		.itemId = ITEM_RUSTED_SHIELD,
		.price = 0,
		.holdEffect = ITEM_EFFECT_RUSTED_SHIELD,
		.holdEffectParam = 0,
		.description = DESC_RUSTED_SHIELD,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _d, _a, _m, _a, _n, _t, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_ADAMANT_ORB,
		.price = 20000,
		.holdEffect = ITEM_EFFECT_ADAMANT_ORB,
		.holdEffectParam = 0,
		.description = DESC_ADAMANT_ORB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _u, _s, _t, _r, _o, _u, _s, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_LUSTROUS_ORB,
		.price = 20000,
		.holdEffect = ITEM_EFFECT_LUSTROUS_ORB,
		.holdEffectParam = 0,
		.description = DESC_LUSTROUS_ORB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _i, _s, _e, _o, _u, _s, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_GRISEOUS_ORB,
		.price = 20000,
		.holdEffect = ITEM_EFFECT_GRISEOUS_ORB,
		.holdEffectParam = 0,
		.description = DESC_GRISEOUS_ORB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _i, _s, _t, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_FIST_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 1,
		.description = DESC_FIST_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _k, _y, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_SKY_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 2,
		.description = DESC_SKY_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _o, _x, _i, _c, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_TOXIC_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 3,
		.description = DESC_TOXIC_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_E, _a, _r, _t, _h, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_EARTH_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 4,
		.description = DESC_EARTH_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _t, _o, _n, _e, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_STONE_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 5,
		.description = DESC_STONE_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _n, _s, _e, _c, _t, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_INSECT_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 6,
		.description = DESC_INSECT_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _p, _o, _o, _k, _y, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_SPOOKY_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 7,
		.description = DESC_SPOOKY_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _r, _o, _n, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_IRON_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 8,
		.description = DESC_IRON_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _l, _a, _m, _e, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_FLAME_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 10,
		.description = DESC_FLAME_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _p, _l, _a, _s, _h, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_SPLASH_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 11,
		.description = DESC_SPLASH_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _e, _a, _d, _o, _w, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_MEADOW_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 12,
		.description = DESC_MEADOW_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_Z, _a, _p, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_ZAP_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 13,
		.description = DESC_ZAP_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _i, _n, _d, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_MIND_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 14,
		.description = DESC_MIND_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _c, _i, _c, _l, _e, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_ICICLE_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 15,
		.description = DESC_ICICLE_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _r, _a, _c, _o, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_DRACO_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 16,
		.description = DESC_DRACO_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _r, _e, _a, _d, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_DREAD_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 17,
		.description = DESC_DREAD_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _i, _x, _i, _e, _SPACE, _P, _l, _a, _t, _e, _END},
		.itemId = ITEM_PIXIE_PLATE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_PLATE,
		.holdEffectParam = 23,
		.description = DESC_PIXIE_PLATE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _i, _g, _h, _t, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_FIGHTING_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 1,
		.description = DESC_FIGHTING_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _l, _y, _i, _n, _g, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_FLYING_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 2,
		.description = DESC_FLYING_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _i, _s, _o, _n, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_POISON_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 3,
		.description = DESC_POISON_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _o, _u, _n, _d, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_GROUND_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 4,
		.description = DESC_GROUND_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _o, _c, _k, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_ROCK_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 5,
		.description = DESC_ROCK_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _u, _g, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_BUG_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 6,
		.description = DESC_BUG_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _h, _o, _s, _t, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_GHOST_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 7,
		.description = DESC_GHOST_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _t, _e, _e, _l, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_STEEL_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 8,
		.description = DESC_STEEL_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _i, _r, _e, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_FIRE_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 10,
		.description = DESC_FIRE_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _a, _t, _e, _r, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_WATER_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 11,
		.description = DESC_WATER_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _a, _s, _s, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_GRASS_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 12,
		.description = DESC_GRASS_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_E, _l, _e, _c, _t, _r, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_ELECTRIC_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 13,
		.description = DESC_ELECTRIC_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _s, _y, _c, _h, _i, _c, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_PSYCHIC_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 14,
		.description = DESC_PSYCHIC_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _c, _e, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_ICE_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 15,
		.description = DESC_ICE_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _r, _a, _g, _o, _n, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_DRAGON_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 16,
		.description = DESC_DRAGON_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _a, _r, _k, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_DARK_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 17,
		.description = DESC_DARK_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _a, _i, _r, _y, _SPACE, _M, _e, _m, _PERIOD, _END},
		.itemId = ITEM_FAIRY_MEMORY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_MEMORY,
		.holdEffectParam = 23,
		.description = DESC_FAIRY_MEMORY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _u, _r, _n, _SPACE, _D, _r, _i, _v, _e, _END},
		.itemId = ITEM_BURN_DRIVE,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_DRIVE,
		.holdEffectParam = 10,
		.description = DESC_BURN_DRIVE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _o, _u, _s, _e, _SPACE, _D, _r, _i, _v, _e, _END},
		.itemId = ITEM_DOUSE_DRIVE,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_DRIVE,
		.holdEffectParam = 11,
		.description = DESC_DOUSE_DRIVE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _h, _o, _c, _k, _SPACE, _D, _r, _i, _v, _e, _END},
		.itemId = ITEM_SHOCK_DRIVE,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_DRIVE,
		.holdEffectParam = 13,
		.description = DESC_SHOCK_DRIVE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _h, _i, _l, _l, _SPACE, _D, _r, _i, _v, _e, _END},
		.itemId = ITEM_CHILL_DRIVE,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_DRIVE,
		.holdEffectParam = 15,
		.description = DESC_CHILL_DRIVE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _d, _SPACE, _N, _e, _c, _t, _a, _r, _END},
		.itemId = ITEM_RED_NECTAR,
		.price = 300,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RED_NECTAR,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_Y, _e, _l, _l, _o, _w, _N, _e, _c, _t, _a, _r, _END},
		.itemId = ITEM_YELLOW_NECTAR,
		.price = 300,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_YELLOW_NECTAR,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _i, _n, _k, _SPACE, _N, _e, _c, _t, _a, _r, _END},
		.itemId = ITEM_PINK_NECTAR,
		.price = 300,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_PINK_NECTAR,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _u, _r, _p, _l, _e, _N, _e, _c, _t, _a, _r, _END},
		.itemId = ITEM_PURPLE_NECTAR,
		.price = 300,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_PURPLE_NECTAR,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_PARTY_MENU,
		.fieldUseFunc = FieldUseFunc_FormChangeItem,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_N, _e, _c, _r, _o, _z, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_ULTRANECROZIUM_Z,
		.price = 0,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_ULTRANECROZIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_V, _e, _n, _u, _s, _a, _u, _r, _i, _t, _e, _END},
		.itemId = ITEM_VENUSAURITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_VENUSAURITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _h, _a, _r, _z, _a, _r, _d, _i, _t, _e, _X, _END},
		.itemId = ITEM_CHARIZARDITE_X,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_CHARIZARDITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _h, _a, _r, _z, _a, _r, _d, _i, _t, _e, _Y, _END},
		.itemId = ITEM_CHARIZARDITE_Y,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_CHARIZARDITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _l, _a, _s, _t, _o, _i, _s, _n, _i, _t, _e, _END},
		.itemId = ITEM_BLASTOISINITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_BLASTOISINITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _e, _e, _d, _r, _i, _l, _l, _i, _t, _e, _END},
		.itemId = ITEM_BEEDRILLITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_BEEDRILLITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _i, _d, _g, _e, _o, _t, _i, _t, _e, _END},
		.itemId = ITEM_PIDGEOTITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_PIDGEOTITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _l, _a, _k, _a, _z, _i, _t, _e, _END},
		.itemId = ITEM_ALAKAZITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_ALAKAZITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _l, _o, _w, _b, _r, _o, _n, _i, _t, _e, _END},
		.itemId = ITEM_SLOWBRONITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_SLOWBRONITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _e, _n, _g, _a, _r, _i, _t, _e, _END},
		.itemId = ITEM_GENGARITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_GENGARITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_K, _a, _n, _g, _a, _s, _k, _a, _n, _i, _t, _e, _END},
		.itemId = ITEM_KANGASKHANITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_KANGASKHANITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _i, _n, _s, _i, _r, _i, _t, _e, _END},
		.itemId = ITEM_PINSIRITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_PINSIRITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _y, _a, _r, _a, _d, _o, _s, _i, _t, _e, _END},
		.itemId = ITEM_GYARADOSITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_GYARADOSITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _e, _r, _o, _d, _a, _c, _t, _l, _i, _t, _e, _END},
		.itemId = ITEM_AERODACTYLITE,
		.price = 0,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_AERODACTYLITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _e, _w, _t, _w, _o, _n, _i, _t, _e, _X, _END},
		.itemId = ITEM_MEWTWONITE_X,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_MEWTWONITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _e, _w, _t, _w, _o, _n, _i, _t, _e, _Y, _END},
		.itemId = ITEM_MEWTWONITE_Y,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_MEWTWONITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _m, _p, _h, _a, _r, _o, _s, _i, _t, _e, _END},
		.itemId = ITEM_AMPHAROSITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_AMPHAROSITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _t, _e, _e, _l, _i, _x, _i, _t, _e, _END},
		.itemId = ITEM_STEELIXITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_STEELIXITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _c, _i, _z, _o, _r, _i, _t, _e, _END},
		.itemId = ITEM_SCIZORITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_SCIZORITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _e, _r, _a, _c, _r, _o, _n, _i, _t, _e, _END},
		.itemId = ITEM_HERACRONITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_HERACRONITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _o, _u, _n, _d, _o, _o, _m, _n, _i, _t, _e, _END},
		.itemId = ITEM_HOUNDOOMINITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_HOUNDOOMINITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _y, _r, _a, _n, _i, _t, _a, _r, _i, _t, _e, _END},
		.itemId = ITEM_TYRANITARITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_TYRANITARITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _c, _e, _p, _t, _i, _l, _i, _t, _e, _END},
		.itemId = ITEM_SCEPTILITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_SCEPTILITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _l, _a, _z, _i, _k, _e, _n, _i, _t, _e, _END},
		.itemId = ITEM_BLAZIKENITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_BLAZIKENITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _w, _a, _m, _p, _e, _r, _t, _i, _t, _e, _END},
		.itemId = ITEM_SWAMPERTITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_SWAMPERTITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _a, _r, _d, _e, _v, _o, _i, _r, _i, _t, _e, _END},
		.itemId = ITEM_GARDEVOIRITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_GARDEVOIRITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _a, _b, _l, _e, _n, _i, _t, _e, _END},
		.itemId = ITEM_SABLENITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_SABLENITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _a, _w, _i, _l, _i, _t, _e, _END},
		.itemId = ITEM_MAWILITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_MAWILITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _g, _g, _r, _o, _n, _i, _t, _e, _END},
		.itemId = ITEM_AGGRONITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_AGGRONITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _e, _d, _i, _c, _h, _a, _m, _i, _t, _e, _END},
		.itemId = ITEM_MEDICHAMITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_MEDICHAMITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _a, _n, _e, _c, _t, _i, _t, _e, _END},
		.itemId = ITEM_MANECTITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_MANECTITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _h, _a, _r, _p, _e, _d, _o, _n, _i, _t, _e, _END},
		.itemId = ITEM_SHARPEDONITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_SHARPEDONITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _a, _m, _e, _r, _u, _p, _t, _i, _t, _e, _END},
		.itemId = ITEM_CAMERUPTITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_CAMERUPTITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _l, _t, _a, _r, _i, _a, _n, _i, _t, _e, _END},
		.itemId = ITEM_ALTARIANITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_ALTARIANITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _a, _n, _e, _t, _t, _i, _t, _e, _END},
		.itemId = ITEM_BANETTITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_BANETTITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _b, _s, _o, _l, _i, _t, _e, _END},
		.itemId = ITEM_ABSOLITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_ABSOLITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _l, _a, _l, _i, _t, _i, _t, _e, _END},
		.itemId = ITEM_GLALITITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_GLALITITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _a, _l, _a, _m, _e, _n, _c, _i, _t, _e, _END},
		.itemId = ITEM_SALAMENCITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_SALAMENCITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _e, _t, _a, _g, _r, _o, _s, _s, _i, _t, _e, _END},
		.itemId = ITEM_METAGROSSITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_METAGROSSITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _a, _t, _i, _a, _s, _i, _t, _e, _END},
		.itemId = ITEM_LATIASITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_LATIASITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _a, _t, _i, _o, _s, _i, _t, _e, _END},
		.itemId = ITEM_LATIOSITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_LATIOSITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _o, _p, _u, _n, _n, _i, _t, _e, _END},
		.itemId = ITEM_LOPUNNITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_LOPUNNITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _a, _r, _c, _h, _o, _m, _p, _i, _t, _e, _END},
		.itemId = ITEM_GARCHOMPITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_GARCHOMPITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _u, _c, _a, _r, _i, _o, _n, _i, _t, _e, _END},
		.itemId = ITEM_LUCARIONITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_LUCARIONITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _b, _o, _m, _a, _s, _i, _t, _e, _END},
		.itemId = ITEM_ABOMASITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_ABOMASITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _a, _l, _l, _a, _d, _i, _t, _e, _END},
		.itemId = ITEM_GALLADITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_GALLADITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _u, _d, _i, _n, _i, _t, _e, _END},
		.itemId = ITEM_AUDINITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_AUDINITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _i, _a, _n, _c, _i, _t, _e, _END},
		.itemId = ITEM_DIANCITE,
		.price = 15000,
		.holdEffect = ITEM_EFFECT_MEGA_STONE,
		.holdEffectParam = 0,
		.description = DESC_DIANCITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_N, _o, _r, _m, _a, _l, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_NORMALIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 0,
		.description = DESC_NORMALIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _i, _g, _h, _t, _i, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_FIGHTINIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 1,
		.description = DESC_FIGHTINIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _l, _y, _i, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_FLYINIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 2,
		.description = DESC_FLYINIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _i, _s, _o, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_POISONIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 3,
		.description = DESC_POISONIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _o, _u, _n, _d, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_GROUNDIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 4,
		.description = DESC_GROUNDIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _o, _c, _k, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_ROCKIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 5,
		.description = DESC_ROCKIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _u, _g, _i, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_BUGINIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 6,
		.description = DESC_BUGINIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _h, _o, _s, _t, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_GHOSTIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 7,
		.description = DESC_GHOSTIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _t, _e, _e, _l, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_STEELIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 8,
		.description = DESC_STEELIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _i, _r, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_FIRIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 10,
		.description = DESC_FIRIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _a, _t, _e, _r, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_WATERIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 11,
		.description = DESC_WATERIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _a, _s, _s, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_GRASSIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 12,
		.description = DESC_GRASSIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_E, _l, _e, _c, _t, _r, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_ELECTRIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 13,
		.description = DESC_ELECTRIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _s, _y, _c, _h, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_PSYCHIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 14,
		.description = DESC_PSYCHIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _c, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_ICIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 15,
		.description = DESC_ICIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _r, _a, _g, _o, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_DRAGONIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 16,
		.description = DESC_DRAGONIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _a, _r, _k, _i, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_DARKINIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 17,
		.description = DESC_DARKINIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _a, _i, _r, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_FAIRIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 23,
		.description = DESC_FAIRIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _l, _o, _r, _i, _c, _h, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_ALORAICHIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_ALORAICHIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _e, _c, _i, _d, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_DECIDIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_DECIDIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_E, _e, _v, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_EEVIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_EEVIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _n, _c, _i, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_INCINIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_INCINIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_K, _o, _m, _m, _o, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_KOMMONIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_KOMMONIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _u, _n, _a, _l, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_LUNALIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_LUNALIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _y, _c, _a, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_LYCANIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_LYCANIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _a, _r, _s, _h, _a, _d, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_MARSHADIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_MARSHADIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _e, _w, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_MEWNIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_MEWNIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _i, _m, _i, _k, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_MIMIKIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_MIMIKIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _i, _k, _a, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_PIKANIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_PIKANIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _i, _k, _s, _h, _u, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_PIKASHUNIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_PIKASHUNIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _r, _i, _m, _a, _r, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_PRIMARIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_PRIMARIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _n, _o, _r, _l, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_SNORLIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_SNORLIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _o, _l, _g, _a, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_SOLGANIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_SOLGANIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _a, _p, _u, _n, _i, _u, _m, _SPACE, _Z, _END},
		.itemId = ITEM_TAPUNIUM_Z,
		.price = 10000,
		.holdEffect = ITEM_EFFECT_Z_CRYSTAL,
		.holdEffectParam = 255,
		.description = DESC_TAPUNIUM_Z,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _l, _k, _SPACE, _A, _p, _r, _i, _c, _o, _r, _n, _END},
		.itemId = ITEM_BLACK_APRICORN,
		.price = 200,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_BLACK_APRICORN,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _l, _u, _SPACE, _A, _p, _r, _i, _c, _o, _r, _n, _END},
		.itemId = ITEM_BLUE_APRICORN,
		.price = 200,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_BLUE_APRICORN,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _n, _SPACE, _A, _p, _r, _i, _c, _o, _r, _n, _END},
		.itemId = ITEM_GREEN_APRICORN,
		.price = 200,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_GREEN_APRICORN,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _n, _k, _SPACE, _A, _p, _r, _i, _c, _o, _r, _n, _END},
		.itemId = ITEM_PINK_APRICORN,
		.price = 200,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_PINK_APRICORN,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _d, _SPACE, _A, _p, _r, _i, _c, _o, _r, _n, _END},
		.itemId = ITEM_RED_APRICORN,
		.price = 200,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RED_APRICORN,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _h, _t, _SPACE, _A, _p, _r, _i, _c, _o, _r, _n, _END},
		.itemId = ITEM_WHITE_APRICORN,
		.price = 200,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_WHITE_APRICORN,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_Y, _l, _w, _SPACE, _A, _p, _r, _i, _c, _o, _r, _n, _END},
		.itemId = ITEM_YELLOW_APRICORN,
		.price = 200,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_YELLOW_APRICORN,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _l, _i, _c, _SPACE, _C, _o, _p, _p, _e, _r, _END},
		.itemId = ITEM_RELIC_COPPER,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RELIC_COPPER,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _l, _i, _c, _SPACE, _S, _i, _l, _v, _e, _r, _END},
		.itemId = ITEM_RELIC_SILVER,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RELIC_SILVER,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _l, _i, _c, _SPACE, _G, _o, _l, _d, _END},
		.itemId = ITEM_RELIC_GOLD,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RELIC_GOLD,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _l, _i, _c, _SPACE, _V, _a, _s, _e, _END},
		.itemId = ITEM_RELIC_VASE,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RELIC_VASE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _l, _i, _c, _SPACE, _B, _a, _n, _d, _END},
		.itemId = ITEM_RELIC_BAND,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RELIC_BAND,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _l, _i, _c, _SPACE, _S, _t, _a, _t, _u, _e, _END},
		.itemId = ITEM_RELIC_STATUE,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RELIC_STATUE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _l, _i, _c, _SPACE, _C, _r, _o, _w, _n, _END},
		.itemId = ITEM_RELIC_CROWN,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_RELIC_CROWN,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _k, _u, _l, _l, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_SKULL_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_SKULL_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _r, _m, _o, _r, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_ARMOR_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_ARMOR_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _o, _v, _e, _r, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_COVER_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_COVER_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _l, _u, _m, _e, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_PLUME_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_PLUME_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_J, _a, _w, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_JAW_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_JAW_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _a, _i, _l, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_SAIL_FOSSIL,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_SAIL_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _i, _r, _d, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_FOSSILIZED_BIRD,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_BIRD_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _i, _s, _h, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_FOSSILIZED_FISH,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_FISH_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _r, _a, _k, _e, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_FOSSILIZED_DRAKE,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_DRAKE_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _i, _n, _o, _SPACE, _F, _o, _s, _s, _i, _l, _END},
		.itemId = ITEM_FOSSILIZED_DINO,
		.price = 7000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_DINO_FOSSIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_O, _d, _d, _SPACE, _K, _e, _y, _s, _t, _o, _n, _e, _END},
		.itemId = ITEM_ODD_KEYSTONE,
		.price = 2000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_ODD_KEYSTONE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _o, _t, _t, _l, _e, _SPACE, _C, _a, _p, _END},
		.itemId = ITEM_BOTTLE_CAP,
		.price = 5000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_BOTTLE_CAP,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _PERIOD, _B, _o, _t, _t, _l, _e, _SPACE, _C, _a, _p, _END},
		.itemId = ITEM_GOLD_BOTTLE_CAP,
		.price = 10000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_GOLD_BOTTLE_CAP,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _i, _s, _h, _SPACE, _P, _i, _e, _c, _e, _END},
		.itemId = ITEM_WISHING_PIECE,
		.price = 3000,
		.holdEffect = 0,
		.holdEffectParam = 0,
		.description = DESC_WISHING_PIECE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _w, _e, _r, _SPACE, _B, _r, _a, _c, _e, _r, _END},
		.itemId = ITEM_POWER_BRACER,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_MACHO_BRACE,
		.holdEffectParam = 2,
		.description = DESC_POWER_BRACER,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _w, _e, _r, _SPACE, _B, _e, _l, _t, _END},
		.itemId = ITEM_POWER_BELT,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_MACHO_BRACE,
		.holdEffectParam = 3,
		.description = DESC_POWER_BELT,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _w, _e, _r, _SPACE, _L, _e, _n, _s, _END},
		.itemId = ITEM_POWER_LENS,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_MACHO_BRACE,
		.holdEffectParam = 5,
		.description = DESC_POWER_LENS,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _w, _e, _r, _SPACE, _B, _a, _n, _d, _END},
		.itemId = ITEM_POWER_BAND,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_MACHO_BRACE,
		.holdEffectParam = 6,
		.description = DESC_POWER_BAND,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _w, _e, _r, _SPACE, _A, _n, _k, _l, _e, _t, _END},
		.itemId = ITEM_POWER_ANKLET,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_MACHO_BRACE,
		.holdEffectParam = 4,
		.description = DESC_POWER_ANKLET,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _w, _e, _r, _SPACE, _W, _e, _i, _g, _h, _t, _END},
		.itemId = ITEM_POWER_WEIGHT,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_MACHO_BRACE,
		.holdEffectParam = 1,
		.description = DESC_POWER_WEIGHT,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _u, _c, _k, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_LUCK_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_DOUBLE_PRIZE,
		.holdEffectParam = 10,
		.description = DESC_LUCK_INCENSE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _u, _l, _l, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_FULL_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_LAGGING_TAIL,
		.holdEffectParam = 0,
		.description = DESC_FULL_INCENSE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_O, _d, _d, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_ODD_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_PSYCHIC_POWER,
		.holdEffectParam = 20,
		.description = DESC_ODD_INCENSE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _u, _r, _e, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_PURE_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_REPEL,
		.holdEffectParam = 0,
		.description = DESC_PURE_INCENSE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _o, _c, _k, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_ROCK_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_ROCK_POWER,
		.holdEffectParam = 20,
		.description = DESC_ROCK_INCENSE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _o, _s, _e, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_ROSE_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_GRASS_POWER,
		.holdEffectParam = 20,
		.description = DESC_ROSE_INCENSE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _a, _v, _e, _SPACE, _I, _n, _c, _e, _n, _s, _e, _END},
		.itemId = ITEM_WAVE_INCENSE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_WATER_POWER,
		.holdEffectParam = 20,
		.description = DESC_WAVE_INCENSE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_N, _o, _r, _m, _a, _l, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_NORMAL_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 0,
		.description = DESC_NORMAL_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _i, _g, _h, _t, _i, _n, _g, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_FIGHTING_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 1,
		.description = DESC_FIGHTING_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _l, _y, _i, _n, _g, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_FLYING_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 2,
		.description = DESC_FLYING_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _i, _s, _o, _n, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_POISON_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 3,
		.description = DESC_POISON_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _o, _u, _n, _d, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_GROUND_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 4,
		.description = DESC_GROUND_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _o, _c, _k, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_ROCK_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 5,
		.description = DESC_ROCK_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _u, _g, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_BUG_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 6,
		.description = DESC_BUG_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _h, _o, _s, _t, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_GHOST_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 8,
		.description = DESC_GHOST_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _t, _e, _e, _l, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_STEEL_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 8,
		.description = DESC_STEEL_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _i, _r, _e, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_FIRE_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 10,
		.description = DESC_FIRE_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _a, _t, _e, _r, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_WATER_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 11,
		.description = DESC_WATER_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _a, _s, _s, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_GRASS_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 12,
		.description = DESC_GRASS_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_E, _l, _e, _c, _t, _r, _i, _c, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_ELECTRIC_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 13,
		.description = DESC_ELECTRIC_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _s, _y, _c, _h, _i, _c, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_PSYCHIC_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 14,
		.description = DESC_PSYCHIC_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _c, _e, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_ICE_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 15,
		.description = DESC_ICE_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _r, _a, _g, _o, _n, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_DRAGON_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 16,
		.description = DESC_DRAGON_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _a, _r, _k, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_DARK_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 17,
		.description = DESC_DARK_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _a, _i, _r, _y, _SPACE, _G, _e, _m, _END},
		.itemId = ITEM_FAIRY_GEM,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GEM,
		.holdEffectParam = 23,
		.description = DESC_FAIRY_GEM,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _i, _d, _e, _SPACE, _L, _e, _n, _s, _END},
		.itemId = ITEM_WIDE_LENS,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_WIDE_LENS,
		.holdEffectParam = 10,
		.description = DESC_WIDE_LENS,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _u, _s, _c, _l, _e, _SPACE, _B, _a, _n, _d, _END},
		.itemId = ITEM_MUSCLE_BAND,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_MUSCLE_BAND,
		.holdEffectParam = 0,
		.description = DESC_MUSCLE_BAND,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _i, _s, _e, _SPACE, _G, _l, _a, _s, _s, _e, _s, _END},
		.itemId = ITEM_WISE_GLASSES,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_WISE_GLASSES,
		.holdEffectParam = 0,
		.description = DESC_WISE_GLASSES,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_E, _x, _p, _e, _r, _t, _SPACE, _B, _e, _l, _t, _END},
		.itemId = ITEM_EXPERT_BELT,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_EXPERT_BELT,
		.holdEffectParam = 0,
		.description = DESC_EXPERT_BELT,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _i, _g, _h, _t, _SPACE, _C, _l, _a, _y, _END},
		.itemId = ITEM_LIGHT_CLAY,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_LIGHT_CLAY,
		.holdEffectParam = 0,
		.description = DESC_LIGHT_CLAY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _i, _f, _e, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_LIFE_ORB,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_LIFE_ORB,
		.holdEffectParam = 0,
		.description = DESC_LIFE_ORB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_P, _o, _w, _e, _r, _SPACE, _H, _e, _r, _b, _END},
		.itemId = ITEM_POWER_HERB,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_POWER_HERB,
		.holdEffectParam = 0,
		.description = DESC_POWER_HERB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_T, _o, _x, _i, _c, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_TOXIC_ORB,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_TOXIC_ORB,
		.holdEffectParam = 0,
		.description = DESC_TOXIC_ORB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _l, _a, _m, _e, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_FLAME_ORB,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_FLAME_ORB,
		.holdEffectParam = 0,
		.description = DESC_FLAME_ORB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_Q, _u, _i, _c, _k, _SPACE, _P, _o, _w, _d, _e, _r, _END},
		.itemId = ITEM_QUICK_POWDER,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_QUICK_POWDER,
		.holdEffectParam = 0,
		.description = DESC_QUICK_POWDER,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _o, _c, _u, _s, _SPACE, _S, _a, _s, _h, _END},
		.itemId = ITEM_FOCUS_SASH,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_FOCUS_BAND,
		.holdEffectParam = 100,
		.description = DESC_FOCUS_SASH,
		.importance = 0,
		.unk19 = 1,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_Z, _o, _o, _m, _SPACE, _L, _e, _n, _s, _END},
		.itemId = ITEM_ZOOM_LENS,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_ZOOM_LENS,
		.holdEffectParam = 20,
		.description = DESC_ZOOM_LENS,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_M, _e, _t, _r, _o, _n, _o, _m, _e, _END},
		.itemId = ITEM_METRONOME,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_METRONOME,
		.holdEffectParam = 0,
		.description = DESC_METRONOME,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _r, _o, _n, _SPACE, _B, _a, _l, _l, _END},
		.itemId = ITEM_IRON_BALL,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_IRON_BALL,
		.holdEffectParam = 0,
		.description = DESC_IRON_BALL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _a, _g, _g, _i, _n, _g, _SPACE, _T, _a, _i, _l, _END},
		.itemId = ITEM_LAGGING_TAIL,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_LAGGING_TAIL,
		.holdEffectParam = 0,
		.description = DESC_LAGGING_TAIL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _e, _s, _t, _i, _n, _y, _SPACE, _K, _n, _o, _t, _END},
		.itemId = ITEM_DESTINY_KNOT,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_DESTINY_KNOT,
		.holdEffectParam = 0,
		.description = DESC_DESTINY_KNOT,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _l, _a, _c, _k, _SPACE, _S, _l, _u, _d, _g, _e, _END},
		.itemId = ITEM_BLACK_SLUDGE,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_BLACK_SLUDGE,
		.holdEffectParam = 0,
		.description = DESC_BLACK_SLUDGE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_I, _c, _y, _SPACE, _R, _o, _c, _k, _END},
		.itemId = ITEM_ICY_ROCK,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_ICY_ROCK,
		.holdEffectParam = 0,
		.description = DESC_ICY_ROCK,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _m, _o, _o, _t, _h, _SPACE, _R, _o, _c, _k, _END},
		.itemId = ITEM_SMOOTH_ROCK,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_SMOOTH_ROCK,
		.holdEffectParam = 0,
		.description = DESC_SMOOTH_ROCK,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_H, _e, _a, _t, _SPACE, _R, _o, _c, _k, _END},
		.itemId = ITEM_HEAT_ROCK,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_HEAT_ROCK,
		.holdEffectParam = 0,
		.description = DESC_HEAT_ROCK,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_D, _a, _m, _p, _SPACE, _R, _o, _c, _k, _END},
		.itemId = ITEM_DAMP_ROCK,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_DAMP_ROCK,
		.holdEffectParam = 0,
		.description = DESC_DAMP_ROCK,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_G, _r, _i, _p, _SPACE, _C, _l, _a, _w, _END},
		.itemId = ITEM_GRIP_CLAW,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_GRIP_CLAW,
		.holdEffectParam = 0,
		.description = DESC_GRIP_CLAW,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _h, _o, _i, _c, _e, _SPACE, _S, _c, _a, _r, _f, _END},
		.itemId = ITEM_CHOICE_SCARF,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_CHOICE_BAND,
		.holdEffectParam = 2,
		.description = DESC_CHOICE_SCARF,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _h, _o, _i, _c, _e, _SPACE, _S, _p, _e, _c, _s, _END},
		.itemId = ITEM_CHOICE_SPECS,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_CHOICE_BAND,
		.holdEffectParam = 1,
		.description = DESC_CHOICE_SPECS,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _t, _i, _c, _k, _y, _SPACE, _B, _a, _r, _b, _END},
		.itemId = ITEM_STICKY_BARB,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_STICKY_BARB,
		.holdEffectParam = 0,
		.description = DESC_STICKY_BARB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _h, _e, _d, _SPACE, _S, _h, _e, _l, _l, _END},
		.itemId = ITEM_SHED_SHELL,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_SHED_SHELL,
		.holdEffectParam = 0,
		.description = DESC_SHED_SHELL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _i, _g, _SPACE, _R, _o, _o, _t, _END},
		.itemId = ITEM_BIG_ROOT,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_BIG_ROOT,
		.holdEffectParam = 0,
		.description = DESC_BIG_ROOT,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_E, _v, _i, _o, _l, _i, _t, _e, _END},
		.itemId = ITEM_EVIOLITE,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_EVIOLITE,
		.holdEffectParam = 0,
		.description = DESC_EVIOLITE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_F, _l, _o, _a, _t, _SPACE, _S, _t, _o, _n, _e, _END},
		.itemId = ITEM_FLOAT_STONE,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_FLOAT_STONE,
		.holdEffectParam = 0,
		.description = DESC_FLOAT_STONE,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _o, _c, _k, _y, _SPACE, _H, _e, _l, _m, _e, _t, _END},
		.itemId = ITEM_ROCKY_HELMET,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_ROCKY_HELMET,
		.holdEffectParam = 0,
		.description = DESC_ROCKY_HELMET,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _i, _r, _SPACE, _B, _a, _l, _l, _o, _o, _n, _END},
		.itemId = ITEM_AIR_BALLOON,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_AIR_BALLOON,
		.holdEffectParam = 0,
		.description = DESC_AIR_BALLOON,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _e, _d, _SPACE, _C, _a, _r, _d, _END},
		.itemId = ITEM_RED_CARD,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_RED_CARD,
		.holdEffectParam = 0,
		.description = DESC_RED_CARD,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_R, _i, _n, _g, _SPACE, _T, _a, _r, _g, _e, _t, _END},
		.itemId = ITEM_RING_TARGET,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_RING_TARGET,
		.holdEffectParam = 0,
		.description = DESC_RING_TARGET,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_B, _i, _n, _d, _i, _n, _g, _SPACE, _B, _a, _n, _d, _END},
		.itemId = ITEM_BINDING_BAND,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_BINDING_BAND,
		.holdEffectParam = 0,
		.description = DESC_BINDING_BAND,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _b, _s, _o, _r, _b, _SPACE, _B, _u, _l, _b, _END},
		.itemId = ITEM_ABSORB_BULB,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_ABSORB_BULB,
		.holdEffectParam = 0,
		.description = DESC_ABSORB_BULB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_C, _e, _l, _l, _SPACE, _B, _a, _t, _t, _e, _r, _y, _END},
		.itemId = ITEM_CELL_BATTERY,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_CELL_BATTERY,
		.holdEffectParam = 0,
		.description = DESC_CELL_BATTERY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_E, _j, _e, _c, _t, _SPACE, _B, _u, _t, _t, _o, _n, _END},
		.itemId = ITEM_EJECT_BUTTON,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_EJECT_BUTTON,
		.holdEffectParam = 0,
		.description = DESC_EJECT_BUTTON,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_W, _e, _a, _k, _n, _e, _s, _s, _SPACE, _P, _o, _l, _PERIOD, _END},
		.itemId = ITEM_WEAKNESS_POLICY,
		.price = 3000,
		.holdEffect = ITEM_EFFECT_WEAKNESS_POLICY,
		.holdEffectParam = 0,
		.description = DESC_WEAKNESS_POLICY,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _s, _s, _a, _u, _l, _t, _SPACE, _V, _e, _s, _t, _END},
		.itemId = ITEM_ASSAULT_VEST,
		.price = 4000,
		.holdEffect = ITEM_EFFECT_ASSAULT_VEST,
		.holdEffectParam = 0,
		.description = DESC_ASSAULT_VEST,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_L, _u, _m, _i, _n, _o, _u, _s, _M, _o, _s, _s, _END},
		.itemId = ITEM_LUMINOUS_MOSS,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_LUMINOUS_MOSS,
		.holdEffectParam = 0,
		.description = DESC_LUMINOUS_MOSS,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _n, _o, _w, _b, _a, _l, _l, _END},
		.itemId = ITEM_SNOWBALL,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_SNOWBALL,
		.holdEffectParam = 0,
		.description = DESC_SNOWBALL,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_S, _a, _f, _e, _SPACE, _G, _u, _a, _r, _d, _END},
		.itemId = ITEM_SAFETY_GOGGLES,
		.price = 2000,
		.holdEffect = ITEM_EFFECT_SAFETY_GOGGLES,
		.holdEffectParam = 0,
		.description = DESC_SAFETY_GOGGLES,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
	{
		.name = {_A, _d, _r, _e, _n, _a, _l, _SPACE, _O, _r, _b, _END},
		.itemId = ITEM_ADRENALINE_ORB,
		.price = 1000,
		.holdEffect = ITEM_EFFECT_ADRENALINE_ORB,
		.holdEffectParam = 0,
		.description = DESC_ADRENALINE_ORB,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_ITEMS,
		.type = ITEM_USE_BAG_MENU,
		.fieldUseFunc = FieldUseFunc_OakStopsYou,
		.battleUsage = 0,
		.battleUseFunc = NULL,
		.secondaryId = 0
	},
    {
        .name = {_T, _e, _r, _r, _a, _i, _n, _SPACE, _E, _x, _t, _PERIOD, _END},
        .itemId = ITEM_TERRAIN_EXTENDER,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_TERRAIN_EXTENDER,
        .holdEffectParam = 0,
        .description = DESC_TERRAIN_EXTENDER,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_P, _r, _o, _t, _e, _c, _SPACE, _P, _a, _d, _s, _END},
        .itemId = ITEM_PROTECTIVE_PADS,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_PROTECTIVE_PADS,
        .holdEffectParam = 2,
        .description = DESC_PROTECTIVE_PADS,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_E, _l, _e, _c, _t, _PERIOD, _SPACE, _S, _e, _e, _d, _END},
        .itemId = ITEM_ELECTRIC_SEED,
        .price = 1000,
        .holdEffect = ITEM_EFFECT_SEEDS,
        .holdEffectParam = 1,
        .description = DESC_ELECTRIC_SEED,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_G, _r, _a, _s, _s, _y, _SPACE, _S, _e, _e, _d, _END},
        .itemId = ITEM_GRASSY_SEED,
        .price = 1000,
        .holdEffect = ITEM_EFFECT_SEEDS,
        .holdEffectParam = 2,
        .description = DESC_GRASSY_SEED,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_M, _i, _s, _t, _y, _SPACE, _S, _e, _e, _d, _END},
        .itemId = ITEM_MISTY_SEED,
        .price = 1000,
        .holdEffect = ITEM_EFFECT_SEEDS,
        .holdEffectParam = 3,
        .description = DESC_MISTY_SEED,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_P, _s, _y, _c, _h, _i, _c, _SPACE, _S, _e, _e, _d, _END},
        .itemId = ITEM_PSYCHIC_SEED,
        .price = 1000,
        .holdEffect = ITEM_EFFECT_SEEDS,
        .holdEffectParam = 4,
        .description = DESC_PSYCHIC_SEED,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_E, _j, _e, _c, _t, _SPACE, _P, _a, _c, _k, _END},
        .itemId = ITEM_EJECT_PACK,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_EJECT_PACK,
        .holdEffectParam = 0,
        .description = DESC_EJECT_PACK,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_R, _o, _o, _m, _SPACE, _S, _e, _r, _v, _i, _c, _e, _END},
        .itemId = ITEM_ROOM_SERVICE,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_ROOM_SERVICE,
        .holdEffectParam = 0,
        .description = DESC_ROOM_SERVICE,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_B, _l, _u, _n, _d, _e, _r, _SPACE, _P, _o, _l, _PERIOD, _END},
        .itemId = ITEM_BLUNDER_POLICY,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_BLUNDER_POLICY,
        .holdEffectParam = 0,
        .description = DESC_BLUNDER_POLICY,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_H, _e, _a, _v, _y, _D, _B, _o, _o, _t, _s, _END},
        .itemId = ITEM_HEAVY_DUTY_BOOTS,
        .price = 3000,
        .holdEffect = ITEM_EFFECT_HEAVY_DUTY_BOOTS,
        .holdEffectParam = 0,
        .description = DESC_HEAVY_DUTY_BOOTS,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_U, _t, _PERIOD, _SPACE, _U, _m, _b, _r, _e, _l, _l, _a, _END},
        .itemId = ITEM_UTILITY_UMBRELLA,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_UTILITY_UMBRELLA,
        .holdEffectParam = 0,
        .description = DESC_UTILITY_UMBRELLA,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_T, _h, _r, _o, _a, _t, _SPACE, _S, _p, _r, _a, _y, _END},
        .itemId = ITEM_THROAT_SPRAY,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_THROAT_SPRAY,
        .holdEffectParam = 0,
        .description = DESC_THROAT_SPRAY,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_A, _HYPHEN, _P, _o, _t, _i, _o, _n, _END},
        .itemId = ITEM_ABILITY_CAPSULE,
        .price = 50000,
        .holdEffect = ITEM_EFFECT_ABILITY_CAPSULE,
        .holdEffectParam = 0,
        .description = DESC_ABILITY_CAPSULE,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_AbilityCapsule,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	{
        .name = {_A, _HYPHEN, _P, _a, _t, _c, _h, _END},
        .itemId = ITEM_ABILITY_PATCH,
        .price = 60000,
        .holdEffect = ITEM_EFFECT_ABILITY_CAPSULE,
        .holdEffectParam = 1,
        .description = DESC_ABILITY_CAPSULE,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_AbilityCapsule,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	    {
        .name = {_A, _u, _s, _p, _i, _c, _i, _o, _u, _s, _HYPHEN, _A, _END},
        .itemId = ITEM_AUSPICIOUS_ARMOR,
        .price = 3000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_AUSPICIOUS_ARMOR,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_EvoItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_M, _a, _l, _i, _c, _i, _o, _u, _s, _HYPHEN, _A, _END},
        .itemId = ITEM_MALICIOUS_ARMOR,
        .price = 3000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_MALICIOUS_ARMOR,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_EvoItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_B, _l, _k, _SPACE, _A, _u, _g, _u, _r, _i, _t, _e, _END},
        .itemId = ITEM_BLACK_AUGURITE,
        .price = 3000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_BLACK_AUGURITE,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_EvoItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_M, _e, _t, _a, _l, _SPACE, _A, _l, _l, _o, _y, _END},
        .itemId = ITEM_METAL_ALLOY,
        .price = 3000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_METAL_ALLOY,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_EvoItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_P, _e, _a, _t, _SPACE, _B, _l, _o, _c, _k, _END},
        .itemId = ITEM_PEAT_BLOCK,
        .price = 3000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_PEAT_BLOCK,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_EvoItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_L, _e, _a, _d, _e, _r, _s, _SPACE, _C, _r, _e, _s, _t, _END},
        .itemId = ITEM_LEADERS_CREST,
        .price = 3000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_LEADERS_CREST,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_EvoItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_G, _i, _m, _m, _i, _SPACE, _C, _o, _i, _n, _END},
        .itemId = ITEM_GIMMIGHOUL_COIN,
        .price = 1000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_GIMMIGHOUL_COIN,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_EvoItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_S, _y, _r, _u, _p, _y, _SPACE, _A, _p, _p, _l, _e, _END},
        .itemId = ITEM_SYRUPY_APPLE,
        .price = 3000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_SYRUPY_APPLE,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_EvoItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_C, _l, _e, _a, _r, _SPACE, _A, _m, _u, _l, _e, _t, _END},
        .itemId = ITEM_CLEAR_AMULET,
        .price = 3000,
        .holdEffect = ITEM_EFFECT_CLEAR_AMULET,
        .holdEffectParam = 0,
        .description = DESC_CLEAR_AMULET,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_C, _o, _v, _e, _r, _t, _SPACE, _C, _l, _o, _a, _k, _END},
        .itemId = ITEM_COVERT_CLOAK,
        .price = 3000,
        .holdEffect = ITEM_EFFECT_COVERT_CLOAK,
        .holdEffectParam = 0,
        .description = DESC_COVERT_CLOAK,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_L, _o, _a, _d, _e, _d, _SPACE, _D, _i, _c, _e, _END},
        .itemId = ITEM_LOADED_DICE,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_LOADED_DICE,
        .holdEffectParam = 0,
        .description = DESC_LOADED_DICE,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_P, _u, _n, _c, _h, _SPACE, _G, _l, _o, _v, _e, _END},
        .itemId = ITEM_PUNCHING_GLOVE,
        .price = 2000,
        .holdEffect = ITEM_EFFECT_PUNCHING_GLOVE,
        .holdEffectParam = 0,
        .description = DESC_PUNCHING_GLOVE,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_R, _e, _i, _n, _s, _SPACE, _U, _n, _i, _t, _y, _END},
        .itemId = ITEM_REINS_OF_UNITY,
        .price = 5000,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = DESC_REINS_OF_UNITY,
        .importance = 1,
        .unk19 = 0,
        .pocket = POCKET_KEY_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_FormChangeItem,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_B, _o, _o, _s, _t, _SPACE, _E, _n, _e, _r, _g, _y, _END},
        .itemId = ITEM_BOOSTER_ENERGY,
        .price = 3000,
        .holdEffect = ITEM_EFFECT_BOOSTER_ENERGY,
        .holdEffectParam = 0,
        .description = DESC_BOOSTER_ENERGY,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_W, _e, _l, _l, _PERIOD, _SPACE, _M, _a, _s, _k, _END},
        .itemId = ITEM_WELLSPRING_MASK,
        .price = 15000,
        .holdEffect = ITEM_EFFECT_MASKS,
        .holdEffectParam = 0,
        .description = DESC_WELLSPRING_MASK,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_H, _e, _a, _r, _t, _PERIOD, _SPACE, _M, _a, _s, _k, _END},
        .itemId = ITEM_HEARTHFLAME_MASK,
        .price = 15000,
        .holdEffect = ITEM_EFFECT_MASKS,
        .holdEffectParam = 0,
        .description = DESC_HEARTHFLAME_MASK,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
    {
        .name = {_C, _o, _r, _n, _e, _r, _PERIOD, _SPACE, _M, _a, _s, _k, _END},
        .itemId = ITEM_CORNERSTONE_MASK,
        .price = 15000,
        .holdEffect = ITEM_EFFECT_MASKS,
        .holdEffectParam = 0,
        .description = DESC_CORNERSTONE_MASK,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_LONELY_MINT]
    {
        .name = {_L, _o, _n, _e, _l, _y, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_LONELY_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 0,
        .description = DESC_LONELY_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_ADAMANT_MINT]
    {
        .name = {_A, _d, _a, _m, _a, _n, _t, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_ADAMANT_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 1,
        .description = DESC_ADAMANT_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_NAUGHTY_MINT]
    {
        .name = {_N, _a, _u, _g, _h, _t, _y, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_NAUGHTY_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 2,
        .description = DESC_NAUGHTY_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_BRAVE_MINT]
    {
        .name = {_B, _r, _a, _v, _e, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_BRAVE_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 3,
        .description = DESC_BRAVE_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_BOLD_MINT]
    {
        .name = {_B, _o, _l, _d, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_BOLD_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 4,
        .description = DESC_BOLD_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_IMPISH_MINT]
    {
        .name = {_I, _m, _p, _i, _s, _h, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_IMPISH_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 5,
        .description = DESC_IMPISH_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_LAX_MINT]
    {
        .name = {_L, _a, _x, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_LAX_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 6,
        .description = DESC_LAX_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_RELAXED_MINT]
    {
        .name = {_R, _e, _l, _a, _x, _e, _d, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_RELAXED_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 7,
        .description = DESC_RELAXED_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_MODEST_MINT]
    {
        .name = {_M, _o, _d, _e, _s, _t, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_MODEST_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 8,
        .description = DESC_MODEST_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_MILD_MINT]
    {
        .name = {_M, _i, _l, _d, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_MILD_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 9,
        .description = DESC_MILD_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_RASH_MINT]
    {
        .name = {_R, _a, _s, _h, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_RASH_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 10,
        .description = DESC_RASH_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_QUIET_MINT]
    {
        .name = {_Q, _u, _i, _e, _t, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_QUIET_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 11,
        .description = DESC_QUIET_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_CALM_MINT]
    {
        .name = {_C, _a, _l, _m, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_CALM_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 12,
        .description = DESC_CALM_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_GENTLE_MINT]
    {
        .name = {_G, _e, _n, _t, _l, _e, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_GENTLE_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 13,
        .description = DESC_GENTLE_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_CAREFUL_MINT]
    {
        .name = {_C, _a, _r, _e, _f, _u, _l, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_CAREFUL_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 14,
        .description = DESC_CAREFUL_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_SASSY_MINT]
    {
        .name = {_S, _a, _s, _s, _y, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_SASSY_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 15,
        .description = DESC_SASSY_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_TIMID_MINT]
    {
        .name = {_T, _i, _m, _i, _d, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_TIMID_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 16,
        .description = DESC_TIMID_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_HASTY_MINT]
    {
        .name = {_H, _a, _s, _t, _y, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_HASTY_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 17,
        .description = DESC_HASTY_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_JOLLY_MINT]
    {
        .name = {_J, _o, _l, _l, _y, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_JOLLY_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 18,
        .description = DESC_JOLLY_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_NAIVE_MINT]
    {
        .name = {_N, _a, _i, _v, _e, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_NAIVE_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 19,
        .description = DESC_NAIVE_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_SERIOUS_MINT]
    {
        .name = {_S, _e, _r, _i, _o, _u, _s, _SPACE, _M, _i, _n, _t, _END},
        .itemId = ITEM_SERIOUS_MINT,
        .price = 20000,
        .holdEffect = ITEM_EFFECT_NATURE_MINT,
        .holdEffectParam = 20,
        .description = DESC_SERIOUS_MINT,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_PARTY_MENU,
        .fieldUseFunc = FieldUseFunc_NatureMint,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	[ITEM_POKEVIAL]
	{
		.name = {_P, _o, _k, _e, _v, _i, _a, _l, _END},
		.itemId = ITEM_POKEVIAL,  // No standard effect
		.price = 0,  // Key Items have no price
		.holdEffect = 0,  // Not a held item
		.holdEffectParam = 0,
		.description = gItemDescription_PokeVial,
		.importance = 0,
		.unk19 = 0,
		.pocket = POCKET_KEY_ITEMS,  // Make it a key item
		.type = ITEM_USE_FIELD,  // Only usable in the overworld
		.fieldUseFunc = ItemUseOutOfBattle_PokeVial,  // Define field behavior
		.battleUsage = 0,  // Cannot be used in battle
		.battleUseFunc = NULL,
		.secondaryId = 0,
	},
	[ITEM_EVIV_DISPLAYER]
 	{
 		.name = {_E, _V, _HYPHEN, _I, _V, _SPACE, _V, _i, _e, _w, _e, _r, _END},
 		.itemId = ITEM_EVIV_DISPLAYER,  // No standard effect
 		.price = 0,  // Key Items have no price
 		.holdEffect = 0,  // Not a held item
 		.holdEffectParam = 0,
 		.description = DESC_EVIV_DISPLAYER,
 		.importance = 0,
 		.unk19 = 0,
 		.pocket = POCKET_KEY_ITEMS,  // Make it a key item
 		.type = ITEM_USE_FIELD,  // Only usable in the overworld
 		.fieldUseFunc = FieldUseFunc_EVIV,  // Define field behavior
 		.battleUsage = 0,  // Cannot be used in battle
 		.battleUseFunc = NULL,
 		.secondaryId = 0,
 	},
	{
        .name = {_F, _r, _e, _e, _SPACE, _S, _p, _a, _c, _e, _SPACE, _1, _END},
        .itemId = ITEM_FREE_SPACE1,
        .price = 0,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = gText_ItemNone,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	{
        .name = {_F, _r, _e, _e, _SPACE, _S, _p, _a, _c, _e, _SPACE, _2, _END},
        .itemId = ITEM_FREE_SPACE1,
        .price = 0,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = gText_ItemNone,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
	{
        .name = {_F, _r, _e, _e, _SPACE, _S, _p, _a, _c, _e, _SPACE, _3, _END},
        .itemId = ITEM_FREE_SPACE1,
        .price = 0,
        .holdEffect = 0,
        .holdEffectParam = 0,
        .description = gText_ItemNone,
        .importance = 0,
        .unk19 = 0,
        .pocket = POCKET_ITEMS,
        .type = ITEM_USE_BAG_MENU,
        .fieldUseFunc = FieldUseFunc_OakStopsYou,
        .battleUsage = 0,
        .battleUseFunc = NULL,
        .secondaryId = 0
    },
};
#endif
