.align 2
.thumb

.include "../xse_commands.s"
.include "../xse_defines.s"

.global EventScript_MoveRelearner

@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
@ Move Relearner NPC Script
@ 
@ FIXED VERSION - <PERSON>perly opens Move Relearner interface
@ 
@ CRITICAL FIXES APPLIED:
@ 1. Use gotonative CB2_InitLearnMove instead of callnative Move_Relearner
@ 2. Close message box before interface transition with closemessage
@ 3. Script execution stops after gotonative - no continuation conflicts
@ 4. Prevents catching tutorial from being triggered
@
@ USAGE: 
@ 1. Add to eventscripts: npc [mapgroup] [mapnum] [npcid] EventScript_MoveRelearner
@ 2. Compile with: python scripts/make.py
@ 3. Test in-game
@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@

EventScript_MoveRelearner:
    lock
    faceplayer
    msgbox gText_MoveRelearner_Welcome MSG_NORMAL
    
    @ Check if player has Pokemon - FIXED: Use correct command
    countpokemon @ This is the correct command for checking party count
    compare LASTRESULT 0
    if equal _goto EventScript_MoveRelearner_NoMons
    
    @ Ask if player wants to use Move Relearner
    msgbox gText_MoveRelearner_AskUse MSG_YESNO
    compare LASTRESULT NO
    if equal _goto EventScript_MoveRelearner_Decline

    @ Inform about the payment before checking
    msgbox gText_MoveRelearner_PaymentInfo MSG_NORMAL

    @ Check if player has enough money (1000 Pokédollars)
    checkmoney 1000 0
    compare LASTRESULT FALSE
    if equal _goto EventScript_MoveRelearner_NoMoney
    
EventScript_MoveRelearner_SelectPokemon:
    @ FIXED: Use the EXACT sequence from original FireRed Move Maniac
    @ Ask which Pokemon needs tutoring (like original script)
    msgbox gText_MoveRelearner_WhichPokemon MSG_KEEPOPEN

    @ Use special 0xDB to open party selection (ORIGINAL FIRERED SEQUENCE!)
    special 0xDB
    waitstate

    @ Check if player cancelled (exactly like original)
    compare 0x8004 0x6
    if greaterorequal _goto EventScript_MoveRelearner_Decline

    @ Use special 0x148 to check if Pokemon can learn moves (like original)
    special 0x148
    compare LASTRESULT 0x1
    if equal _goto EventScript_MoveRelearner_NoMovesToLearn

    @ Check if Pokemon has moves to relearn
    compare 0x8005 0x0
    if equal _goto EventScript_MoveRelearner_NoMovesToLearn

    @ Ask which move to teach (like original)
    msgbox gText_MoveRelearner_WhichMove MSG_KEEPOPEN

    @ Use special 0xE0 to open move list (ORIGINAL FIRERED SEQUENCE!)
    special 0xE0
    waitstate

    @ Check if player cancelled move selection
    compare 0x8004 0x0
    if equal _goto EventScript_MoveRelearner_Decline

    @ Charge the money (1000 Pokédollars)
    removemoney 1000 0

    @ Success message
    msgbox gText_MoveRelearner_Success MSG_NORMAL

    @ Ask if player wants to teach another move (Quality of Life improvement)
    msgbox gText_MoveRelearner_AskAnother MSG_YESNO
    compare LASTRESULT YES
    if equal _goto EventScript_MoveRelearner_Continue

    @ Player chose No - end the service
    msgbox gText_MoveRelearner_Goodbye MSG_NORMAL
    release
    end

EventScript_MoveRelearner_Continue:
    @ Player wants to continue - check money again and loop back
    checkmoney 1000 0
    compare LASTRESULT FALSE
    if equal _goto EventScript_MoveRelearner_NoMoney

    @ Has money - go back to Pokemon selection
    goto EventScript_MoveRelearner_SelectPokemon

EventScript_MoveRelearner_NoMovesToLearn:
    msgbox gText_MoveRelearner_NoMovesToLearn MSG_NORMAL

    @ Ask if player wants to try with another Pokemon
    msgbox gText_MoveRelearner_TryAnother MSG_YESNO
    compare LASTRESULT YES
    if equal _goto EventScript_MoveRelearner_SelectPokemon

    @ Player chose No - end the service
    msgbox gText_MoveRelearner_Goodbye MSG_NORMAL
    release
    end

EventScript_MoveRelearner_NoMons:
    msgbox gText_MoveRelearner_NoMons MSG_NORMAL
    release
    end

EventScript_MoveRelearner_NoMoney:
    msgbox gText_MoveRelearner_NoMoney MSG_NORMAL
    release
    end

EventScript_MoveRelearner_Decline:
    msgbox gText_MoveRelearner_Decline MSG_NORMAL
    release
    end

EventScript_MoveRelearner_NoHeartScale:
    msgbox gText_MoveRelearner_NoHeartScale MSG_NORMAL
    release
    end

@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
@ USAGE EXAMPLES:
@
@ Add one of these lines to your eventscripts file:
@
@ # Two Island Move Relearner
@ npc 23 0 1 EventScript_MoveRelearner
@
@ # Saffron City Pokemon Center Move Relearner  
@ npc 5 5 3 EventScript_MoveRelearner
@
@ # Celadon City Pokemon Center Move Relearner
@ npc 6 5 2 EventScript_MoveRelearner
@
@ # Fuchsia City Pokemon Center Move Relearner
@ npc 7 5 2 EventScript_MoveRelearner
@
@ # Four Island Move Relearner
@ npc 25 0 2 EventScript_MoveRelearner
@
@ # Indigo Plateau Move Relearner
@ npc 9 0 3 EventScript_MoveRelearner
@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
