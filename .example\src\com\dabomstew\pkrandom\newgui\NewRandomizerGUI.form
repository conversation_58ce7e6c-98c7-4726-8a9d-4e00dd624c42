<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="com.dabomstew.pkrandom.newgui.NewRandomizerGUI">
  <grid id="27dc6" binding="mainPanel" layout-manager="GridBagLayout">
    <constraints>
      <xy x="7" y="20" width="1350" height="1103"/>
    </constraints>
    <properties/>
    <border type="none">
      <font/>
    </border>
    <children>
      <grid id="73235" layout-manager="GridBagLayout">
        <constraints>
          <grid row="1" column="2" row-span="3" col-span="1" vsize-policy="3" hsize-policy="3" anchor="9" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <enabled value="true"/>
        </properties>
        <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.generalOptionsPanel.title">
          <font style="1"/>
          <title-color color="-16777216"/>
          <color color="-4473925"/>
        </border>
        <children>
          <component id="f2100" class="javax.swing.JCheckBox" binding="raceModeCheckBox" default-binding="true">
            <constraints>
              <grid row="2" column="0" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag top="0" left="0" bottom="0" right="30" weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <enabled value="false"/>
              <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.raceModeCheckBox.text"/>
              <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.raceModeCheckBox.toolTipText"/>
            </properties>
          </component>
          <component id="d9ef2" class="javax.swing.JCheckBox" binding="limitPokemonCheckBox">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag top="5" left="0" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <enabled value="false"/>
              <text value=""/>
              <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.limitPokemonCheckBox.toolTipText"/>
            </properties>
          </component>
          <component id="cab02" class="javax.swing.JButton" binding="limitPokemonButton" default-binding="true">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
              <gridbag top="5" left="0" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <enabled value="false"/>
              <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.limitPokemonCheckBox.text"/>
            </properties>
          </component>
          <component id="41e69" class="javax.swing.JCheckBox" binding="noIrregularAltFormesCheckBox" default-binding="true">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <enabled value="false"/>
              <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.noIrregularAltFormesCheckBox.text"/>
              <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.noIrregularAltFormesCheckBox.toolTipText"/>
            </properties>
          </component>
        </children>
      </grid>
      <vspacer id="7ea6">
        <constraints>
          <grid row="7" column="2" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="8" fill="2" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </vspacer>
      <grid id="b24ed" layout-manager="GridBagLayout">
        <constraints>
          <grid row="1" column="4" row-span="3" col-span="2" vsize-policy="3" hsize-policy="3" anchor="1" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.1" weighty="0.0"/>
        </constraints>
        <properties/>
        <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.romInformationPanel.title">
          <font style="1"/>
        </border>
        <children>
          <hspacer id="de227">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
          </hspacer>
          <component id="de3d7" class="javax.swing.JLabel" binding="romNameLabel">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="9" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag top="5" left="5" bottom="5" right="0" weightx="0.1" weighty="0.1"/>
            </constraints>
            <properties>
              <horizontalAlignment value="0"/>
              <labelFor value=""/>
              <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.noRomLoaded"/>
            </properties>
          </component>
          <component id="41f42" class="javax.swing.JLabel" binding="romCodeLabel">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag top="5" left="5" bottom="5" right="0" weightx="0.0" weighty="0.1"/>
            </constraints>
            <properties>
              <labelFor value=""/>
              <text value=""/>
            </properties>
          </component>
          <component id="4bcae" class="javax.swing.JLabel" binding="romSupportLabel">
            <constraints>
              <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
              <gridbag top="5" left="5" bottom="5" right="0" weightx="0.0" weighty="0.1"/>
            </constraints>
            <properties>
              <text value=""/>
            </properties>
          </component>
        </children>
      </grid>
      <tabbedpane id="d57f7" binding="tabbedPane1" default-binding="true">
        <constraints>
          <grid row="8" column="1" row-span="1" col-span="11" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false">
            <preferred-size width="200" height="200"/>
          </grid>
          <gridbag weightx="0.5" weighty="0.1"/>
        </constraints>
        <properties>
          <tabLayoutPolicy value="1"/>
          <tabPlacement value="1"/>
        </properties>
        <border type="none"/>
        <children>
          <grid id="54bda" layout-manager="GridBagLayout">
            <constraints>
              <tabbedpane title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.pokemonTraitsPanel.title"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <grid id="fc2b0" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="2" vsize-policy="3" hsize-policy="3" anchor="1" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag top="5" left="0" bottom="0" right="0" weightx="0.1" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.pbsPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="576dd" class="javax.swing.JRadioButton" binding="pbsUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="f556c">
                    <constraints>
                      <grid row="1" column="6" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <component id="e8460" class="javax.swing.JRadioButton" binding="pbsShuffleRadioButton" default-binding="true">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsShuffleRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsShuffleRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="8625b" class="javax.swing.JRadioButton" binding="pbsLegendariesSlowRadioButton">
                    <constraints>
                      <grid row="1" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsLegendariesSlowRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsLegendariesSlowRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="9be58" class="javax.swing.JRadioButton" binding="pbsStrongLegendariesSlowRadioButton">
                    <constraints>
                      <grid row="2" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsStrongLegendariesSlowRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsStrongLegendariesSlowRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="db430" class="javax.swing.JCheckBox" binding="pbsStandardizeEXPCurvesCheckBox">
                    <constraints>
                      <grid row="1" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsStandardizeEXPCurvesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsStandardizeEXPCurvesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="986c1" class="javax.swing.JCheckBox" binding="pbsFollowEvolutionsCheckBox" default-binding="true">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsFollowEvolutionsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsFollowEvolutionsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="fb58a">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="14884">
                    <constraints>
                      <grid row="5" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <vspacer id="19dac">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="5b04a" class="javax.swing.JCheckBox" binding="pbsUpdateBaseStatsCheckBox">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsUpdateBaseStatsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsUpdateBaseStatsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="6768d" class="javax.swing.JCheckBox" binding="pbsFollowMegaEvosCheckBox">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsFollowMegaEvosCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsFollowMegaEvosCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="4d643" class="javax.swing.JComboBox" binding="pbsUpdateComboBox">
                    <constraints>
                      <grid row="4" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="5" bottom="0" right="0" weightx="0.6" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <model>
                        <item value="--"/>
                      </model>
                    </properties>
                  </component>
                  <component id="7eb1a" class="javax.swing.JComboBox" binding="pbsEXPCurveComboBox">
                    <constraints>
                      <grid row="2" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="20" bottom="0" right="5" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <model>
                        <item value="Medium Fast"/>
                      </model>
                    </properties>
                  </component>
                  <component id="97567" class="javax.swing.JCheckBox" binding="pbsAssignEvoStatsRandomlyCheckBox">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsAssignEvoStatsRandomlyCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsAssignEvoStatsRandomlyCheckBox.tooltipText"/>
                    </properties>
                  </component>
                  <component id="4535b" class="javax.swing.JRadioButton" binding="pbsRandomRadioButton" default-binding="true">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsRandomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsRandomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="213c9" class="javax.swing.JRadioButton" binding="pbsAllMediumFastRadioButton">
                    <constraints>
                      <grid row="3" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsAllMediumFastRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pbsAllMediumFastRadioButton.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <hspacer id="17e6a">
                <constraints>
                  <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="ae365">
                <constraints>
                  <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.1"/>
                </constraints>
              </vspacer>
              <hspacer id="f48be">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <grid id="54ad9" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.ptPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="fb38a" class="javax.swing.JRadioButton" binding="ptUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <hideActionText value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="ab767">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="8f6b1">
                    <constraints>
                      <grid row="5" column="2" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="bdcac">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="ff766">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="f7a27" class="javax.swing.JRadioButton" binding="ptRandomFollowEvolutionsRadioButton" default-binding="true">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptRandomFollowEvolutionsRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptRandomFollowEvolutionsRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="45dd7" class="javax.swing.JRadioButton" binding="ptRandomCompletelyRadioButton" default-binding="true">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptRandomCompletelyRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptRandomCompletelyRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="360ba" class="javax.swing.JCheckBox" binding="ptFollowMegaEvosCheckBox">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptFollowMegaEvosCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptFollowMegaEvosCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="237b6" class="javax.swing.JCheckBox" binding="ptIsDualTypeCheckBox">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <selected value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptForceDualTypeCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.ptForceDualTypeCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <grid id="f8946" binding="pokemonAbilitiesPanel" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.9" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.paPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="fa40d" class="javax.swing.JRadioButton" binding="paUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="559d2">
                    <constraints>
                      <grid row="1" column="6" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="2747c">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="4" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.1"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="d7eda">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="bc08c">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="5bd6c" class="javax.swing.JRadioButton" binding="paRandomRadioButton">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paRandomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paRandomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="97cb8" class="javax.swing.JCheckBox" binding="paAllowWonderGuardCheckBox">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="5" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paAllowWonderGuardCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paAllowWonderGuardCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="18744" class="javax.swing.JCheckBox" binding="paFollowEvolutionsCheckBox" default-binding="true">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="5" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paFollowEvolutionsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paFollowEvolutionsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="5694" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="4" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="5" left="0" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paBanLabel.text"/>
                    </properties>
                  </component>
                  <component id="5afb0" class="javax.swing.JCheckBox" binding="paTrappingAbilitiesCheckBox">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="5" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paTrappingAbilitiesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paTrappingAbilitiesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="76ea8" class="javax.swing.JCheckBox" binding="paNegativeAbilitiesCheckBox">
                    <constraints>
                      <grid row="3" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <selected value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paNegativeAbilitiesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paNegativeAbilitiesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="4ac30" class="javax.swing.JCheckBox" binding="paBadAbilitiesCheckBox">
                    <constraints>
                      <grid row="3" column="4" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paBadAbilitiesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paBadAbilitiesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="652d7" class="javax.swing.JCheckBox" binding="paWeighDuplicatesTogetherCheckBox">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paWeighDuplicatesTogetherCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paWeighDuplicatesTogetherCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="bae4a" class="javax.swing.JCheckBox" binding="paEnsureTwoAbilitiesCheckbox">
                    <constraints>
                      <grid row="1" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paEnsureTwoAbilitiesCheckbox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paEnsureTwoAbilitiesCheckbox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="b3439" class="javax.swing.JCheckBox" binding="paFollowMegaEvosCheckBox">
                    <constraints>
                      <grid row="2" column="3" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paFollowMegaEvosCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.paFollowMegaEvosCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <grid id="b4a80" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="3" column="1" row-span="1" col-span="2" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.pePanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="9dc25" class="javax.swing.JRadioButton" binding="peUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="25cc7">
                    <constraints>
                      <grid row="1" column="4" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="5ed0e">
                    <constraints>
                      <grid row="6" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="cbc8a">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="15fb8">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="577d" class="javax.swing.JRadioButton" binding="peRandomRadioButton">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peRandomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peRandomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="b5878" class="javax.swing.JCheckBox" binding="peSimilarStrengthCheckBox">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.9" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peSimilarStrengthCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peSimilarStrengthCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="4eb8e" class="javax.swing.JCheckBox" binding="peSameTypingCheckBox">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peSameTypingCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peSameTypingCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="6e101" class="javax.swing.JCheckBox" binding="peLimitEvolutionsToThreeCheckBox">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peLimitEvolutionsToThreeCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peLimitEvolutionsToThreeCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="52db5" class="javax.swing.JCheckBox" binding="peForceChangeCheckBox">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peForceChangeCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peForceChangeCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="9fe2f" class="javax.swing.JCheckBox" binding="peChangeImpossibleEvosCheckBox">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peChangeImpossibleEvosCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peChangeImpossibleEvosCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="4ad81" class="javax.swing.JCheckBox" binding="peMakeEvolutionsEasierCheckBox">
                    <constraints>
                      <grid row="2" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peMakeEvolutionsEasierCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peMakeEvolutionsEasierCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="81bd3" class="javax.swing.JCheckBox" binding="peAllowAltFormesCheckBox">
                    <constraints>
                      <grid row="5" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peAllowAltFormesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peAllowAltFormesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="b091c" class="javax.swing.JCheckBox" binding="peRemoveTimeBasedEvolutionsCheckBox">
                    <constraints>
                      <grid row="3" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peRemoveTimeBasedEvolutions.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peRemoveTimeBasedEvolutions.toolTipText"/>
                    </properties>
                  </component>
                  <component id="7fc31" class="javax.swing.JRadioButton" binding="peRandomEveryLevelRadioButton">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peRandomEveryLevelRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.peRandomEveryLevelRadioButton.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <vspacer id="44843">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
            </children>
          </grid>
          <grid id="313b7" layout-manager="GridBagLayout">
            <constraints>
              <tabbedpane title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.startersStaticsTradesPanel"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <grid id="bb7c7" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.1" weighty="0.1"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.spPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="124c" class="javax.swing.JRadioButton" binding="spUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="8b022">
                    <constraints>
                      <grid row="1" column="6" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="499d4">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="2" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="1b3c2">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="8a064">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="94a4e" class="javax.swing.JRadioButton" binding="spCustomRadioButton">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spCustomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spCustomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="c1c80" class="javax.swing.JRadioButton" binding="spRandomCompletelyRadioButton" default-binding="true">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spRandomCompletelyRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spRandomCompletelyRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="12184" class="javax.swing.JRadioButton" binding="spRandomTwoEvosRadioButton">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="4" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spRandomTwoEvosRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spRandomTwoEvosRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="83a64" class="javax.swing.JComboBox" binding="spComboBox1">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <model>
                        <item value="--"/>
                      </model>
                    </properties>
                  </component>
                  <component id="36c27" class="javax.swing.JComboBox" binding="spComboBox2">
                    <constraints>
                      <grid row="2" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="5" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <model>
                        <item value="--"/>
                      </model>
                    </properties>
                  </component>
                  <component id="3169c" class="javax.swing.JComboBox" binding="spComboBox3">
                    <constraints>
                      <grid row="2" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="5" bottom="0" right="5" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <model>
                        <item value="--"/>
                      </model>
                    </properties>
                  </component>
                  <component id="9ddd3" class="javax.swing.JCheckBox" binding="spRandomizeStarterHeldItemsCheckBox">
                    <constraints>
                      <grid row="2" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spRandomizeStarterHeldItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spRandomizeStarterHeldItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="a1ea2" class="javax.swing.JCheckBox" binding="spBanBadItemsCheckBox" default-binding="true">
                    <constraints>
                      <grid row="3" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spBanBadItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spBanBadItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="cad77" class="javax.swing.JCheckBox" binding="spAllowAltFormesCheckBox">
                    <constraints>
                      <grid row="4" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spAllowAltFormesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.spAllowAltFormesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <hspacer id="48447">
                <constraints>
                  <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="2cb29">
                <constraints>
                  <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
              <hspacer id="78e2">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="797e9">
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
              <grid id="cb778" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.1"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.stpPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="a28a1" class="javax.swing.JRadioButton" binding="stpUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="c4003">
                    <constraints>
                      <grid row="1" column="5" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="6.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="ec4fb">
                    <constraints>
                      <grid row="6" column="1" row-span="1" col-span="4" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="a5bb0">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="9076e">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="66089" class="javax.swing.JRadioButton" binding="stpSwapLegendariesSwapStandardsRadioButton">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpSwapLegendariesSwapStandardsRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpSwapLegendariesSwapStandardsRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="17456" class="javax.swing.JRadioButton" binding="stpRandomCompletelyRadioButton" default-binding="true">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpRandomCompletelyRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpRandomCompletelyRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="282a5" class="javax.swing.JRadioButton" binding="stpRandomSimilarStrengthRadioButton">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpRandomSimilarStrengthRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpRandomSimilarStrengthRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="c441c" class="javax.swing.JCheckBox" binding="stpSwapMegaEvosCheckBox">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpSwapMegaEvosCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpSwapMegaEvosCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="84b86" class="javax.swing.JCheckBox" binding="stpPercentageLevelModifierCheckBox">
                    <constraints>
                      <grid row="1" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="6.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpPercentageLevelModifierCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpPercentageLevelModifierCheckBox.tooltipText"/>
                    </properties>
                  </component>
                  <component id="f3518" class="javax.swing.JSlider" binding="stpPercentageLevelModifierSlider">
                    <constraints>
                      <grid row="2" column="4" row-span="3" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <majorTickSpacing value="10"/>
                      <maximum value="50"/>
                      <minimum value="-50"/>
                      <minorTickSpacing value="2"/>
                      <paintLabels value="true"/>
                      <paintTicks value="true"/>
                      <snapToTicks value="true"/>
                      <value value="0"/>
                    </properties>
                  </component>
                  <component id="ae826" class="javax.swing.JCheckBox" binding="stpRandomize600BSTCheckBox">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.8" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpRandomize600BSTCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpRandomize600BSTCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="69057" class="javax.swing.JCheckBox" binding="stpAllowAltFormesCheckBox">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpAllowAltFormesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpAllowAltFormesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="cde9e" class="javax.swing.JCheckBox" binding="stpLimitMainGameLegendariesCheckBox">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="1.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpLimitMainGameLegendariesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpLimitMainGameLegendariesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="4f53f" class="javax.swing.JCheckBox" binding="stpFixMusicCheckBox">
                    <constraints>
                      <grid row="5" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpFixMusicAllCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.stpFixMusicAllCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <grid id="f2cab" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.1"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.igtPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="4e02b" class="javax.swing.JRadioButton" binding="igtUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="40623">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="d7f82">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="2" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="a626c" class="javax.swing.JRadioButton" binding="igtRandomizeGivenPokemonOnlyRadioButton">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeGivenPokemonOnlyRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeGivenPokemonOnlyRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="f6c7b" class="javax.swing.JRadioButton" binding="igtRandomizeBothRequestedGivenRadioButton">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeBothRequestedGivenRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeBothRequestedGivenRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="963c8">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="9b889">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="5eee5" class="javax.swing.JCheckBox" binding="igtRandomizeNicknamesCheckBox">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.8" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeNicknamesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeNicknamesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="719e9" class="javax.swing.JCheckBox" binding="igtRandomizeOTsCheckBox">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeOTsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeOTsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="e1478" class="javax.swing.JCheckBox" binding="igtRandomizeIVsCheckBox">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeIVsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeIVsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="2d4fb" class="javax.swing.JCheckBox" binding="igtRandomizeItemsCheckBox">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.igtRandomizeItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
          <grid id="f599e" layout-manager="GridBagLayout">
            <constraints>
              <tabbedpane title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.movesMovesetsPanel"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <grid id="d5afa" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.1" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.mdPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="9cf00" class="javax.swing.JCheckBox" binding="mdRandomizeMovePowerCheckBox">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMovePowerCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMovePowerCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="bd712">
                    <constraints>
                      <grid row="1" column="4" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="ecc10">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="8e64">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="b12e4">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="658bb" class="javax.swing.JCheckBox" binding="mdRandomizeMoveAccuracyCheckBox">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMoveAccuracyCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMoveAccuracyCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="5af2e" class="javax.swing.JCheckBox" binding="mdRandomizeMovePPCheckBox">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMovePPCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMovePPCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="e9417" class="javax.swing.JCheckBox" binding="mdRandomizeMoveTypesCheckBox">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMoveTypesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMoveTypesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="7dbd0" class="javax.swing.JCheckBox" binding="mdRandomizeMoveCategoryCheckBox">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.6" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMoveCategoryCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdRandomizeMoveCategoryCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="5eccc" class="javax.swing.JCheckBox" binding="mdUpdateMovesCheckBox">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdUpdateMovesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mdUpdateMovesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="19338" class="javax.swing.JComboBox" binding="mdUpdateComboBox">
                    <constraints>
                      <grid row="4" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="5" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <model>
                        <item value="--"/>
                      </model>
                    </properties>
                  </component>
                </children>
              </grid>
              <hspacer id="636b3">
                <constraints>
                  <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="33947">
                <constraints>
                  <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.1"/>
                </constraints>
              </vspacer>
              <hspacer id="bfe59">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="51e99">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
              <grid id="6490f" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.pmsPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="b296a" class="javax.swing.JRadioButton" binding="pmsUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="5551e">
                    <constraints>
                      <grid row="1" column="4" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="98120">
                    <constraints>
                      <grid row="7" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="59331">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="867be">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="556eb" class="javax.swing.JRadioButton" binding="pmsRandomPreferringSameTypeRadioButton">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsRandomPreferringSameTypeRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsRandomPreferringSameTypeRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="c1370" class="javax.swing.JRadioButton" binding="pmsRandomCompletelyRadioButton">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsRandomCompletelyRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsRandomCompletelyRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="bc810" class="javax.swing.JRadioButton" binding="pmsMetronomeOnlyModeRadioButton">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsMetronomeOnlyModeRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsMetronomeOnlyModeRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="d836e" class="javax.swing.JCheckBox" binding="pmsGuaranteedLevel1MovesCheckBox">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsGuaranteedLevel1MovesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsGuaranteedLevel1MovesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="c408b" class="javax.swing.JCheckBox" binding="pmsReorderDamagingMovesCheckBox">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsReorderDamagingMovesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsReorderDamagingMovesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="ee040" class="javax.swing.JCheckBox" binding="pmsNoGameBreakingMovesCheckBox">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsNoGameBreakingMovesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsNoGameBreakingMovesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="aba8f" class="javax.swing.JCheckBox" binding="pmsForceGoodDamagingCheckBox" default-binding="true">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsForceGoodDamagingCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsForceGoodDamagingCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="451d5" class="javax.swing.JSlider" binding="pmsGuaranteedLevel1MovesSlider">
                    <constraints>
                      <grid row="1" column="3" row-span="2" col-span="1" vsize-policy="0" hsize-policy="6" anchor="9" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="10" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <majorTickSpacing value="1"/>
                      <maximum value="4"/>
                      <minimum value="2"/>
                      <paintLabels value="true"/>
                      <paintTicks value="false"/>
                      <snapToTicks value="true"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsGuaranteedLevel1MovesSlider.toolTipText"/>
                      <value value="2"/>
                      <valueIsAdjusting value="false"/>
                    </properties>
                  </component>
                  <component id="c6162" class="javax.swing.JSlider" binding="pmsForceGoodDamagingSlider" default-binding="true">
                    <constraints>
                      <grid row="5" column="2" row-span="1" col-span="2" vsize-policy="0" hsize-policy="6" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <majorTickSpacing value="20"/>
                      <minorTickSpacing value="5"/>
                      <paintLabels value="true"/>
                      <paintTicks value="true"/>
                      <snapToTicks value="true"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsForceGoodDamagingSlider.toolTipText"/>
                      <value value="0"/>
                    </properties>
                  </component>
                  <component id="7a835" class="javax.swing.JCheckBox" binding="pmsEvolutionMovesCheckBox">
                    <constraints>
                      <grid row="6" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsEvolutionMovesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.pmsEvolutionMovesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
          <grid id="c4c9e" layout-manager="GridBagLayout">
            <constraints>
              <tabbedpane title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.foePokemonPanel.title"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <grid id="6ba9a" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.1" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.tpPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <hspacer id="b875b">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="c7847">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="c6902" class="javax.swing.JCheckBox" binding="tpRivalCarriesStarterCheckBox">
                    <constraints>
                      <grid row="1" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRivalCarriesStarterCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRivalCarriesStarterCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="22cfb" class="javax.swing.JCheckBox" binding="tpSimilarStrengthCheckBox">
                    <constraints>
                      <grid row="2" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpSimilarStrengthCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpSimilarStrengthCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="e225c" class="javax.swing.JCheckBox" binding="tpRandomizeTrainerNamesCheckBox">
                    <constraints>
                      <grid row="1" column="6" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRandomizeTrainerNamesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRandomizeTrainerNamesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="16a2b" class="javax.swing.JCheckBox" binding="tpRandomizeTrainerClassNamesCheckBox">
                    <constraints>
                      <grid row="2" column="6" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRandomizeTrainerClassNamesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRandomizeTrainerClassNamesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="d6ea3" class="javax.swing.JCheckBox" binding="tpForceFullyEvolvedAtCheckBox">
                    <constraints>
                      <grid row="4" column="6" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpForceFullyEvolvedAtCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpForceFullyEvolvedAtCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="63823" class="javax.swing.JSlider" binding="tpForceFullyEvolvedAtSlider">
                    <constraints>
                      <grid row="5" column="6" row-span="2" col-span="2" vsize-policy="0" hsize-policy="6" anchor="9" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <majorTickSpacing value="5"/>
                      <maximum value="65"/>
                      <minimum value="30"/>
                      <minorTickSpacing value="1"/>
                      <paintLabels value="true"/>
                      <paintTicks value="true"/>
                      <snapToTicks value="true"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpForceFullyEvolvedAtSlider.toolTipText"/>
                      <value value="30"/>
                    </properties>
                  </component>
                  <component id="670de" class="javax.swing.JSlider" binding="tpPercentageLevelModifierSlider" default-binding="true">
                    <constraints>
                      <grid row="8" column="6" row-span="2" col-span="2" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <majorTickSpacing value="10"/>
                      <maximum value="50"/>
                      <minimum value="-50"/>
                      <minorTickSpacing value="2"/>
                      <paintLabels value="true"/>
                      <paintTicks value="true"/>
                      <snapToTicks value="true"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpPercentageLevelModifierSlider.toolTipText"/>
                      <value value="0"/>
                    </properties>
                  </component>
                  <component id="8428b" class="javax.swing.JCheckBox" binding="tpPercentageLevelModifierCheckBox" default-binding="true">
                    <constraints>
                      <grid row="7" column="6" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpPercentageLevelModifierCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpPercentageLevelModifierCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="41124">
                    <constraints>
                      <grid row="0" column="8" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <component id="5ff08" class="javax.swing.JCheckBox" binding="tpWeightTypesCheckBox">
                    <constraints>
                      <grid row="3" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpWeightTypesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpWeightTypesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="acf1a" class="javax.swing.JCheckBox" binding="tpDontUseLegendariesCheckBox" default-binding="true">
                    <constraints>
                      <grid row="4" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpDontUseLegendariesCheckBox.text"/>
                    </properties>
                  </component>
                  <component id="d0471" class="javax.swing.JCheckBox" binding="tpNoEarlyWonderGuardCheckBox">
                    <constraints>
                      <grid row="5" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpNoEarlyWonderGuardCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpNoEarlyWonderGuardCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="be65d" class="javax.swing.JCheckBox" binding="tpAllowAlternateFormesCheckBox">
                    <constraints>
                      <grid row="6" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpAllowAlternateFormesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpAllowAlternateFormesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="af4d1" class="javax.swing.JCheckBox" binding="tpSwapMegaEvosCheckBox">
                    <constraints>
                      <grid row="7" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpSwapMegaEvosCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpSwapMegaEvosCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="34a10" class="javax.swing.JCheckBox" binding="tpRandomShinyTrainerPokemonCheckBox">
                    <constraints>
                      <grid row="8" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="9" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRandomShinyTrainerPokemonCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRandomShinyTrainerPokemonCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <vspacer id="2342a">
                    <constraints>
                      <grid row="11" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="28e4c" class="javax.swing.JCheckBox" binding="tpEliteFourUniquePokemonCheckBox">
                    <constraints>
                      <grid row="10" column="6" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpEliteFourUniquePokemonCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpEliteFourUniquePokemonCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="727d5" class="javax.swing.JSpinner" binding="tpEliteFourUniquePokemonSpinner">
                    <constraints>
                      <grid row="10" column="7" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                    </properties>
                  </component>
                  <component id="69236" class="javax.swing.JComboBox" binding="tpComboBox">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="3" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <model>
                        <item value="Unchanged"/>
                      </model>
                    </properties>
                  </component>
                  <component id="feec4" class="javax.swing.JCheckBox" binding="tpDoubleBattleModeCheckBox">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="4" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpDoubleBattleModeCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpDoubleBattleModeCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="b62cd" class="javax.swing.JLabel" binding="tpAdditionalPokemonForLabel">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="4" vsize-policy="0" hsize-policy="0" anchor="10" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpAddMorePokemonForLabel.text"/>
                    </properties>
                  </component>
                  <component id="8f956" class="javax.swing.JCheckBox" binding="tpBossTrainersCheckBox">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpBossTrainersCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpBossTrainersCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="6d5aa" class="javax.swing.JCheckBox" binding="tpImportantTrainersCheckBox">
                    <constraints>
                      <grid row="5" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpImportantTrainersCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpImportantTrainersCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="39903" class="javax.swing.JCheckBox" binding="tpRegularTrainersCheckBox">
                    <constraints>
                      <grid row="5" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRegularTrainersCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRegularTrainersCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="181e" class="javax.swing.JSpinner" binding="tpBossTrainersSpinner">
                    <constraints>
                      <grid row="6" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="6" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="4" bottom="0" right="0" weightx="0.0" weighty="0.0" ipadx="10"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                    </properties>
                  </component>
                  <component id="e7a99" class="javax.swing.JSpinner" binding="tpImportantTrainersSpinner">
                    <constraints>
                      <grid row="6" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="4" bottom="0" right="0" weightx="0.0" weighty="0.0" ipadx="10"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                    </properties>
                  </component>
                  <component id="da8e4" class="javax.swing.JSpinner" binding="tpRegularTrainersSpinner">
                    <constraints>
                      <grid row="6" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="4" bottom="0" right="0" weightx="0.0" weighty="0.0" ipadx="10"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                    </properties>
                  </component>
                  <component id="2918f" class="javax.swing.JLabel" binding="tpHeldItemsLabel">
                    <constraints>
                      <grid row="8" column="1" row-span="1" col-span="4" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpHeldItemsLabel.text"/>
                    </properties>
                  </component>
                  <component id="dad16" class="javax.swing.JCheckBox" binding="tpBossTrainersItemsCheckBox">
                    <constraints>
                      <grid row="9" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpBossTrainersItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpBossTrainersItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="d7d93" class="javax.swing.JCheckBox" binding="tpImportantTrainersItemsCheckBox">
                    <constraints>
                      <grid row="9" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpImportantTrainersItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpImportantTrainersItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="7fddd" class="javax.swing.JCheckBox" binding="tpRegularTrainersItemsCheckBox">
                    <constraints>
                      <grid row="9" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRegularTrainersItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpRegularTrainersItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="31e9a" class="javax.swing.JCheckBox" binding="tpConsumableItemsOnlyCheckBox">
                    <constraints>
                      <grid row="10" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpConsumableItemsOnlyCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpConsumableItemsOnlyCheckBox.tooltip"/>
                    </properties>
                  </component>
                  <component id="79d6a" class="javax.swing.JCheckBox" binding="tpSensibleItemsCheckBox">
                    <constraints>
                      <grid row="10" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpSensibleItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpSensibleItemsCheckBox.tooltip"/>
                    </properties>
                  </component>
                  <component id="f3c52" class="javax.swing.JCheckBox" binding="tpHighestLevelGetsItemCheckBox">
                    <constraints>
                      <grid row="10" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpHighestLevelGetsItemCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpHighestLevelGetsItemCheckBox.tooltip"/>
                    </properties>
                  </component>
                  <component id="21090" class="javax.swing.JCheckBox" binding="tpBetterMovesetsCheckBox">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpBetterMovesetsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tpBetterMovesetsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <hspacer id="66d2">
                <constraints>
                  <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="4bb69">
                <constraints>
                  <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.1"/>
                </constraints>
              </vspacer>
              <hspacer id="377fb">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="979ca">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
              <grid id="ff2a3" binding="totpPanel" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.totpPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="9e1ec" class="javax.swing.JRadioButton" binding="totpUnchangedRadioButton">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.01" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="d7892">
                    <constraints>
                      <grid row="1" column="5" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="77258">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="2" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="cd5a0">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="18fc9">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <grid id="760ca" binding="totpAllyPanel" layout-manager="GridBagLayout">
                    <constraints>
                      <grid row="1" column="2" row-span="4" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties/>
                    <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.totpAllyPanel.title"/>
                    <children>
                      <component id="315a6" class="javax.swing.JRadioButton" binding="totpAllyUnchangedRadioButton">
                        <constraints>
                          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAllyUnchangedRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAllyUnchangedRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="68af" class="javax.swing.JRadioButton" binding="totpAllyRandomRadioButton">
                        <constraints>
                          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAllyRandomRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAllyRandomRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="3b30" class="javax.swing.JRadioButton" binding="totpAllyRandomSimilarStrengthRadioButton">
                        <constraints>
                          <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAllyRandomSimilarStrengthRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAllyRandomSimilarStrengthRadioButton.toolTipText"/>
                        </properties>
                      </component>
                    </children>
                  </grid>
                  <grid id="1af81" binding="totpAuraPanel" layout-manager="GridBagLayout">
                    <constraints>
                      <grid row="1" column="3" row-span="4" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties/>
                    <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.totpAuraPanel.title"/>
                    <children>
                      <component id="f6c3b" class="javax.swing.JRadioButton" binding="totpAuraUnchangedRadioButton">
                        <constraints>
                          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAuraUnchangedRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAuraUnchangedRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="b512" class="javax.swing.JRadioButton" binding="totpAuraRandomRadioButton">
                        <constraints>
                          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAuraRandomRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAuraRandomRadioButton.toolTipText."/>
                        </properties>
                      </component>
                      <component id="d3f0a" class="javax.swing.JRadioButton" binding="totpAuraRandomSameStrengthRadioButton">
                        <constraints>
                          <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAuraRandomSameStrengthRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAuraRandomSameStrengthRadioButton.toolTipText"/>
                        </properties>
                      </component>
                    </children>
                  </grid>
                  <component id="ab28f" class="javax.swing.JSlider" binding="totpPercentageLevelModifierSlider">
                    <constraints>
                      <grid row="4" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <majorTickSpacing value="10"/>
                      <maximum value="50"/>
                      <minimum value="-50"/>
                      <minorTickSpacing value="2"/>
                      <paintLabels value="true"/>
                      <paintTicks value="true"/>
                      <snapToTicks value="true"/>
                      <value value="0"/>
                    </properties>
                  </component>
                  <component id="69ebe" class="javax.swing.JCheckBox" binding="totpPercentageLevelModifierCheckBox">
                    <constraints>
                      <grid row="3" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpPercentageLevelModifierCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpPercentageLevelModifierCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="94ffc" class="javax.swing.JCheckBox" binding="totpRandomizeHeldItemsCheckBox">
                    <constraints>
                      <grid row="1" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpRandomizeHeldItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpRandomizeHeldItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="555b9" class="javax.swing.JCheckBox" binding="totpAllowAltFormesCheckBox">
                    <constraints>
                      <grid row="2" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAllowAltFormesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpAllowAltFormesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="ddde3" class="javax.swing.JRadioButton" binding="totpRandomRadioButton">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpRandomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpRandomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="148f2" class="javax.swing.JRadioButton" binding="totpRandomSimilarStrengthRadioButton">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpRandomSimilarStrengthRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.totpRandomSimilarStrengthRadioButton.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
          <grid id="59ef3" layout-manager="GridBagLayout">
            <constraints>
              <tabbedpane title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.wildPokemonPanel.title"/>
            </constraints>
            <properties>
              <enabled value="false"/>
            </properties>
            <border type="none"/>
            <children>
              <grid id="c2161" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.1" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.wpPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="34243" class="javax.swing.JRadioButton" binding="wpUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="45277">
                    <constraints>
                      <grid row="1" column="5" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="580fb">
                    <constraints>
                      <grid row="8" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="d940d">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="b2fc4">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="17061" class="javax.swing.JRadioButton" binding="wpRandomRadioButton" default-binding="true">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpRandomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpRandomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="4abec" class="javax.swing.JRadioButton" binding="wpArea1To1RadioButton">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpArea1To1RadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpArea1To1RadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="44917" class="javax.swing.JRadioButton" binding="wpGlobal1To1RadioButton">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpGlobal1To1RadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpGlobal1To1RadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <grid id="ff19e" layout-manager="GridBagLayout">
                    <constraints>
                      <grid row="1" column="2" row-span="6" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="0" bottom="0" right="80" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties/>
                    <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.wpARPanel.title"/>
                    <children>
                      <component id="6fd8b" class="javax.swing.JRadioButton" binding="wpARNoneRadioButton">
                        <constraints>
                          <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpARNoneRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpARNoneRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <hspacer id="cc8ed">
                        <constraints>
                          <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </hspacer>
                      <vspacer id="6eb45">
                        <constraints>
                          <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.1"/>
                        </constraints>
                      </vspacer>
                      <hspacer id="697e1">
                        <constraints>
                          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </hspacer>
                      <component id="f5f68" class="javax.swing.JRadioButton" binding="wpARSimilarStrengthRadioButton">
                        <constraints>
                          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpARSimilarStrengthRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpARSimilarStrengthRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="f3f94" class="javax.swing.JRadioButton" binding="wpARCatchEmAllModeRadioButton">
                        <constraints>
                          <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpARCatchEmAllModeRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpARCatchEmAllModeRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="bc941" class="javax.swing.JRadioButton" binding="wpARTypeThemeAreasRadioButton">
                        <constraints>
                          <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpARTypeThemeAreasRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpARTypeThemeAreasRadioButton.toolTipText"/>
                        </properties>
                      </component>
                    </children>
                  </grid>
                  <component id="58553" class="javax.swing.JCheckBox" binding="wpUseTimeBasedEncountersCheckBox">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpUseTimeBasedEncountersCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpUseTimeBasedEncountersCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="31e5d" class="javax.swing.JCheckBox" binding="wpDontUseLegendariesCheckBox" default-binding="true">
                    <constraints>
                      <grid row="2" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpDontUseLegendariesCheckBox.text"/>
                    </properties>
                  </component>
                  <component id="daaf0" class="javax.swing.JCheckBox" binding="wpSetMinimumCatchRateCheckBox">
                    <constraints>
                      <grid row="3" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpSetMinimumCatchRateCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpSetMinimumCatchRateCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="b72b8" class="javax.swing.JCheckBox" binding="wpRandomizeHeldItemsCheckBox">
                    <constraints>
                      <grid row="4" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpRandomizeHeldItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpRandomizeHeldItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="7e552" class="javax.swing.JCheckBox" binding="wpBanBadItemsCheckBox" default-binding="true">
                    <constraints>
                      <grid row="5" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpBanBadItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpBanBadItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="da56d" class="javax.swing.JCheckBox" binding="wpBalanceShakingGrassPokemonCheckBox">
                    <constraints>
                      <grid row="6" column="3" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpBalanceShakingGrassPokemonCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpBalanceShakingGrassPokemonCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="a5c09" class="javax.swing.JCheckBox" binding="wpPercentageLevelModifierCheckBox" default-binding="true">
                    <constraints>
                      <grid row="7" column="3" row-span="1" col-span="2" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpPercentageLevelModifierCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpPercentageLevelModifierCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="7e56b" class="javax.swing.JSlider" binding="wpPercentageLevelModifierSlider" default-binding="true">
                    <constraints>
                      <grid row="8" column="3" row-span="1" col-span="2" vsize-policy="0" hsize-policy="6" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0" ipadx="30"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <majorTickSpacing value="10"/>
                      <maximum value="50"/>
                      <minimum value="-50"/>
                      <minorTickSpacing value="2"/>
                      <paintLabels value="true"/>
                      <paintTicks value="true"/>
                      <snapToTicks value="true"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpPercentageLevelModifierSlider.toolTipText"/>
                      <value value="0"/>
                    </properties>
                  </component>
                  <component id="f60c2" class="javax.swing.JSlider" binding="wpSetMinimumCatchRateSlider">
                    <constraints>
                      <grid row="3" column="4" row-span="2" col-span="1" vsize-policy="0" hsize-policy="6" anchor="9" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="10" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <majorTickSpacing value="1"/>
                      <maximum value="5"/>
                      <minimum value="1"/>
                      <paintLabels value="true"/>
                      <snapToTicks value="true"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpSetMinimumCatchRateSlider.toolTipText"/>
                      <value value="1"/>
                    </properties>
                  </component>
                  <component id="438d0" class="javax.swing.JCheckBox" binding="wpAllowAltFormesCheckBox">
                    <constraints>
                      <grid row="9" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpAllowAltFormesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wpAllowAltFormesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <hspacer id="731f9">
                <constraints>
                  <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="f6a73">
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.1"/>
                </constraints>
              </vspacer>
              <hspacer id="694df">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="3b48">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
            </children>
          </grid>
          <grid id="c0784" layout-manager="GridBagLayout">
            <constraints>
              <tabbedpane title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.tmsHMsTutorsPanel.title"/>
            </constraints>
            <properties>
              <enabled value="false"/>
            </properties>
            <border type="none"/>
            <children>
              <grid id="b2c62" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.1" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.tmPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <grid id="a78c8" layout-manager="GridBagLayout">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties/>
                    <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.tmMovesPanel.title"/>
                    <children>
                      <component id="77153" class="javax.swing.JRadioButton" binding="tmUnchangedRadioButton" default-binding="true">
                        <constraints>
                          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag top="0" left="0" bottom="0" right="70" weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmUnchangedRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmUnchangedRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <vspacer id="c775">
                        <constraints>
                          <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </vspacer>
                      <hspacer id="43886">
                        <constraints>
                          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </hspacer>
                      <vspacer id="efb0b">
                        <constraints>
                          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </vspacer>
                      <component id="1bf95" class="javax.swing.JRadioButton" binding="tmRandomRadioButton" default-binding="true">
                        <constraints>
                          <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmRandomRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmRandomRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="e94bb" class="javax.swing.JCheckBox" binding="tmNoGameBreakingMovesCheckBox">
                        <constraints>
                          <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="9" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmNoGameBreakingMovesCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmNoGameBreakingMovesCheckBox.toolTipText"/>
                        </properties>
                      </component>
                      <component id="e66ed" class="javax.swing.JCheckBox" binding="tmKeepFieldMoveTMsCheckBox">
                        <constraints>
                          <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmKeepFieldMoveTMsCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmKeepFieldMoveTMsCheckBox.toolTipText"/>
                        </properties>
                      </component>
                      <component id="39544" class="javax.swing.JCheckBox" binding="tmForceGoodDamagingCheckBox" default-binding="true">
                        <constraints>
                          <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmForceGoodDamagingCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmForceGoodDamagingCheckBox.toolTipText"/>
                        </properties>
                      </component>
                      <component id="60c91" class="javax.swing.JSlider" binding="tmForceGoodDamagingSlider" default-binding="true">
                        <constraints>
                          <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <majorTickSpacing value="20"/>
                          <minorTickSpacing value="5"/>
                          <paintLabels value="true"/>
                          <paintTicks value="true"/>
                          <snapToTicks value="true"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmForceGoodDamagingSlider.toolTipText"/>
                          <value value="0"/>
                        </properties>
                      </component>
                    </children>
                  </grid>
                  <hspacer id="fc0a6">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="f2096">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="5a534">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="47186">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <grid id="51d08" layout-manager="GridBagLayout">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="20" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties/>
                    <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.thcPanel.title"/>
                    <children>
                      <component id="eb901" class="javax.swing.JRadioButton" binding="thcUnchangedRadioButton" default-binding="true">
                        <constraints>
                          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.thcUnchangedRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.thcUnchangedRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <vspacer id="f28aa">
                        <constraints>
                          <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </vspacer>
                      <hspacer id="b7ad2">
                        <constraints>
                          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </hspacer>
                      <vspacer id="5acfb">
                        <constraints>
                          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </vspacer>
                      <component id="6bd5e" class="javax.swing.JRadioButton" binding="thcRandomPreferSameTypeRadioButton" default-binding="true">
                        <constraints>
                          <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.thcRandomPreferSameTypeRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.thcRandomPreferSameTypeRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="bdcf8" class="javax.swing.JRadioButton" binding="thcRandomCompletelyRadioButton">
                        <constraints>
                          <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.thcRandomCompletelyRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.thcRandomCompletelyRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="1bb6c" class="javax.swing.JRadioButton" binding="thcFullCompatibilityRadioButton" default-binding="true">
                        <constraints>
                          <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.thcFullCompatibilityRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.thcFullCompatibilityRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="65e15" class="javax.swing.JCheckBox" binding="tmFollowEvolutionsCheckBox">
                        <constraints>
                          <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmFollowEvolutionsCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmFollowEvolutionsCheckBox.toolTipText"/>
                        </properties>
                      </component>
                      <component id="7ba73" class="javax.swing.JCheckBox" binding="tmLevelupMoveSanityCheckBox" default-binding="true">
                        <constraints>
                          <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmLevelupMoveSanityCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmLevelupMoveSanityCheckBox.toolTipText"/>
                        </properties>
                      </component>
                      <component id="b7d85" class="javax.swing.JCheckBox" binding="tmFullHMCompatibilityCheckBox">
                        <constraints>
                          <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmFullHMCompatibilityCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.tmFullHMCompatibilityCheckBox.toolTipText"/>
                        </properties>
                      </component>
                    </children>
                  </grid>
                </children>
              </grid>
              <hspacer id="674e">
                <constraints>
                  <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="f5d4b">
                <constraints>
                  <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.1"/>
                </constraints>
              </vspacer>
              <hspacer id="216da">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="a4da9">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
              <grid id="e917c" binding="moveTutorPanel" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.mtPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <grid id="55a17" binding="mtMovesPanel" layout-manager="GridBagLayout">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties/>
                    <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.mtMovesPanel.title"/>
                    <children>
                      <component id="c71e0" class="javax.swing.JRadioButton" binding="mtUnchangedRadioButton" default-binding="true">
                        <constraints>
                          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag top="0" left="0" bottom="0" right="70" weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtUnchangedRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtUnchangedRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <vspacer id="e8b9">
                        <constraints>
                          <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </vspacer>
                      <hspacer id="5c652">
                        <constraints>
                          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </hspacer>
                      <vspacer id="ab20f">
                        <constraints>
                          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </vspacer>
                      <component id="7a3f2" class="javax.swing.JRadioButton" binding="mtRandomRadioButton" default-binding="true">
                        <constraints>
                          <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtRandomRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtRandomRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="108c3" class="javax.swing.JSlider" binding="mtForceGoodDamagingSlider">
                        <constraints>
                          <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <majorTickSpacing value="20"/>
                          <minorTickSpacing value="5"/>
                          <paintLabels value="true"/>
                          <paintTicks value="true"/>
                          <snapToTicks value="true"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtForceGoodDamagingSlider.toolTipText"/>
                          <value value="0"/>
                        </properties>
                      </component>
                      <component id="3c7ba" class="javax.swing.JCheckBox" binding="mtNoGameBreakingMovesCheckBox">
                        <constraints>
                          <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="9" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtNoGameBreakingMovesCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtNoGameBreakingMovesCheckBox.toolTipText"/>
                        </properties>
                      </component>
                      <component id="d8aa" class="javax.swing.JCheckBox" binding="mtKeepFieldMoveTutorsCheckBox">
                        <constraints>
                          <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtKeepFieldMoveTutorsCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtKeepFieldMoveTutorsCheckBox.toolTipText"/>
                        </properties>
                      </component>
                      <component id="5a253" class="javax.swing.JCheckBox" binding="mtForceGoodDamagingCheckBox">
                        <constraints>
                          <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtForceGoodDamagingCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtForceGoodDamagingCheckBox.toolTipText"/>
                        </properties>
                      </component>
                    </children>
                  </grid>
                  <hspacer id="417fe">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="6203c">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="fe938">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="22179">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <grid id="b731b" binding="mtCompatPanel" layout-manager="GridBagLayout">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="20" bottom="0" right="0" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties/>
                    <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.mtcPanel.title"/>
                    <children>
                      <component id="909ae" class="javax.swing.JRadioButton" binding="mtcUnchangedRadioButton" default-binding="true">
                        <constraints>
                          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtcUnchangedRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtcUnchangedRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <vspacer id="993c4">
                        <constraints>
                          <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </vspacer>
                      <hspacer id="4d26c">
                        <constraints>
                          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </hspacer>
                      <vspacer id="e70a2">
                        <constraints>
                          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                      </vspacer>
                      <component id="13e1d" class="javax.swing.JRadioButton" binding="mtcRandomPreferSameTypeRadioButton" default-binding="true">
                        <constraints>
                          <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtcRandomPreferSameTypeRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtcRandomPreferSameTypeRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="e0f5e" class="javax.swing.JRadioButton" binding="mtcRandomCompletelyRadioButton">
                        <constraints>
                          <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtcRandomCompletelyRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtcRandomCompletelyRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="6ffa1" class="javax.swing.JRadioButton" binding="mtcFullCompatibilityRadioButton" default-binding="true">
                        <constraints>
                          <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtcFullCompatibilityRadioButton.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtcFullCompatibilityRadioButton.toolTipText"/>
                        </properties>
                      </component>
                      <component id="e4773" class="javax.swing.JCheckBox" binding="mtFollowEvolutionsCheckBox">
                        <constraints>
                          <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtFollowEvolutionsCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtFollowEvolutionsCheckBox.toolTipText"/>
                        </properties>
                      </component>
                      <component id="801a1" class="javax.swing.JCheckBox" binding="mtLevelupMoveSanityCheckBox">
                        <constraints>
                          <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                          <gridbag weightx="0.0" weighty="0.0"/>
                        </constraints>
                        <properties>
                          <enabled value="false"/>
                          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtLevelupMoveSanityCheckBox.text"/>
                          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtLevelupMoveSanityCheckBox.toolTipText"/>
                        </properties>
                      </component>
                    </children>
                  </grid>
                  <component id="5bbc3" class="javax.swing.JLabel" binding="mtNoExistLabel">
                    <constraints>
                      <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="true"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.mtNoExistLabel.text"/>
                    </properties>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
          <grid id="42a4d" layout-manager="GridBagLayout">
            <constraints>
              <tabbedpane title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.itemsPanel.title"/>
            </constraints>
            <properties>
              <enabled value="false"/>
            </properties>
            <border type="none"/>
            <children>
              <grid id="7b397" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.1" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.fiPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="4d6b9" class="javax.swing.JRadioButton" binding="fiUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="0" bottom="0" right="140" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="365a6">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="58d9c">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="b73ae">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="219a6">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="4bc5e" class="javax.swing.JRadioButton" binding="fiShuffleRadioButton" default-binding="true">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiShuffleRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiShuffleRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="74283" class="javax.swing.JRadioButton" binding="fiRandomRadioButton" default-binding="true">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiRandomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiRandomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="b0d01" class="javax.swing.JRadioButton" binding="fiRandomEvenDistributionRadioButton" default-binding="true">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiRandomEvenDistributionRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiRandomEvenDistributionRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="d9fbb" class="javax.swing.JCheckBox" binding="fiBanBadItemsCheckBox" default-binding="true">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiBanBadItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.fiBanBadItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <hspacer id="36f9b">
                <constraints>
                  <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="e7c77">
                <constraints>
                  <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.1"/>
                </constraints>
              </vspacer>
              <hspacer id="a18c0">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="c7e4f">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
              <grid id="ae8f3" binding="shopItemsPanel" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.shPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="85c80" class="javax.swing.JRadioButton" binding="shUnchangedRadioButton" default-binding="true">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="0" bottom="0" right="140" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="ffc84">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="2f864">
                    <constraints>
                      <grid row="7" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="cf4a3">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="70501">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="30ba3" class="javax.swing.JRadioButton" binding="shShuffleRadioButton" default-binding="true">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shShuffleRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shShuffleRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="4324" class="javax.swing.JRadioButton" binding="shRandomRadioButton" default-binding="true">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shRandomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shRandomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="2d537" class="javax.swing.JCheckBox" binding="shBanOverpoweredShopItemsCheckBox">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shBanOverpoweredShopItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shBanOverpoweredShopItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="c8590" class="javax.swing.JCheckBox" binding="shBanBadItemsCheckBox" default-binding="true">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shBanBadItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shBanBadItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="65395" class="javax.swing.JCheckBox" binding="shBanRegularShopItemsCheckBox">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shBanRegularShopItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shBanRegularShopItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="9e484" class="javax.swing.JCheckBox" binding="shBalanceShopItemPricesCheckBox">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shBalanceShopItemPricesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shBalanceShopItemPricesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="5bba5" class="javax.swing.JCheckBox" binding="shGuaranteeEvolutionItemsCheckBox">
                    <constraints>
                      <grid row="5" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shGuaranteeEvolutionItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shGuaranteeEvolutionItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="f7f02" class="javax.swing.JCheckBox" binding="shGuaranteeXItemsCheckBox">
                    <constraints>
                      <grid row="6" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <selected value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shGuaranteeXItemsCheckbox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.shGuaranteeXItemsCheckbox.tooltipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <grid id="30a3f" binding="pickupItemsPanel" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.puPanel.title">
                  <font style="1"/>
                </border>
                <children>
                  <component id="62bb6" class="javax.swing.JRadioButton" binding="puUnchangedRadioButton">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag top="0" left="0" bottom="0" right="140" weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.puUnchangedRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.puUnchangedRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="5bf93">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="5fa25">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="3b455">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="ff3b7">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </vspacer>
                  <component id="478a" class="javax.swing.JRadioButton" binding="puRandomRadioButton">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.puRandomRadioButton.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.puRandomRadioButton.toolTipText"/>
                    </properties>
                  </component>
                  <component id="45bbb" class="javax.swing.JCheckBox" binding="puBanBadItemsCheckBox">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.puBanBadItemsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.puBanBadItemsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
          <grid id="e8bbb" binding="baseTweaksPanel" layout-manager="GridBagLayout">
            <constraints>
              <tabbedpane title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.miscTweaksPanel.title"/>
            </constraints>
            <properties>
              <enabled value="false"/>
            </properties>
            <border type="none"/>
            <children>
              <grid id="cb53c" binding="miscTweaksPanel" layout-manager="GridBagLayout">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.1" weighty="0.1"/>
                </constraints>
                <properties/>
                <border type="none" title-resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" title-key="GUI.miscPanel.title">
                  <font/>
                </border>
                <children>
                  <component id="70ba8" class="javax.swing.JCheckBox" binding="miscBWExpPatchCheckBox">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscBWExpPatchCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscBWExpPatchCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <hspacer id="e97bc">
                    <constraints>
                      <grid row="1" column="5" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.1" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <vspacer id="fa9a6">
                    <constraints>
                      <grid row="7" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.1"/>
                    </constraints>
                  </vspacer>
                  <hspacer id="42099">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                  </hspacer>
                  <component id="7f730" class="javax.swing.JCheckBox" binding="miscNerfXAccuracyCheckBox">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscNerfXAccuracyCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscNerfXAccuracyCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="39078" class="javax.swing.JCheckBox" binding="miscFixCritRateCheckBox">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscFixCritRateCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscFixCritRateCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="89b5c" class="javax.swing.JCheckBox" binding="miscFastestTextCheckBox">
                    <constraints>
                      <grid row="1" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscFastestTextCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscFastestTextCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="5c1b1" class="javax.swing.JCheckBox" binding="miscRunningShoesIndoorsCheckBox">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscRunningShoesIndoorsCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscRunningShoesIndoorsCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="bd6ce" class="javax.swing.JCheckBox" binding="miscRandomizePCPotionCheckBox">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscRandomizePCPotionCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscRandomizePCPotionCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="a174b" class="javax.swing.JCheckBox" binding="miscAllowPikachuEvolutionCheckBox">
                    <constraints>
                      <grid row="2" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscAllowPikachuEvolutionCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscAllowPikachuEvolutionCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="6af08" class="javax.swing.JCheckBox" binding="miscGiveNationalDexAtCheckBox">
                    <constraints>
                      <grid row="2" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscGiveNationalDexAtCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscGiveNationalDexAtCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="f8bbd" class="javax.swing.JCheckBox" binding="miscUpdateTypeEffectivenessCheckBox">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscUpdateTypeEffectivenessCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscUpdateTypeEffectivenessCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="b7d7c" class="javax.swing.JCheckBox" binding="miscLowerCasePokemonNamesCheckBox">
                    <constraints>
                      <grid row="3" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscLowerCasePokemonNamesCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscLowerCasePokemonNamesCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="7fe16" class="javax.swing.JCheckBox" binding="miscRandomizeCatchingTutorialCheckBox">
                    <constraints>
                      <grid row="3" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscRandomizeCatchingTutorialCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscRandomizeCatchingTutorialCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="d8e23" class="javax.swing.JCheckBox" binding="miscBanLuckyEggCheckBox">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscBanLuckyEggCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscBanLuckyEggCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="9cf5c" class="javax.swing.JCheckBox" binding="miscNoFreeLuckyEggCheckBox">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscNoFreeLuckyEggCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscNoFreeLuckyEggCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="68538" class="javax.swing.JCheckBox" binding="miscBanBigMoneyManiacCheckBox">
                    <constraints>
                      <grid row="4" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscBanBigMoneyManiacCheckBox.text"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscBanBigMoneyManiacCheckBox.toolTipText"/>
                    </properties>
                  </component>
                  <component id="3e84b" class="javax.swing.JLabel" binding="mtNoneAvailableLabel">
                    <constraints>
                      <grid row="0" column="1" row-span="1" col-span="2" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.miscNoneAvailableLabel.text"/>
                    </properties>
                  </component>
                  <component id="27c9" class="javax.swing.JCheckBox" binding="miscSOSBattlesCheckBox">
                    <constraints>
                      <grid row="4" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.sosBattles.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.sosBattles.toolTipText"/>
                    </properties>
                  </component>
                  <component id="5cd10" class="javax.swing.JCheckBox" binding="miscBalanceStaticLevelsCheckBox">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.balanceStaticLevels.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.balanceStaticLevels.toolTipText"/>
                    </properties>
                  </component>
                  <component id="1d98d" class="javax.swing.JCheckBox" binding="miscRetainAltFormesCheckBox">
                    <constraints>
                      <grid row="5" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.retainAltFormes.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.retainAltFormes.toolTipText"/>
                    </properties>
                  </component>
                  <component id="baacf" class="javax.swing.JCheckBox" binding="miscRunWithoutRunningShoesCheckBox">
                    <constraints>
                      <grid row="5" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.runWithoutRunningShoes.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.runWithoutRunningShoes.toolTipText"/>
                    </properties>
                  </component>
                  <component id="52a" class="javax.swing.JCheckBox" binding="miscFasterHPAndEXPBarsCheckBox">
                    <constraints>
                      <grid row="5" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.fasterHpAndExpBars.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.fasterHpAndExpBars.toolTipText"/>
                    </properties>
                  </component>
                  <component id="531ae" class="javax.swing.JCheckBox" binding="miscForceChallengeModeCheckBox">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.forceChallengeMode.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.forceChallengeMode.toolTipText"/>
                    </properties>
                  </component>
                  <component id="d472a" class="javax.swing.JCheckBox" binding="miscFastDistortionWorldCheckBox">
                    <constraints>
                      <grid row="6" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.fastDistortionWorld.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.fastDistortionWorld.toolTipText"/>
                    </properties>
                  </component>
                  <component id="720c8" class="javax.swing.JCheckBox" binding="miscUpdateRotomFormeTypingCheckBox">
                    <constraints>
                      <grid row="6" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.updateRotomFormeTyping.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.updateRotomFormeTyping.toolTipText"/>
                    </properties>
                  </component>
                  <component id="1f079" class="javax.swing.JCheckBox" binding="miscDisableLowHPMusicCheckBox">
                    <constraints>
                      <grid row="6" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <gridbag weightx="0.0" weighty="0.0"/>
                    </constraints>
                    <properties>
                      <enabled value="false"/>
                      <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.disableLowHpMusic.name"/>
                      <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="CodeTweaks.disableLowHpMusic.toolTipText"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <hspacer id="c4e06">
                <constraints>
                  <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="b5a7f">
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
              <hspacer id="b60f7">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </hspacer>
              <vspacer id="b5389">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                  <gridbag weightx="0.0" weighty="0.0"/>
                </constraints>
              </vspacer>
            </children>
          </grid>
        </children>
      </tabbedpane>
      <component id="a7e2c" class="javax.swing.JButton" binding="openROMButton" default-binding="true">
        <constraints>
          <grid row="1" column="9" row-span="1" col-span="3" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag top="0" left="0" bottom="10" right="0" weightx="0.3" weighty="0.0"/>
        </constraints>
        <properties>
          <requestFocusEnabled value="false"/>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.openROMButton.text"/>
        </properties>
      </component>
      <component id="60ca8" class="javax.swing.JButton" binding="randomizeSaveButton" default-binding="true">
        <constraints>
          <grid row="2" column="9" row-span="1" col-span="3" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag top="0" left="0" bottom="10" right="0" weightx="0.3" weighty="0.0"/>
        </constraints>
        <properties>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.randomizeSaveButton.text"/>
        </properties>
      </component>
      <component id="d61b0" class="javax.swing.JButton" binding="premadeSeedButton" default-binding="true">
        <constraints>
          <grid row="3" column="9" row-span="1" col-span="3" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag top="0" left="0" bottom="10" right="0" weightx="0.3" weighty="0.0"/>
        </constraints>
        <properties>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.premadeSeedButton.text"/>
        </properties>
      </component>
      <component id="2f42" class="javax.swing.JButton" binding="settingsButton" default-binding="true">
        <constraints>
          <grid row="4" column="9" row-span="1" col-span="3" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.3" weighty="0.0"/>
        </constraints>
        <properties>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.settingsButton.text"/>
        </properties>
      </component>
      <component id="ca71c" class="javax.swing.JLabel" binding="websiteLinkLabel">
        <constraints>
          <grid row="6" column="11" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="4" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.websiteLinkLabel.text"/>
        </properties>
      </component>
      <component id="df6e7" class="javax.swing.JLabel" binding="versionLabel">
        <constraints>
          <grid row="6" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <font style="1"/>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.versionLabel.text"/>
        </properties>
      </component>
      <hspacer id="d2a3b">
        <constraints>
          <grid row="3" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </hspacer>
      <hspacer id="6276e">
        <constraints>
          <grid row="3" column="12" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </hspacer>
      <vspacer id="d5978">
        <constraints>
          <grid row="0" column="10" row-span="1" col-span="2" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </vspacer>
      <vspacer id="be7c1">
        <constraints>
          <grid row="9" column="7" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </vspacer>
      <component id="33b75" class="javax.swing.JLabel" binding="gameMascotLabel">
        <constraints>
          <grid row="1" column="7" row-span="4" col-span="1" vsize-policy="0" hsize-policy="0" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.1" weighty="0.0"/>
        </constraints>
        <properties>
          <icon value="com/dabomstew/pkrandom/newgui/emptyIcon.png"/>
          <text value=""/>
        </properties>
      </component>
      <component id="52c7e" class="javax.swing.JButton" binding="saveSettingsButton" default-binding="true">
        <constraints>
          <grid row="4" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.1" weighty="0.0"/>
        </constraints>
        <properties>
          <enabled value="false"/>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.saveSettingsButton.text"/>
          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.saveSettingsButton.toolTipText"/>
        </properties>
      </component>
      <component id="b0dde" class="javax.swing.JButton" binding="loadSettingsButton" default-binding="true">
        <constraints>
          <grid row="4" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.1" weighty="0.0"/>
        </constraints>
        <properties>
          <enabled value="false"/>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.loadSettingsButton.text"/>
          <toolTipText resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.loadSettingsButton.toolTipText"/>
        </properties>
      </component>
      <hspacer id="bd24f">
        <constraints>
          <grid row="2" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.1" weighty="0.0"/>
        </constraints>
      </hspacer>
      <hspacer id="afa25">
        <constraints>
          <grid row="2" column="6" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.2" weighty="0.0"/>
        </constraints>
      </hspacer>
      <hspacer id="46">
        <constraints>
          <grid row="2" column="8" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.1" weighty="0.0"/>
        </constraints>
      </hspacer>
      <vspacer id="da2fa">
        <constraints>
          <grid row="5" column="2" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </vspacer>
      <component id="1c59e" class="javax.swing.JLabel" binding="wikiLinkLabel">
        <constraints>
          <grid row="6" column="10" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="4" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag top="0" left="0" bottom="0" right="10" weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <horizontalAlignment value="10"/>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GUI.wikiLinkLabel.text"/>
        </properties>
      </component>
    </children>
  </grid>
  <buttonGroups>
    <group name="trainerPokemonButtonGroup">
      <member id="ddab7"/>
      <member id="2fe72"/>
      <member id="cd865"/>
      <member id="bd6d2"/>
      <member id="334e5"/>
      <member id="40ee1"/>
    </group>
    <group name="baseStatButtonGroup">
      <member id="4535b"/>
      <member id="e8460"/>
      <member id="576dd"/>
    </group>
    <group name="expCurveButtonGroup">
      <member id="8625b"/>
      <member id="9be58"/>
      <member id="213c9"/>
    </group>
    <group name="pokemonTypeButtonGroup">
      <member id="fb38a"/>
      <member id="f7a27"/>
      <member id="45dd7"/>
    </group>
    <group name="pokemonEvoButtonGroup">
      <member id="9dc25"/>
      <member id="577d"/>
      <member id="7fc31"/>
    </group>
    <group name="pokemonAbilityButtonGroup">
      <member id="fa40d"/>
      <member id="5bd6c"/>
    </group>
    <group name="starterButtonGroup">
      <member id="124c"/>
      <member id="94a4e"/>
      <member id="c1c80"/>
      <member id="12184"/>
    </group>
    <group name="staticPokemonButtonGroup">
      <member id="a28a1"/>
      <member id="66089"/>
      <member id="17456"/>
      <member id="282a5"/>
    </group>
    <group name="tradeButtonGroup">
      <member id="4e02b"/>
      <member id="a626c"/>
      <member id="f6c7b"/>
    </group>
    <group name="pokemonMovesetButtonGroup">
      <member id="b296a"/>
      <member id="556eb"/>
      <member id="c1370"/>
      <member id="bc810"/>
    </group>
    <group name="wildPokemonButtonGroup">
      <member id="34243"/>
      <member id="17061"/>
      <member id="4abec"/>
      <member id="44917"/>
    </group>
    <group name="wildPokemonAdditionalButtonGroup">
      <member id="6fd8b"/>
      <member id="f5f68"/>
      <member id="f3f94"/>
      <member id="bc941"/>
    </group>
    <group name="tmsButtonGroup">
      <member id="77153"/>
      <member id="1bf95"/>
    </group>
    <group name="tmCompatButtonGroup">
      <member id="eb901"/>
      <member id="6bd5e"/>
      <member id="bdcf8"/>
      <member id="1bb6c"/>
    </group>
    <group name="tutorButtonGroup">
      <member id="c71e0"/>
      <member id="7a3f2"/>
    </group>
    <group name="tutorCompatButtonGroup">
      <member id="909ae"/>
      <member id="13e1d"/>
      <member id="e0f5e"/>
      <member id="6ffa1"/>
    </group>
    <group name="fieldItemButtonGroup">
      <member id="4d6b9"/>
      <member id="4bc5e"/>
      <member id="74283"/>
      <member id="b0d01"/>
    </group>
    <group name="shopItemButtonGroup">
      <member id="85c80"/>
      <member id="30ba3"/>
      <member id="4324"/>
    </group>
    <group name="totemPokemonButtonGroup">
      <member id="9e1ec"/>
      <member id="ddde3"/>
      <member id="148f2"/>
    </group>
    <group name="allyPokemonButtonGroup">
      <member id="315a6"/>
      <member id="68af"/>
      <member id="3b30"/>
    </group>
    <group name="auraButtonGroup">
      <member id="f6c3b"/>
      <member id="b512"/>
      <member id="d3f0a"/>
    </group>
    <group name="pickupItemButtonGroup">
      <member id="62bb6"/>
      <member id="478a"/>
    </group>
  </buttonGroups>
  <inspectionSuppressions>
    <suppress inspection="MissingMnemonic"/>
    <suppress inspection="NoLabelFor" id="3169c"/>
    <suppress inspection="NoLabelFor" id="36c27"/>
    <suppress inspection="NoLabelFor" id="83a64"/>
  </inspectionSuppressions>
</form>
