#ifndef GUARD_CONSTANTS_SPECIES_H
#define GUAR<PERSON>_CONSTANTS_SPECIES_H

#define SPECIES_NONE 0
#define SPECIES_BULBASAUR 1
#define SPECIES_IVYSAUR 2
#define SPECIES_VENUSAUR 3
#define SPECIES_CHARMANDER 4
#define SPECIES_CHARMELEON 5
#define SPECIES_CHARIZARD 6
#define SPECIES_SQUIRTLE 7
#define SPECIES_WARTORTLE 8
#define SPECIES_BLASTOISE 9
#define SPECIES_CATERPIE 10
#define SPECIES_METAPOD 11
#define SPECIES_BUTTERFREE 12
#define SPECIES_WEEDLE 13
#define SPECIES_KAKUNA 14
#define SPECIES_BEEDRILL 15
#define SPECIES_PIDGEY 16
#define SPECIES_PIDGEOTTO 17
#define SPECIES_PIDGEOT 18
#define SPECIES_RATTATA 19
#define SPECIES_RATICATE 20
#define SPECIES_SPEAROW 21
#define SPECIES_FEAROW 22
#define SPECIES_EKANS 23
#define SPECIES_ARBOK 24
#define SPECIES_PIKACHU 25
#define SPECIES_RAICHU 26
#define SPECIES_SANDSHREW 27
#define SPECIES_SANDSLASH 28
#define SPECIES_NIDORAN_F 29
#define SPECIES_NIDORINA 30
#define SPECIES_NIDOQUEEN 31
#define SPECIES_NIDORAN_M 32
#define SPECIES_NIDORINO 33
#define SPECIES_NIDOKING 34
#define SPECIES_CLEFAIRY 35
#define SPECIES_CLEFABLE 36
#define SPECIES_VULPIX 37
#define SPECIES_NINETALES 38
#define SPECIES_JIGGLYPUFF 39
#define SPECIES_WIGGLYTUFF 40
#define SPECIES_ZUBAT 41
#define SPECIES_GOLBAT 42
#define SPECIES_ODDISH 43
#define SPECIES_GLOOM 44
#define SPECIES_VILEPLUME 45
#define SPECIES_PARAS 46
#define SPECIES_PARASECT 47
#define SPECIES_VENONAT 48
#define SPECIES_VENOMOTH 49
#define SPECIES_DIGLETT 50
#define SPECIES_DUGTRIO 51
#define SPECIES_MEOWTH 52
#define SPECIES_PERSIAN 53
#define SPECIES_PSYDUCK 54
#define SPECIES_GOLDUCK 55
#define SPECIES_MANKEY 56
#define SPECIES_PRIMEAPE 57
#define SPECIES_GROWLITHE 58
#define SPECIES_ARCANINE 59
#define SPECIES_POLIWAG 60
#define SPECIES_POLIWHIRL 61
#define SPECIES_POLIWRATH 62
#define SPECIES_ABRA 63
#define SPECIES_KADABRA 64
#define SPECIES_ALAKAZAM 65
#define SPECIES_MACHOP 66
#define SPECIES_MACHOKE 67
#define SPECIES_MACHAMP 68
#define SPECIES_BELLSPROUT 69
#define SPECIES_WEEPINBELL 70
#define SPECIES_VICTREEBEL 71
#define SPECIES_TENTACOOL 72
#define SPECIES_TENTACRUEL 73
#define SPECIES_GEODUDE 74
#define SPECIES_GRAVELER 75
#define SPECIES_GOLEM 76
#define SPECIES_PONYTA 77
#define SPECIES_RAPIDASH 78
#define SPECIES_SLOWPOKE 79
#define SPECIES_SLOWBRO 80
#define SPECIES_MAGNEMITE 81
#define SPECIES_MAGNETON 82
#define SPECIES_FARFETCHD 83
#define SPECIES_DODUO 84
#define SPECIES_DODRIO 85
#define SPECIES_SEEL 86
#define SPECIES_DEWGONG 87
#define SPECIES_GRIMER 88
#define SPECIES_MUK 89
#define SPECIES_SHELLDER 90
#define SPECIES_CLOYSTER 91
#define SPECIES_GASTLY 92
#define SPECIES_HAUNTER 93
#define SPECIES_GENGAR 94
#define SPECIES_ONIX 95
#define SPECIES_DROWZEE 96
#define SPECIES_HYPNO 97
#define SPECIES_KRABBY 98
#define SPECIES_KINGLER 99
#define SPECIES_VOLTORB 100
#define SPECIES_ELECTRODE 101
#define SPECIES_EXEGGCUTE 102
#define SPECIES_EXEGGUTOR 103
#define SPECIES_CUBONE 104
#define SPECIES_MAROWAK 105
#define SPECIES_HITMONLEE 106
#define SPECIES_HITMONCHAN 107
#define SPECIES_LICKITUNG 108
#define SPECIES_KOFFING 109
#define SPECIES_WEEZING 110
#define SPECIES_RHYHORN 111
#define SPECIES_RHYDON 112
#define SPECIES_CHANSEY 113
#define SPECIES_TANGELA 114
#define SPECIES_KANGASKHAN 115
#define SPECIES_HORSEA 116
#define SPECIES_SEADRA 117
#define SPECIES_GOLDEEN 118
#define SPECIES_SEAKING 119
#define SPECIES_STARYU 120
#define SPECIES_STARMIE 121
#define SPECIES_MR_MIME 122
#define SPECIES_SCYTHER 123
#define SPECIES_JYNX 124
#define SPECIES_ELECTABUZZ 125
#define SPECIES_MAGMAR 126
#define SPECIES_PINSIR 127
#define SPECIES_TAUROS 128
#define SPECIES_MAGIKARP 129
#define SPECIES_GYARADOS 130
#define SPECIES_LAPRAS 131
#define SPECIES_DITTO 132
#define SPECIES_EEVEE 133
#define SPECIES_VAPOREON 134
#define SPECIES_JOLTEON 135
#define SPECIES_FLAREON 136
#define SPECIES_PORYGON 137
#define SPECIES_OMANYTE 138
#define SPECIES_OMASTAR 139
#define SPECIES_KABUTO 140
#define SPECIES_KABUTOPS 141
#define SPECIES_AERODACTYL 142
#define SPECIES_SNORLAX 143
#define SPECIES_ARTICUNO 144
#define SPECIES_ZAPDOS 145
#define SPECIES_MOLTRES 146
#define SPECIES_DRATINI 147
#define SPECIES_DRAGONAIR 148
#define SPECIES_DRAGONITE 149
#define SPECIES_MEWTWO 150
#define SPECIES_MEW 151

#define KANTO_SPECIES_END SPECIES_MEW

#define SPECIES_CHIKORITA 152
#define SPECIES_BAYLEEF 153
#define SPECIES_MEGANIUM 154
#define SPECIES_CYNDAQUIL 155
#define SPECIES_QUILAVA 156
#define SPECIES_TYPHLOSION 157
#define SPECIES_TOTODILE 158
#define SPECIES_CROCONAW 159
#define SPECIES_FERALIGATR 160
#define SPECIES_SENTRET 161
#define SPECIES_FURRET 162
#define SPECIES_HOOTHOOT 163
#define SPECIES_NOCTOWL 164
#define SPECIES_LEDYBA 165
#define SPECIES_LEDIAN 166
#define SPECIES_SPINARAK 167
#define SPECIES_ARIADOS 168
#define SPECIES_CROBAT 169
#define SPECIES_CHINCHOU 170
#define SPECIES_LANTURN 171
#define SPECIES_PICHU 172
#define SPECIES_CLEFFA 173
#define SPECIES_IGGLYBUFF 174
#define SPECIES_TOGEPI 175
#define SPECIES_TOGETIC 176
#define SPECIES_NATU 177
#define SPECIES_XATU 178
#define SPECIES_MAREEP 179
#define SPECIES_FLAAFFY 180
#define SPECIES_AMPHAROS 181
#define SPECIES_BELLOSSOM 182
#define SPECIES_MARILL 183
#define SPECIES_AZUMARILL 184
#define SPECIES_SUDOWOODO 185
#define SPECIES_POLITOED 186
#define SPECIES_HOPPIP 187
#define SPECIES_SKIPLOOM 188
#define SPECIES_JUMPLUFF 189
#define SPECIES_AIPOM 190
#define SPECIES_SUNKERN 191
#define SPECIES_SUNFLORA 192
#define SPECIES_YANMA 193
#define SPECIES_WOOPER 194
#define SPECIES_QUAGSIRE 195
#define SPECIES_ESPEON 196
#define SPECIES_UMBREON 197
#define SPECIES_MURKROW 198
#define SPECIES_SLOWKING 199
#define SPECIES_MISDREAVUS 200
#define SPECIES_UNOWN 201
#define SPECIES_WOBBUFFET 202
#define SPECIES_GIRAFARIG 203
#define SPECIES_PINECO 204
#define SPECIES_FORRETRESS 205
#define SPECIES_DUNSPARCE 206
#define SPECIES_GLIGAR 207
#define SPECIES_STEELIX 208
#define SPECIES_SNUBBULL 209
#define SPECIES_GRANBULL 210
#define SPECIES_QWILFISH 211
#define SPECIES_SCIZOR 212
#define SPECIES_SHUCKLE 213
#define SPECIES_HERACROSS 214
#define SPECIES_SNEASEL 215
#define SPECIES_TEDDIURSA 216
#define SPECIES_URSARING 217
#define SPECIES_SLUGMA 218
#define SPECIES_MAGCARGO 219
#define SPECIES_SWINUB 220
#define SPECIES_PILOSWINE 221
#define SPECIES_CORSOLA 222
#define SPECIES_REMORAID 223
#define SPECIES_OCTILLERY 224
#define SPECIES_DELIBIRD 225
#define SPECIES_MANTINE 226
#define SPECIES_SKARMORY 227
#define SPECIES_HOUNDOUR 228
#define SPECIES_HOUNDOOM 229
#define SPECIES_KINGDRA 230
#define SPECIES_PHANPY 231
#define SPECIES_DONPHAN 232
#define SPECIES_PORYGON2 233
#define SPECIES_STANTLER 234
#define SPECIES_SMEARGLE 235
#define SPECIES_TYROGUE 236
#define SPECIES_HITMONTOP 237
#define SPECIES_SMOOCHUM 238
#define SPECIES_ELEKID 239
#define SPECIES_MAGBY 240
#define SPECIES_MILTANK 241
#define SPECIES_BLISSEY 242
#define SPECIES_RAIKOU 243
#define SPECIES_ENTEI 244
#define SPECIES_SUICUNE 245
#define SPECIES_LARVITAR 246
#define SPECIES_PUPITAR 247
#define SPECIES_TYRANITAR 248
#define SPECIES_LUGIA 249
#define SPECIES_HO_OH 250
#define SPECIES_CELEBI 251

#define SPECIES_OLD_UNOWN_B 252
#define SPECIES_OLD_UNOWN_C 253
#define SPECIES_OLD_UNOWN_D 254
#define SPECIES_OLD_UNOWN_E 255
#define SPECIES_OLD_UNOWN_F 256
#define SPECIES_OLD_UNOWN_G 257
#define SPECIES_OLD_UNOWN_H 258
#define SPECIES_OLD_UNOWN_I 259
#define SPECIES_OLD_UNOWN_J 260
#define SPECIES_OLD_UNOWN_K 261
#define SPECIES_OLD_UNOWN_L 262
#define SPECIES_OLD_UNOWN_M 263
#define SPECIES_OLD_UNOWN_N 264
#define SPECIES_OLD_UNOWN_O 265
#define SPECIES_OLD_UNOWN_P 266
#define SPECIES_OLD_UNOWN_Q 267
#define SPECIES_OLD_UNOWN_R 268
#define SPECIES_OLD_UNOWN_S 269
#define SPECIES_OLD_UNOWN_T 270
#define SPECIES_OLD_UNOWN_U 271
#define SPECIES_OLD_UNOWN_V 272
#define SPECIES_OLD_UNOWN_W 273
#define SPECIES_OLD_UNOWN_X 274
#define SPECIES_OLD_UNOWN_Y 275
#define SPECIES_OLD_UNOWN_Z 276

#define SPECIES_TREECKO 277
#define SPECIES_GROVYLE 278
#define SPECIES_SCEPTILE 279
#define SPECIES_TORCHIC 280
#define SPECIES_COMBUSKEN 281
#define SPECIES_BLAZIKEN 282
#define SPECIES_MUDKIP 283
#define SPECIES_MARSHTOMP 284
#define SPECIES_SWAMPERT 285
#define SPECIES_POOCHYENA 286
#define SPECIES_MIGHTYENA 287
#define SPECIES_ZIGZAGOON 288
#define SPECIES_LINOONE 289
#define SPECIES_WURMPLE 290
#define SPECIES_SILCOON 291
#define SPECIES_BEAUTIFLY 292
#define SPECIES_CASCOON 293
#define SPECIES_DUSTOX 294
#define SPECIES_LOTAD 295
#define SPECIES_LOMBRE 296
#define SPECIES_LUDICOLO 297
#define SPECIES_SEEDOT 298
#define SPECIES_NUZLEAF 299
#define SPECIES_SHIFTRY 300
#define SPECIES_NINCADA 301
#define SPECIES_NINJASK 302
#define SPECIES_SHEDINJA 303
#define SPECIES_TAILLOW 304
#define SPECIES_SWELLOW 305
#define SPECIES_SHROOMISH 306
#define SPECIES_BRELOOM 307
#define SPECIES_SPINDA 308
#define SPECIES_WINGULL 309
#define SPECIES_PELIPPER 310
#define SPECIES_SURSKIT 311
#define SPECIES_MASQUERAIN 312
#define SPECIES_WAILMER 313
#define SPECIES_WAILORD 314
#define SPECIES_SKITTY 315
#define SPECIES_DELCATTY 316
#define SPECIES_KECLEON 317
#define SPECIES_BALTOY 318
#define SPECIES_CLAYDOL 319
#define SPECIES_NOSEPASS 320
#define SPECIES_TORKOAL 321
#define SPECIES_SABLEYE 322
#define SPECIES_BARBOACH 323
#define SPECIES_WHISCASH 324
#define SPECIES_LUVDISC 325
#define SPECIES_CORPHISH 326
#define SPECIES_CRAWDAUNT 327
#define SPECIES_FEEBAS 328
#define SPECIES_MILOTIC 329
#define SPECIES_CARVANHA 330
#define SPECIES_SHARPEDO 331
#define SPECIES_TRAPINCH 332
#define SPECIES_VIBRAVA 333
#define SPECIES_FLYGON 334
#define SPECIES_MAKUHITA 335
#define SPECIES_HARIYAMA 336
#define SPECIES_ELECTRIKE 337
#define SPECIES_MANECTRIC 338
#define SPECIES_NUMEL 339
#define SPECIES_CAMERUPT 340
#define SPECIES_SPHEAL 341
#define SPECIES_SEALEO 342
#define SPECIES_WALREIN 343
#define SPECIES_CACNEA 344
#define SPECIES_CACTURNE 345
#define SPECIES_SNORUNT 346
#define SPECIES_GLALIE 347
#define SPECIES_LUNATONE 348
#define SPECIES_SOLROCK 349
#define SPECIES_AZURILL 350
#define SPECIES_SPOINK 351
#define SPECIES_GRUMPIG 352
#define SPECIES_PLUSLE 353
#define SPECIES_MINUN 354
#define SPECIES_MAWILE 355
#define SPECIES_MEDITITE 356
#define SPECIES_MEDICHAM 357
#define SPECIES_SWABLU 358
#define SPECIES_ALTARIA 359
#define SPECIES_WYNAUT 360
#define SPECIES_DUSKULL 361
#define SPECIES_DUSCLOPS 362
#define SPECIES_ROSELIA 363
#define SPECIES_SLAKOTH 364
#define SPECIES_VIGOROTH 365
#define SPECIES_SLAKING 366
#define SPECIES_GULPIN 367
#define SPECIES_SWALOT 368
#define SPECIES_TROPIUS 369
#define SPECIES_WHISMUR 370
#define SPECIES_LOUDRED 371
#define SPECIES_EXPLOUD 372
#define SPECIES_CLAMPERL 373
#define SPECIES_HUNTAIL 374
#define SPECIES_GOREBYSS 375
#define SPECIES_ABSOL 376
#define SPECIES_SHUPPET 377
#define SPECIES_BANETTE 378
#define SPECIES_SEVIPER 379
#define SPECIES_ZANGOOSE 380
#define SPECIES_RELICANTH 381
#define SPECIES_ARON 382
#define SPECIES_LAIRON 383
#define SPECIES_AGGRON 384
#define SPECIES_CASTFORM 385
#define SPECIES_VOLBEAT 386
#define SPECIES_ILLUMISE 387
#define SPECIES_LILEEP 388
#define SPECIES_CRADILY 389
#define SPECIES_ANORITH 390
#define SPECIES_ARMALDO 391
#define SPECIES_RALTS 392
#define SPECIES_KIRLIA 393
#define SPECIES_GARDEVOIR 394
#define SPECIES_BAGON 395
#define SPECIES_SHELGON 396
#define SPECIES_SALAMENCE 397
#define SPECIES_BELDUM 398
#define SPECIES_METANG 399
#define SPECIES_METAGROSS 400
#define SPECIES_REGIROCK 401
#define SPECIES_REGICE 402
#define SPECIES_REGISTEEL 403
#define SPECIES_KYOGRE 404
#define SPECIES_GROUDON 405
#define SPECIES_RAYQUAZA 406
#define SPECIES_LATIAS 407
#define SPECIES_LATIOS 408
#define SPECIES_JIRACHI 409
#define SPECIES_DEOXYS 410
#define SPECIES_CHIMECHO 411
#define SPECIES_EGG 412

#define NUM_SPECIES SPECIES_EGG

#define SPECIES_UNOWN_B (NUM_SPECIES + 1)
#define SPECIES_UNOWN_C (NUM_SPECIES + 2)
#define SPECIES_UNOWN_D (NUM_SPECIES + 3)
#define SPECIES_UNOWN_E (NUM_SPECIES + 4)
#define SPECIES_UNOWN_F (NUM_SPECIES + 5)
#define SPECIES_UNOWN_G (NUM_SPECIES + 6)
#define SPECIES_UNOWN_H (NUM_SPECIES + 7)
#define SPECIES_UNOWN_I (NUM_SPECIES + 8)
#define SPECIES_UNOWN_J (NUM_SPECIES + 9)
#define SPECIES_UNOWN_K (NUM_SPECIES + 10)
#define SPECIES_UNOWN_L (NUM_SPECIES + 11)
#define SPECIES_UNOWN_M (NUM_SPECIES + 12)
#define SPECIES_UNOWN_N (NUM_SPECIES + 13)
#define SPECIES_UNOWN_O (NUM_SPECIES + 14)
#define SPECIES_UNOWN_P (NUM_SPECIES + 15)
#define SPECIES_UNOWN_Q (NUM_SPECIES + 16)
#define SPECIES_UNOWN_R (NUM_SPECIES + 17)
#define SPECIES_UNOWN_S (NUM_SPECIES + 18)
#define SPECIES_UNOWN_T (NUM_SPECIES + 19)
#define SPECIES_UNOWN_U (NUM_SPECIES + 20)
#define SPECIES_UNOWN_V (NUM_SPECIES + 21)
#define SPECIES_UNOWN_W (NUM_SPECIES + 22)
#define SPECIES_UNOWN_X (NUM_SPECIES + 23)
#define SPECIES_UNOWN_Y (NUM_SPECIES + 24)
#define SPECIES_UNOWN_Z (NUM_SPECIES + 25)
#define SPECIES_UNOWN_EMARK (NUM_SPECIES + 26)
#define SPECIES_UNOWN_QMARK (NUM_SPECIES + 27)

#endif  // GUARD_CONSTANTS_SPECIES_H
