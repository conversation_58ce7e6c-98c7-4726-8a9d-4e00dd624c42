BWXP_ENEMY_LEVEL EQU $D0FC
BWXP_ENEMY_SPECIES EQU $D0EF
BWXP_MAX_LEVEL EQU 100
BWXP_BATTLE_TYPE EQU $D116
BWXP_MULTIPLICAND EQU $FFB5
BWXP_MULTIPLIER EQU $FFB9
BWXP_DIVIDEND EQU BWXP_MULTIPLICAND
BWXP_DIVISOR EQU BWXP_MULTIPLIER
BWXP_INBUILT_MULTIPLY EQU $31BE 
BWXP_INBUILT_DIVIDE EQU $31C9 
BWXP_INBUILT_PARTYPARAMLOC EQU $3B3A
BWXP_PARTYPARAM_LEVEL EQU $1F
BWXP_PARTYPARAM_TID EQU $06
BWXP_PARTYPARAM_HELDITEM EQU $01
BWXP_PARTYPARAM_HP EQU $02
BWXP_NUM_PARTICIPANTS EQU $D151
BWXP_SCRATCH5B_1 EQU $D0F1
BWXP_SCRATCH5B_2 EQU $D0F6
BWXP_SCRATCH1B EQU $D0FB
BWXP_MULTIPLIER_STOR EQU $FFBB
BWXP_BIG_MULTIPLICAND EQU $FFB4
BWXP_BIG_DIVIDEND EQU BWXP_BIG_MULTIPLICAND
BWXP_PLAYER_TID EQU $D1A1
BWXP_BOOSTED_EXP_FLAG EQU $CF80
BWXP_DIVIDEEXP_RETURN_POINT EQU $6F13
BWXP_PARTYCOUNT EQU $DA22
BWXP_PARTYMON1 EQU $DA2A
BWXP_PARTYMON2 EQU $DA5A
BWXP_MAIN_RETURN_POINT EQU $6D35
BWXP_EXPADDER_RETURN_POINT EQU $6D69
BWXP_UNKNOWNFUNC1 EQU $6F5F
BWXP_UNKNOWNFUNC2 EQU $3158