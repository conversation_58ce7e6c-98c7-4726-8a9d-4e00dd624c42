#ifndef GUARD_METATILE_BEHAVIORS
#define GUARD_METATILE_BEHAVIORS

#define MB_NORMAL 0x00
#define MB_UNUSED_01 0x01
#define MB_TALL_GRASS 0x02
//
#define MB_CAVE 0x08
//
#define MB_RUNNING_DISALLOWED 0x0A
#define MB_INDOOR_ENCOUNTER 0x0B
#define MB_MOUNTAIN_TOP 0x0C
//
#define MB_POND_WATER 0x10
#define MB_FAST_WATER 0x11
#define MB_DEEP_WATER 0x12
#define MB_WATERFALL 0x13
//
#define MB_OCEAN_WATER 0x15
#define MB_PUDDLE 0x16
#define MB_SHALLOW_WATER 0x17
//
#define MB_UNDERWATER_BLOCKED_ABOVE 0x19
#define MB_UNUSED_WATER 0x1A
#define MB_CYCLING_ROAD_WATER 0x1B
//
#define MB_STRENGTH_BUTTON 0x20
#define MB_SAND 0x21
#define MB_SEAWEED 0x22
#define MB_ICE 0x23
//
#define MB_THIN_ICE 0x26
#define MB_CRACKED_ICE 0x27
#define MB_HOT_SPRINGS 0x28
//
#define MB_ROCK_STAIRS 0x2A
#define MB_SAND_CAVE 0x2B
//
#define MB_IMPASSABLE_EAST 0x30
#define MB_IMPASSABLE_WEST 0x31
#define MB_IMPASSABLE_NORTH 0x32
#define MB_IMPASSABLE_SOUTH 0x33
#define MB_IMPASSABLE_NORTHEAST 0x34
#define MB_IMPASSABLE_NORTHWEST 0x35
#define MB_IMPASSABLE_SOUTHEAST 0x36
#define MB_IMPASSABLE_SOUTHWEST 0x37
#define MB_JUMP_EAST 0x38
#define MB_JUMP_WEST 0x39
#define MB_JUMP_NORTH 0x3A
#define MB_JUMP_SOUTH 0x3B
//
#define MB_WALK_EAST 0x40
#define MB_WALK_WEST 0x41
#define MB_WALK_NORTH 0x42
#define MB_WALK_SOUTH 0x43
#define MB_SLIDE_EAST 0x44
#define MB_SLIDE_WEST 0x45
#define MB_SLIDE_NORTH 0x46
#define MB_SLIDE_SOUTH 0x47
#define MB_TRICK_HOUSE_PUZZLE_8_FLOOR 0x48
//
#define MB_EASTWARD_CURRENT 0x50
#define MB_WESTWARD_CURRENT 0x51
#define MB_NORTHWARD_CURRENT 0x52
#define MB_SOUTHWARD_CURRENT 0x53
#define MB_SPIN_RIGHT 0x54
#define MB_SPIN_LEFT 0x55
#define MB_SPIN_UP 0x56
#define MB_SPIN_DOWN 0x57
#define MB_STOP_SPINNING 0x58
//
#define MB_CAVE_DOOR 0x60
#define MB_LADDER 0x61
#define MB_EAST_ARROW_WARP 0x62
#define MB_WEST_ARROW_WARP 0x63
#define MB_NORTH_ARROW_WARP 0x64
#define MB_SOUTH_ARROW_WARP 0x65
#define MB_FALL_WARP 0x66
#define MB_REGULAR_WARP 0x67
#define MB_LAVARIDGE_1F_WARP 0x68
#define MB_WARP_DOOR 0x69
#define MB_UP_ESCALATOR 0x6A
#define MB_DOWN_ESCALATOR 0x6B
#define MB_UP_RIGHT_STAIR_WARP 0x6C
#define MB_UP_LEFT_STAIR_WARP 0x6D
#define MB_DOWN_RIGHT_STAIR_WARP 0x6E
#define MB_DOWN_LEFT_STAIR_WARP 0x6F
//
#define MB_UNION_ROOM_WARP 0x71
//
#define MB_COUNTER 0x80
#define MB_BOOKSHELF 0x81
#define MB_POKEMART_SHELF 0x82
#define MB_PC 0x83
#define MB_SIGNPOST 0x84
#define MB_REGION_MAP 0x85
#define MB_TELEVISION 0x86
#define MB_POKEMON_CENTER_SIGN 0x87
#define MB_POKEMART_SIGN 0x88
#define MB_CABINET 0x89
#define MB_KITCHEN 0x8A
#define MB_DRESSER 0x8B
#define MB_SNACKS 0x8C
#define MB_CABLE_CLUB_WIRELESS_MONITOR 0x8D
#define MB_BATTLE_RECORDS 0x8E
#define MB_QUESTIONNAIRE 0x8F
#define MB_FOOD 0x90
#define MB_INDIGO_PLATEAU_SIGN_1 0x91
#define MB_INDIGO_PLATEAU_SIGN_2 0x92
#define MB_BLUEPRINTS 0x93
#define MB_PAINTING 0x94
#define MB_POWER_PLANT_MACHINE 0x95
#define MB_TELEPHONE 0x96
#define MB_COMPUTER 0x97
#define MB_ADVERTISING_POSTER 0x98
#define MB_FOOD_SMELLS_TASTY 0x99
#define MB_TRASH_BIN 0x9A
#define MB_CUP 0x9B
#define MB_PORTHOLE 0x9C
#define MB_WINDOW 0x9D
#define MB_BLINKING_LIGHTS 0x9E
#define MB_NEATLY_LINED_UP_TOOLS 0x9F
#define MB_IMPRESSIVE_MACHINE 0xA0
#define MB_VIDEO_GAME 0xA1
#define MB_BURGLARY 0xA2
#define MB_TRAINER_TOWER_MONITOR 0xA3
//
#define MB_CYCLING_ROAD_PULL_DOWN 0xD0
#define MB_CYCLING_ROAD_PULL_DOWN_GRASS 0xD1
//
#define NUM_METATILE_BEHAVIORS 0xF0

#endif // GUARD_METATILE_BEHAVIORS
