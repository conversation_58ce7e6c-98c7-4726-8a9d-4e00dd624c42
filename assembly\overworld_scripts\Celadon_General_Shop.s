.align 2
.thumb

.include "../xse_commands.s"
.include "../xse_defines.s"

.global CeladonGeneralShop_Items
.global EventScript_CeladonGeneralShop

@ Complete General Supplies list for Celadon Department Store 2F South
@ ALL new supply items from CFRU including enhanced healing, berries, Pokeballs, and special items
CeladonGeneralShop_Items:
    @ === POKEBALLS (All new types) ===
    .hword 4        @ ITEM_POKE_BALL
    .hword 3        @ ITEM_GREAT_BALL
    .hword 2        @ ITEM_ULTRA_BALL
    .hword 6        @ ITEM_NET_BALL
    .hword 7        @ ITEM_DIVE_BALL
    .hword 8        @ ITEM_NEST_BALL
    .hword 9        @ ITEM_REPEAT_BALL
    .hword 10       @ ITEM_TIMER_BALL
    .hword 11       @ ITEM_LUXURY_BALL
    @ .hword 12       @ ITEM_PREMIER_BALL
    .hword 240      @ ITEM_DUSK_BALL
    .hword 241      @ ITEM_HEAL_BALL
    .hword 242      @ ITEM_QUICK_BALL
    .hword 243      @ ITEM_FAST_BALL
    .hword 244      @ ITEM_LEVEL_BALL
    .hword 245      @ ITEM_LURE_BALL
    .hword 246      @ ITEM_HEAVY_BALL
    .hword 247      @ ITEM_LOVE_BALL
    .hword 248      @ ITEM_FRIEND_BALL
    .hword 249      @ ITEM_MOON_BALL
    
    @ === HEALING ITEMS (Enhanced + Original) ===
    .hword 13       @ ITEM_POTION
    .hword 22       @ ITEM_SUPER_POTION
    .hword 21       @ ITEM_HYPER_POTION
@    .hword 20       @ ITEM_MAX_POTION
@    .hword 19       @ ITEM_FULL_RESTORE
    .hword 24       @ ITEM_REVIVE
@    .hword 25       @ ITEM_MAX_REVIVE
    .hword 23       @ ITEM_FULL_HEAL
    .hword 26       @ ITEM_FRESH_WATER
    .hword 27       @ ITEM_SODA_POP
    .hword 28       @ ITEM_LEMONADE
    .hword 29       @ ITEM_MOOMOO_MILK
    
    @ === STATUS CONDITION ITEMS ===
    .hword 14       @ ITEM_ANTIDOTE
    .hword 15       @ ITEM_BURN_HEAL
    .hword 16       @ ITEM_ICE_HEAL
    .hword 17       @ ITEM_AWAKENING
    .hword 18       @ ITEM_PARALYZE_HEAL
    
    @ === PP RESTORATION ===
    .hword 34       @ ITEM_ETHER
    @ .hword 35       @ ITEM_MAX_ETHER
    .hword 36       @ ITEM_ELIXIR
    @ .hword 37       @ ITEM_MAX_ELIXIR

    @ === SPECIAL ITEMS (Soothe Bell, Shell Bell, etc.) ===
    .hword 184      @ ITEM_SOOTHE_BELL
    .hword 219      @ ITEM_SHELL_BELL
@    .hword 189      @ ITEM_AMULET_COIN
@    .hword 197      @ ITEM_LUCKY_EGG
@    .hword 182      @ ITEM_EXP_SHARE
    .hword 183      @ ITEM_QUICK_CLAW
    .hword 196      @ ITEM_FOCUS_BAND
    .hword 198      @ ITEM_SCOPE_LENS
@    .hword 200      @ ITEM_LEFTOVERS
    .hword 190      @ ITEM_CLEANSE_TAG
    .hword 194      @ ITEM_SMOKE_BALL
    .hword 195      @ ITEM_EVERSTONE
    
    @ === BATTLE ITEMS ===
    .hword 75       @ ITEM_X_ATTACK
    .hword 76       @ ITEM_X_DEFEND
    .hword 77       @ ITEM_X_SPEED
    .hword 78       @ ITEM_X_ACCURACY
    .hword 79       @ ITEM_X_SPECIAL
    .hword 74       @ ITEM_DIRE_HIT
    .hword 73       @ ITEM_GUARD_SPEC
    
    @ === REPELS ===
    .hword 86       @ ITEM_REPEL
    .hword 83       @ ITEM_SUPER_REPEL
    .hword 84       @ ITEM_MAX_REPEL

    @ === ESCAPE ITEMS ===
    .hword 85       @ ITEM_ESCAPE_ROPE
    .hword 80       @ ITEM_POKE_DOLL
    .hword 81       @ ITEM_FLUFFY_TAIL
    
    @ === BERRIES (Essential ones for healing/status) ===
    .hword 133      @ ITEM_CHERI_BERRY (Paralysis)
    .hword 134      @ ITEM_CHESTO_BERRY (Sleep)
    .hword 135      @ ITEM_PECHA_BERRY (Poison)
    .hword 136      @ ITEM_RAWST_BERRY (Burn)
    .hword 137      @ ITEM_ASPEAR_BERRY (Freeze)
    .hword 138      @ ITEM_LEPPA_BERRY (PP restore)
    .hword 139      @ ITEM_ORAN_BERRY (HP restore)
    .hword 140      @ ITEM_PERSIM_BERRY (Confusion)
    .hword 141      @ ITEM_LUM_BERRY (All status)
    .hword 142      @ ITEM_SITRUS_BERRY (HP restore)
    
    @ === NEW CFRU BERRIES (Type-resist berries) ===
    .hword 0x1BE    @ ITEM_OCCA_BERRY (Fire resist)
    .hword 0x1BF    @ ITEM_PASSHO_BERRY (Water resist)
    .hword 0x1C0    @ ITEM_WACAN_BERRY (Electric resist)
    .hword 0x1C1    @ ITEM_RINDO_BERRY (Grass resist)
    .hword 0x1C2    @ ITEM_YACHE_BERRY (Ice resist)
    .hword 0x1C3    @ ITEM_CHOPLE_BERRY (Fighting resist)
    .hword 0x1C4    @ ITEM_KEBIA_BERRY (Poison resist)
    .hword 0x1C5    @ ITEM_SHUCA_BERRY (Ground resist)
    .hword 0x1C6    @ ITEM_COBA_BERRY (Flying resist)
    .hword 0x1C7    @ ITEM_PAYAPA_BERRY (Psychic resist)
    .hword 0x1C8    @ ITEM_TANGA_BERRY (Bug resist)
    .hword 0x1C9    @ ITEM_CHARTI_BERRY (Rock resist)
    .hword 0x1CA    @ ITEM_KASIB_BERRY (Ghost resist)
    .hword 0x1CB    @ ITEM_HABAN_BERRY (Dragon resist)
    .hword 0x1CC    @ ITEM_COLBUR_BERRY (Dark resist)
    .hword 0x1CD    @ ITEM_BABIRI_BERRY (Steel resist)
    .hword 0x1CE    @ ITEM_CHILAN_BERRY (Normal resist)
    .hword 0x1D3    @ ITEM_ROSELI_BERRY (Fairy resist)
    
    @ === VITAMINS ===
    .hword 63       @ ITEM_HP_UP
    .hword 64       @ ITEM_PROTEIN
    .hword 65       @ ITEM_IRON
    .hword 66       @ ITEM_CARBOS
    .hword 67       @ ITEM_CALCIUM
    .hword 68       @ ITEM_RARE_CANDY
    .hword 69       @ ITEM_PP_UP
    .hword 70       @ ITEM_ZINC
    .hword 71       @ ITEM_PP_MAX
    
    @ === NEW CFRU ITEMS ===
    .hword 0x27F    @ ITEM_BOTTLE_CAP
    .hword 0x280    @ ITEM_GOLD_BOTTLE_CAP
    .hword 0x2D8    @ ITEM_ABILITY_CAPSULE
    .hword 0x2D9    @ ITEM_ABILITY_PATCH
         
    @ === FOOD ITEMS ===
    .hword 116      @ ITEM_HONEY
    .hword 82       @ ITEM_BIG_MALASADA
    .hword 54       @ ITEM_CASTELIACONE
    .hword 55       @ ITEM_LUMIOSE_GALETTE
    .hword 52       @ ITEM_RAGE_CANDY_BAR
    .hword 56       @ ITEM_SHALOUR_SABLE
    .hword 53       @ ITEM_OLD_GATEAU
    
    .hword 0x0000   @ End of list

.align 2

@ Script for Celadon General Supplies Shop
EventScript_CeladonGeneralShop:
    faceplayer
    lock
    msgbox gText_CeladonGeneralShop_Welcome MSG_NORMAL
    pokemart CeladonGeneralShop_Items
    msgbox gText_CeladonGeneralShop_Goodbye MSG_NORMAL
    release
    end
