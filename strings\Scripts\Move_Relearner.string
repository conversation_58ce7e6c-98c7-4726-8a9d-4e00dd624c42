#org @gText_MoveRelearner_Welcome
Hello! I'm the Move Relearner!\pI can help your Pokémon remember\nmoves they've forgotten!

#org @gText_MoveRelearner_AskUse
Would you like me to help one of\nyour Pokémon remember a move?

#org @gText_MoveRelearner_PaymentInfo
Great! My service costs $1000.\pLet me check if you have enough\nmoney...

#org @gText_MoveRelearner_Thanks
There! Your Pokémon remembered\nthe move perfectly!\pCome back anytime if you need\nhelp with forgotten moves!

#org @gText_MoveRelearner_NoMons
Oh my! You don't have any\nPokémon with you!\pCome back when you have some\nPokémon to help!

#org @gText_MoveRelearner_Decline
No problem! Come back anytime\nif you change your mind!

#org @gText_MoveRelearner_NoHeartScale
I'm sorry, but I need a Heart Scale\nto help your Pokémon remember moves.\pPlease bring me a Heart Scale\nand I'll be happy to help!

#org @gText_MoveRelearner_WhichPokemon
Which POKéMON needs tutoring?

#org @gText_MoveRelearner_WhichMove
Which move should I teach?

#org @gText_MoveRelearner_Success
There! Your POKéMON learned\nthe move successfully!\pThat'll be $1000. Thank you!

#org @gText_MoveRelearner_NoMovesToLearn
Sorry!\pIt doesn't appear as if I have a\nmove I can teach that POKéMON.

#org @gText_MoveRelearner_NoMoney
I'm sorry, but you don't have\nenough money for my service.\pI need P1000 to help\nyour Pokémon remember moves.

#org @gText_MoveRelearner_AskAnother
Would you like me to teach another\nmove to one of your Pokémon?

#org @gText_MoveRelearner_Goodbye
Thank you for using my service!\pCome back anytime if you need\nhelp with forgotten moves!

#org @gText_MoveRelearner_TryAnother
Would you like to try with\nanother Pokémon instead?
