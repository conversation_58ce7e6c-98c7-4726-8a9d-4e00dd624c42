#ifndef GUARD_METATILE_LABELS_H
#define GUARD_METATILE_LABELS_H

// gTileset_Building
#define METATILE_Building_PCOff  0x062
#define METATILE_Building_PCOn   0x063

// gTileset_Cave
#define METATILE_Cave_Floor_Ledge_Bottom  0x2E1
#define METATILE_Cave_Floor_Ledge_Top     0x2D1
#define METATILE_Cave_RockBarrier_Bottom  0x317
#define METATILE_Cave_RockBarrier_Top     0x307

// gTileset_CeladonCity
#define METATILE_CeladonCity_CyclingRoad_Grass  0x352
#define METATILE_CeladonCity_CyclingRoad_Mowed  0x33E
#define METATILE_CeladonCity_DeptStoreDoor      0x294

// gTileset_CeruleanCity
#define METATILE_CeruleanCity_Door  0x298

// gTileset_CinnabarGym
#define METATILE_CinnabarGym_Floor                  0x281
#define METATILE_CinnabarGym_Floor_ShadeDiagonal    0x282
#define METATILE_CinnabarGym_Floor_ShadeFull        0x289
#define METATILE_CinnabarGym_Floor_WallLeftCorner   0x2C7
#define METATILE_CinnabarGym_Floor_WallLeftEdge     0x2CF
#define METATILE_CinnabarGym_Floor_WallRightCorner  0x2C6
#define METATILE_CinnabarGym_Floor_WallRightEdge    0x2CE
#define METATILE_CinnabarGym_Wall_RetractedBarrier  0x2D1

// gTileset_CinnabarIsland
#define METATILE_CinnabarIsland_LabDoor  0x2AD

// gTileset_DepartmentStore
#define METATILE_DepartmentStore_ElevatorDoor  0x28D

// gTileset_FuchsiaCity
#define METATILE_FuchsiaCity_Door                           0x2BF
#define METATILE_FuchsiaCity_SafariZoneDoor                 0x2D2
#define METATILE_FuchsiaCity_SafariZoneTreeTopLeft_Grass    0x300
#define METATILE_FuchsiaCity_SafariZoneTreeTopLeft_Mowed    0x310
#define METATILE_FuchsiaCity_SafariZoneTreeTopMiddle_Grass  0x301
#define METATILE_FuchsiaCity_SafariZoneTreeTopMiddle_Mowed  0x311
#define METATILE_FuchsiaCity_SafariZoneTreeTopRight_Grass   0x302
#define METATILE_FuchsiaCity_SafariZoneTreeTopRight_Mowed   0x312

// gTileset_GameCorner
#define METATILE_GameCorner_CheckeredFloor_ShadeLeft  0x2E1
#define METATILE_GameCorner_CounterBarrier            0x2F7
#define METATILE_GameCorner_Floor_ShadeFull           0x292
#define METATILE_GameCorner_Floor_StairsTop           0x29D
#define METATILE_GameCorner_PurpleWall_Floor          0x2CF
#define METATILE_GameCorner_PurpleWall_StairsTop      0x2A6
#define METATILE_GameCorner_PurpleWall_stairsBottom   0x2A7
#define METATILE_GameCorner_StairsBottom              0x29F
#define METATILE_GameCorner_StairsTop                 0x29E

// gTileset_General
#define METATILE_General_CalmWater               0x12B
#define METATILE_General_Door                    0x03D
#define METATILE_General_Plain_Grass             0x00D
#define METATILE_General_Plain_Mowed             0x001
#define METATILE_General_SlidingDoubleDoor       0x15B
#define METATILE_General_SlidingSingleDoor       0x062
#define METATILE_General_ThinTreeTop_Grass       0x00A
#define METATILE_General_ThinTreeTop_Mowed       0x013
#define METATILE_General_WideTreeTopLeft_Grass   0x00B
#define METATILE_General_WideTreeTopLeft_Mowed   0x00E
#define METATILE_General_WideTreeTopRight_Grass  0x00C
#define METATILE_General_WideTreeTopRight_Mowed  0x00F

// gTileset_GenericBuilding1
#define METATILE_GenericBuilding1_PlayersPCOff  0x28F
#define METATILE_GenericBuilding1_PlayersPCOn   0x28A

// gTileset_LavenderTown
#define METATILE_LavenderTown_Door  0x2A2

// gTileset_Mart
#define METATILE_Mart_CounterMid_Bottom  0x2C0
#define METATILE_Mart_CounterMid_Top     0x2BF

// gTileset_MtEmber
#define METATILE_MtEmber_CaveEntrance  0x346

// gTileset_PalletTown
#define METATILE_PalletTown_Door         0x2A3
#define METATILE_PalletTown_OaksLabDoor  0x2AC

// gTileset_PewterCity
#define METATILE_PewterCity_Door  0x2CE

// gTileset_PokemonCenter
#define METATILE_PokemonCenter_CableClubDoor                         0x2DE
#define METATILE_PokemonCenter_CounterBarrier                        0x2F9
#define METATILE_PokemonCenter_Escalator_BottomNextRail_Normal       0x2D0
#define METATILE_PokemonCenter_Escalator_BottomNextRail_Transition1  0x30A
#define METATILE_PokemonCenter_Escalator_BottomNextRail_Transition2  0x308
#define METATILE_PokemonCenter_Escalator_BottomNext_Normal           0x2D8
#define METATILE_PokemonCenter_Escalator_BottomNext_Transition1      0x312
#define METATILE_PokemonCenter_Escalator_BottomNext_Transition2      0x310
#define METATILE_PokemonCenter_Escalator_BottomRail_Normal           0x2D1
#define METATILE_PokemonCenter_Escalator_BottomRail_Transition1      0x30B
#define METATILE_PokemonCenter_Escalator_BottomRail_Transition2      0x309
#define METATILE_PokemonCenter_Escalator_Bottom_Normal               0x2D9
#define METATILE_PokemonCenter_Escalator_Bottom_Transition1          0x313
#define METATILE_PokemonCenter_Escalator_Bottom_Transition2          0x311
#define METATILE_PokemonCenter_Escalator_TopNextRail_Normal          0x2EB
#define METATILE_PokemonCenter_Escalator_TopNextRail_Transition1     0x31E
#define METATILE_PokemonCenter_Escalator_TopNextRail_Transition2     0x31C
#define METATILE_PokemonCenter_Escalator_TopNext_Normal              0x2E3
#define METATILE_PokemonCenter_Escalator_TopNext_Transition1         0x316
#define METATILE_PokemonCenter_Escalator_TopNext_Transition2         0x314
#define METATILE_PokemonCenter_Escalator_Top_Normal                  0x2E4
#define METATILE_PokemonCenter_Escalator_Top_Transition1             0x317
#define METATILE_PokemonCenter_Escalator_Top_Transition2             0x315
#define METATILE_PokemonCenter_Floor_ShadeLeft                       0x2C5
#define METATILE_PokemonCenter_NetworkMachine_Ruby                   0x35D
#define METATILE_PokemonCenter_NetworkMachine_Sapphire               0x35F
#define METATILE_PokemonCenter_NetworkMachine_ScreenLeft_On          0x35A
#define METATILE_PokemonCenter_NetworkMachine_ScreenRight_On         0x35B

// gTileset_PokemonLeague
#define METATILE_PokemonLeague_Door_Mid_Open             0x296
#define METATILE_PokemonLeague_Door_Top_Open             0x28E
#define METATILE_PokemonLeague_Entry_BottomLeft_Closed   0x2A5
#define METATILE_PokemonLeague_Entry_BottomMid_Closed    0x2A6
#define METATILE_PokemonLeague_Entry_BottomRight_Closed  0x2A7
#define METATILE_PokemonLeague_Entry_TopLeft_Closed      0x29D
#define METATILE_PokemonLeague_Entry_TopMid_Closed       0x29E
#define METATILE_PokemonLeague_Entry_TopRight_Closed     0x29F
#define METATILE_PokemonLeague_Floor_ShadeFull_Lance     0x311

// gTileset_PokemonMansion
#define METATILE_PokemonMansion_Barrier_Horizontal_BottomLeft            0x300
#define METATILE_PokemonMansion_Barrier_Horizontal_BottomLeft_Basement   0x354
#define METATILE_PokemonMansion_Barrier_Horizontal_BottomMid             0x301
#define METATILE_PokemonMansion_Barrier_Horizontal_BottomMid_Basement    0x355
#define METATILE_PokemonMansion_Barrier_Horizontal_BottomRight           0x302
#define METATILE_PokemonMansion_Barrier_Horizontal_BottomRight_Basement  0x356
#define METATILE_PokemonMansion_Barrier_Horizontal_TopLeft               0x2F8
#define METATILE_PokemonMansion_Barrier_Horizontal_TopMid                0x2F9
#define METATILE_PokemonMansion_Barrier_Horizontal_TopRight              0x2FA
#define METATILE_PokemonMansion_Barrier_Vertical_Bottom                  0x370
#define METATILE_PokemonMansion_Barrier_Vertical_Bottom_Basement         0x372
#define METATILE_PokemonMansion_Barrier_Vertical_Mid                     0x368
#define METATILE_PokemonMansion_Barrier_Vertical_MidShadow               0x360
#define METATILE_PokemonMansion_Barrier_Vertical_MidShadow_Basement      0x362
#define METATILE_PokemonMansion_Barrier_Vertical_Mid_Basement            0x36A
#define METATILE_PokemonMansion_Barrier_Vertical_TopBase                 0x358
#define METATILE_PokemonMansion_Barrier_Vertical_TopWall                 0x350
#define METATILE_PokemonMansion_BasementFloor                            0x286
#define METATILE_PokemonMansion_BasementFloor_ShadeFull                  0x287
#define METATILE_PokemonMansion_Floor                                    0x284
#define METATILE_PokemonMansion_Floor_ShadeFull                          0x285
#define METATILE_PokemonMansion_Statue_BlackEyes                         0x314
#define METATILE_PokemonMansion_Statue_BlackEyes_Basement                0x316
#define METATILE_PokemonMansion_Statue_BlackEyes_Shade                   0x315
#define METATILE_PokemonMansion_Statue_RedEyes                           0x34D
#define METATILE_PokemonMansion_Statue_RedEyes_Basement                  0x34F
#define METATILE_PokemonMansion_Statue_RedEyes_Shade                     0x34E
#define METATILE_PokemonMansion_Wall_EndCap                              0x2B5
#define METATILE_PokemonMansion_Wall_EndCap_Basement                     0x2B0
#define METATILE_PokemonMansion_Wall_EndPost_Bottom                      0x2AD
#define METATILE_PokemonMansion_Wall_EndPost_Mid                         0x2A5

// gTileset_SSAnne
#define METATILE_SSAnne_Door  0x281

// gTileset_SaffronCity
#define METATILE_SaffronCity_Door         0x284
#define METATILE_SaffronCity_SilphCoDoor  0x2BC

// gTileset_SeaCottage
#define METATILE_SeaCottage_Teleporter_CableBall_Bottom  0x2BA
#define METATILE_SeaCottage_Teleporter_CableBall_Top     0x2B9
#define METATILE_SeaCottage_Teleporter_Cable_Bottom      0x2B4
#define METATILE_SeaCottage_Teleporter_Cable_Top         0x285
#define METATILE_SeaCottage_Teleporter_Door              0x296
#define METATILE_SeaCottage_Teleporter_Door_FullGlowing  0x2B8
#define METATILE_SeaCottage_Teleporter_Door_HalfGlowing  0x2B7
#define METATILE_SeaCottage_Teleporter_Light_Green       0x28A
#define METATILE_SeaCottage_Teleporter_Light_Red         0x2B6
#define METATILE_SeaCottage_Teleporter_Light_Yellow      0x2B5

// gTileset_SeafoamIslands
#define METATILE_SeafoamIslands_CrackedIce    0x35A
#define METATILE_SeafoamIslands_IceHole       0x35B
#define METATILE_SeafoamIslands_SlidingIce    0x34B
#define METATILE_SeafoamIslands_UncrackedIce  0x359

// gTileset_SeviiIslands123
#define METATILE_SeviiIslands123_Door            0x297
#define METATILE_SeviiIslands123_GameCornerDoor  0x29B
#define METATILE_SeviiIslands123_PokeCenterDoor  0x2EB

// gTileset_SeviiIslands45
#define METATILE_SeviiIslands45_DayCareDoor                   0x2B9
#define METATILE_SeviiIslands45_Door                          0x29A
#define METATILE_SeviiIslands45_RocketWarehouseDoor_Locked    0x30B
#define METATILE_SeviiIslands45_RocketWarehouseDoor_Unlocked  0x2AF

// gTileset_SeviiIslands67
#define METATILE_SeviiIslands67_Door                   0x30C
#define METATILE_SeviiIslands67_DottedHoleDoor_Closed  0x357
#define METATILE_SeviiIslands67_DottedHoleDoor_Open    0x358

// gTileset_SilphCo
#define METATILE_SilphCo_Arrow_Down                     0x2A8
#define METATILE_SilphCo_Arrow_Left                     0x2A1
#define METATILE_SilphCo_Arrow_Right                    0x2A9
#define METATILE_SilphCo_Arrow_Up                       0x2A0
#define METATILE_SilphCo_ElevatorDoor                   0x2E2
#define METATILE_SilphCo_ElevatorWindow_Bottom0         0x2F8
#define METATILE_SilphCo_ElevatorWindow_Bottom1         0x2F9
#define METATILE_SilphCo_ElevatorWindow_Bottom2         0x2FA
#define METATILE_SilphCo_ElevatorWindow_Mid0            0x2F0
#define METATILE_SilphCo_ElevatorWindow_Mid1            0x2F1
#define METATILE_SilphCo_ElevatorWindow_Mid2            0x2F2
#define METATILE_SilphCo_ElevatorWindow_Top0            0x2E8
#define METATILE_SilphCo_ElevatorWindow_Top1            0x2E9
#define METATILE_SilphCo_ElevatorWindow_Top2            0x2EA
#define METATILE_SilphCo_Floor                          0x334
#define METATILE_SilphCo_Floor_ShadeFull                0x335
#define METATILE_SilphCo_Floor_WallLeftCorner           0x339
#define METATILE_SilphCo_Floor_WallRightCorner          0x33A
#define METATILE_SilphCo_HideoutBarrier_BottomLeft      0x2D6
#define METATILE_SilphCo_HideoutBarrier_BottomRight     0x2D7
#define METATILE_SilphCo_HideoutBarrier_TopLeft_Floor   0x2CE
#define METATILE_SilphCo_HideoutBarrier_TopLeft_Wall    0x2C6
#define METATILE_SilphCo_HideoutBarrier_TopRight_Floor  0x2CF
#define METATILE_SilphCo_HideoutBarrier_TopRight_Wall   0x2C7
#define METATILE_SilphCo_HideoutElevatorDoor            0x2AB
#define METATILE_SilphCo_HideoutFloor                   0x281
#define METATILE_SilphCo_HideoutFloor_ShadeFull         0x282
#define METATILE_SilphCo_HorizontalBarrier_BottomLeft   0x3B8
#define METATILE_SilphCo_HorizontalBarrier_BottomRight  0x3B9
#define METATILE_SilphCo_HorizontalBarrier_TopLeft      0x3B0
#define METATILE_SilphCo_HorizontalBarrier_TopRight     0x3B1
#define METATILE_SilphCo_VerticalBarrier_BottomLeft     0x3C4
#define METATILE_SilphCo_VerticalBarrier_BottomRight    0x3C5
#define METATILE_SilphCo_VerticalBarrier_MidLeft        0x3C2
#define METATILE_SilphCo_VerticalBarrier_MidRight       0x3C3
#define METATILE_SilphCo_VerticalBarrier_TopLeft        0x3C0
#define METATILE_SilphCo_VerticalBarrier_TopRight       0x3C1
#define METATILE_SilphCo_Wall_LeftEdge                  0x347
#define METATILE_SilphCo_Wall_RightEdge                 0x346

// gTileset_TrainerTower
#define METATILE_TrainerTower_CounterBarrier         0x2B4
#define METATILE_TrainerTower_Floor_ShadeBottomLeft  0x287
#define METATILE_TrainerTower_LobbyElevatorDoor      0x2C3
#define METATILE_TrainerTower_RoofElevatorDoor       0x356

// gTileset_VermilionCity
#define METATILE_VermilionCity_Door        0x29E
#define METATILE_VermilionCity_SSAnneWarp  0x2E1

// gTileset_VermilionGym
#define METATILE_VermilionGym_Beam_MidBottom                     0x28D
#define METATILE_VermilionGym_Beam_MidBottom_HalfOn              0x2C5
#define METATILE_VermilionGym_Beam_MidTop                        0x285
#define METATILE_VermilionGym_Beam_MidTop_HalfOn                 0x2BD
#define METATILE_VermilionGym_Beam_Node_BottomLeft_Edge_HalfOn   0x2C4
#define METATILE_VermilionGym_Beam_Node_BottomLeft_Edge_Off      0x29C
#define METATILE_VermilionGym_Beam_Node_BottomLeft_Edge_On       0x2B2
#define METATILE_VermilionGym_Beam_Node_BottomLeft_HalfOn        0x2C3
#define METATILE_VermilionGym_Beam_Node_BottomLeft_Off           0x29B
#define METATILE_VermilionGym_Beam_Node_BottomLeft_On            0x2B1
#define METATILE_VermilionGym_Beam_Node_BottomRight_Edge_HalfOn  0x2C6
#define METATILE_VermilionGym_Beam_Node_BottomRight_Edge_Off     0x29D
#define METATILE_VermilionGym_Beam_Node_BottomRight_Edge_On      0x2B3
#define METATILE_VermilionGym_Beam_Node_BottomRight_HalfOn       0x2C7
#define METATILE_VermilionGym_Beam_Node_BottomRight_Off          0x29E
#define METATILE_VermilionGym_Beam_Node_BottomRight_On           0x2B4
#define METATILE_VermilionGym_Beam_Node_TopLeft_Edge_HalfOn      0x2BC
#define METATILE_VermilionGym_Beam_Node_TopLeft_Edge_Off         0x294
#define METATILE_VermilionGym_Beam_Node_TopLeft_Edge_On          0x2AA
#define METATILE_VermilionGym_Beam_Node_TopLeft_HalfOn           0x2BB
#define METATILE_VermilionGym_Beam_Node_TopLeft_Off              0x293
#define METATILE_VermilionGym_Beam_Node_TopLeft_On               0x2A9
#define METATILE_VermilionGym_Beam_Node_TopRight_Edge_HalfOn     0x2BE
#define METATILE_VermilionGym_Beam_Node_TopRight_Edge_Off        0x295
#define METATILE_VermilionGym_Beam_Node_TopRight_Edge_On         0x2AB
#define METATILE_VermilionGym_Beam_Node_TopRight_HalfOn          0x2BF
#define METATILE_VermilionGym_Beam_Node_TopRight_Off             0x296
#define METATILE_VermilionGym_Beam_Node_TopRight_On              0x2AC
#define METATILE_VermilionGym_Floor                              0x281

// gTileset_ViridianCity
#define METATILE_ViridianCity_Door  0x299

// gTileset_ViridianForest
#define METATILE_ViridianForest_HugeTreeTopMiddle_Grass  0x284
#define METATILE_ViridianForest_HugeTreeTopMiddle_Mowed  0x281

// Other
#define METATILE_Fallarbor_AshGrass                      0x20A
#define METATILE_Fallarbor_NormalGrass                   0x212
#define METATILE_Lavaridge_NormalGrass                   0x206
#define METATILE_Pacifidlog_SkyPillar_CrackedFloor_Hole  0x237
#define METATILE_RSCave_CrackedFloor                     0x22F
#define METATILE_RSCave_CrackedFloor_Hole                0x206

#endif // GUARD_METATILE_LABELS_H
