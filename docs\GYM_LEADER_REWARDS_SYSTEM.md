# Gym Leader BONUS Rewards System

## Overview

This system provides **COMPLEMENTARY** bonus rewards for gym leaders that work **ALONGSIDE** the existing TM/badge system. It does NOT replace the original rewards - it adds extra items as bonus rewards.

## Features

- **Complementary System**: Works WITH existing TM/badge rewards, not instead of them
- **Bonus Items**: Gives extra items like type-boosting items, berries, and useful tools
- **Flag Management**: Prevents duplicate bonus rewards with separate flags
- **Flexible Integration**: Easy to add new gym leaders or modify existing bonus rewards
- **Text System**: Dynamic item name buffering for dialogue
- **Non-Intrusive**: Does not interfere with original game mechanics

## Implementation Details

### Core Files

1. **`include/gym_leader_rewards.h`** - Header with structures and function declarations
2. **`src/gym_leader_rewards.c`** - Implementation with data tables and functions
3. **`include/constants/flags.h`** - TM reward flags (updated)
4. **`assembly/overworld_scripts/Pewter_Gym.s`** - Example Brock implementation
5. **`assembly/overworld_scripts/Cerulean_Gym.s`** - Example Misty implementation

### Data Structure

```c
struct GymLeaderBonusReward {
    u16 trainerId;      // Trainer ID
    u16 bonusItem;      // Bonus item to give (in addition to normal TM)
    u16 quantity;       // Quantity of bonus item
    u16 flagReceived;   // Flag to prevent duplicate bonus
    const u8 *rewardText; // Bonus reward description text
};
```

### Gym Leader BONUS Mappings

| Leader | Trainer ID | Bonus Item | Qty | Purpose | Flag |
|--------|------------|------------|-----|---------|------|
| Brock | 0x19E | Razz Berry | 5 | Catching aid | FLAG_RECEIVED_BONUS_BROCK |
| Misty | 0x19F | Mystic Water | 1 | Water move boost | FLAG_RECEIVED_BONUS_MISTY |
| Lt. Surge | 0x1A0 | Magnet | 1 | Electric move boost | FLAG_RECEIVED_BONUS_LT_SURGE |
| Erika | 0x1A1 | Miracle Seed | 3 | Grass move boost | FLAG_RECEIVED_BONUS_ERIKA |
| Koga | 0x1A2 | Poison Barb | 1 | Poison move boost | FLAG_RECEIVED_BONUS_KOGA |
| Sabrina | 0x1A4 | Twisted Spoon | 1 | Psychic move boost | FLAG_RECEIVED_BONUS_SABRINA |
| Blaine | 0x1A3 | Charcoal | 1 | Fire move boost | FLAG_RECEIVED_BONUS_BLAINE |
| Giovanni | 0x15E | Soft Sand | 1 | Ground move boost | FLAG_RECEIVED_BONUS_GIOVANNI |

## Usage

### Basic Gym Leader Script (COMPLEMENTARY System)

```assembly
EventScript_GymLeader_Example:
    faceplayer
    checkflag FLAG_DEFEATED_LEADER
    if SET _goto LeaderAlreadyDefeated
    msgbox LeaderIntroText MSG_NORMAL
    trainerbattle1 0x1 TRAINER_ID 0x0 IntroText DefeatText PostBattleScript
    release
    end

PostBattleScript:
    // Normal badge and TM giving (EXISTING system - untouched)
    msgbox CongratulationsText MSG_NORMAL
    setflag FLAG_DEFEATED_LEADER
    // Badge and TM are given by the original game system here

    // BONUS reward system (NEW - complementary)
    checkflag FLAG_RECEIVED_BONUS_LEADER
    if NOT_SET _call GiveBonusReward

    msgbox BadgeExplanationText MSG_NORMAL
    release
    end

GiveBonusReward:
    msgbox BonusIntroText MSG_NORMAL
    callasm GiveGymLeaderBonusReward  // <- NEW function call
    msgbox BonusOutroText MSG_NORMAL
    return
```

### Key Functions

#### `GiveGymLeaderBonusReward()`
- Automatically determines which bonus item to give based on `gTrainerBattleOpponent_A`
- Checks if bonus was already received
- Sets appropriate flags
- Gives bonus item to player (COMPLEMENTARY to existing TM system)

#### `BufferGymLeaderBonusItemName()`
- Buffers bonus item name into `gStringVar1` for text display
- Used for dynamic text generation

#### `HasReceivedGymLeaderBonus(u16 trainerId)`
- Returns TRUE if player already received bonus from specific leader
- Useful for conditional dialogue

#### `GetGymLeaderBonusItem(u16 trainerId)`
- Returns item ID of bonus item for specific leader
- Returns ITEM_NONE if leader not found

## Adding New Gym Leaders

### 1. Update Data Table

Edit `src/gym_leader_rewards.c`:

```c
const struct GymLeaderReward sGymLeaderRewards[] = {
    // Existing entries...
    {TRAINER_NEW_LEADER, TM_NUMBER, FLAG_RECEIVED_TM_NEW, FLAG_BADGE_NEW, sText_TMNew},
};
```

### 2. Add Flags

Edit `include/constants/flags.h`:

```c
#define FLAG_RECEIVED_TM_NEW    (SYS_FLAGS + 0xXX)
```

### 3. Add Text

Edit `src/gym_leader_rewards.c`:

```c
static const u8 sText_TMNew[] = _("TM## contains MOVE NAME.\pUse it on a worthy POKéMON!");
```

### 4. Create Script

Create new `.s` file in `assembly/overworld_scripts/`:

```assembly
EventScript_NewGym_Leader:
    // Use standard pattern with callasm GiveGymLeaderTMReward
```

## Integration with Existing Systems

### Trainer Battle Types

The system works with any trainer battle type that supports post-battle scripts:
- `trainerbattle1` - Standard with post-battle script
- `trainerbattle2` - With continue script
- `trainerbattle6` - Double battle with script

### Flag System

Integrates with existing CFRU flag system:
- Uses `SYS_FLAGS` range for TM flags
- Compatible with existing badge flags
- Follows CFRU flag naming conventions

### Text System

Compatible with CFRU text system:
- Uses standard `msgbox` commands
- Supports text buffering with `gStringVar1`
- Follows CFRU text formatting

## Technical Notes

### Memory Addresses (from .example analysis)

- **Trainer Data**: 0x23EAC8 (FireRed 1.0)
- **TM Moves Table**: 0x45A5A4
- **Trainer Entry Size**: 40 bytes
- **Total Trainers**: 0x2E7

### TM Text Format (from .example/gen3_offsets.ini)

Format: `[TM_NUMBER, MAP_BANK, MAP_NUMBER, PERSON_NUM, OFFSET_IN_SCRIPT, TEXT_TEMPLATE]`

Example: `TMText[]=[39,6,2,1,0x99,TM39 contains [move].]`

### Trainer Class System

Uses `CLASS_LEADER` (0x53) for gym leaders, which provides:
- Special battle music
- Special victory music
- Ultra Ball animations
- Enhanced AI

## Troubleshooting

### TM Not Given
- Check trainer ID matches table entry
- Verify flag not already set
- Ensure `callasm GiveGymLeaderTMReward` is called

### Wrong TM Given
- Verify trainer ID in table
- Check TM number calculation
- Confirm ITEM_TM01 base address

### Flag Issues
- Ensure flags don't conflict with existing system
- Check flag range (SYS_FLAGS + offset)
- Verify flag names match between files

## Future Enhancements

- Support for HM rewards
- Multiple TM rewards per leader
- Conditional rewards based on player progress
- Integration with randomizer systems
- Support for custom TM text per leader
