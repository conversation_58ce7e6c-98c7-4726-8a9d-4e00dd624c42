#ifndef GUARD_UNION_ROOM_MESSAGE_H
#define GUARD_UNION_ROOM_MESSAGE_H

#include "global.h"

extern const u8 gText_UR_EmptyString[];
extern const u8 gText_UR_Colon[];
extern const u8 gText_UR_ID[];
extern const u8 gText_UR_PleaseStartOver[];
extern const u8 gText_UR_WirelessSearchCanceled[];
extern const u8 gText_UR_AwaitingCommunication[];
extern const u8 gText_UR_AwaitingLinkPressStart[];
extern const u8 *const gTexts_UR_PlayersNeededOrMode[][5];
extern const u8 gText_UR_BButtonCancel[];
extern const u8 gText_UR_PlayerContactedYouForXAccept[];
extern const u8 gText_UR_PlayerContactedYouShareX[];
extern const u8 gText_UR_PlayerContactedYouAddToMembers[];
extern const u8 gText_UR_AreTheseMembersOK[];
extern const u8 gText_UR_CancelModeWithTheseMembers[];
extern const u8 gText_UR_AnOKWasSentToPlayer[];
extern const u8 *const gTexts_UR_CantTransmitToTrainer[];
extern const u8 gText_UR_ModeWithTheseMembersWillBeCanceled[];
extern const u8 *const gTexts_UR_PlayerUnavailable[];
extern const u8 gText_UR_PlayerSentBackOK[];
extern const u8 gText_UR_PlayerOKdRegistration[];
extern const u8 gText_UR_AwaitingOtherMembers[];
extern const u8 gText_UR_QuitBeingMember[];
extern const u8 *const gTexts_UR_PlayerDisconnected[];
extern const u8 gText_UR_WirelessLinkEstablished[];
extern const u8 gText_UR_WirelessLinkDropped[];
extern const u8 gText_UR_LinkWithFriendDropped[];
extern const u8 *const gTexts_UR_LinkDropped[];
extern const u8 gText_UR_AwaitingPlayersResponseAboutTrade[];
extern const u8 *const gTexts_UR_CommunicatingWait[];
extern const u8 *const gTexts_UR_HiDoSomething[][GENDER_COUNT];
extern const u8 *const gTexts_UR_PlayerContactedYou[];
extern const u8 *const gTexts_UR_AwaitingResponse[];
extern const u8 gText_UR_ShowTrainerCard[];
extern const u8 gText_UR_BattleChallenge[];
extern const u8 gText_UR_ChatInvitation[];
extern const u8 gText_UR_OfferToTradeMon[];
extern const u8 gText_UR_OfferToTradeEgg[];
extern const u8 gText_UR_ChatDropped[];
extern const u8 gText_UR_OfferDeclined1[];
extern const u8 gText_UR_OfferDeclined2[];
extern const u8 gText_UR_ChatEnded[];
extern const u8 *const gTexts_UR_JoinChat[][GENDER_COUNT];
extern const u8 gText_UR_TrainerAppearsBusy[];
extern const u8 *const gTexts_UR_WaitOrShowCard[GENDER_COUNT][4];
extern const u8 *const gTexts_UR_StartActivity[][GENDER_COUNT][3];
extern const u8 *const gTexts_UR_BattleDeclined[GENDER_COUNT];
extern const u8 *const gTexts_UR_ShowTrainerCardDeclined[GENDER_COUNT];
extern const u8 *const gTexts_UR_IfYouWantToDoSomething[GENDER_COUNT];
extern const u8 gText_UR_TrainerBattleBusy[];
extern const u8 gText_UR_NeedTwoMonsOfLevel30OrLower1[];
extern const u8 gText_UR_NeedTwoMonsOfLevel30OrLower2[];
extern const u8 *const gTexts_UR_DeclineChat[GENDER_COUNT];
extern const u8 *const gTexts_UR_ChatDeclined[GENDER_COUNT];
extern const u8 *const gTexts_UR_BattleReaction[GENDER_COUNT][4];
extern const u8 *const gTexts_UR_ChatReaction[GENDER_COUNT][4];
extern const u8 *const gTexts_UR_TrainerCardReaction[GENDER_COUNT][2];
extern const u8 *const gTexts_UR_TradeReaction[GENDER_COUNT][4];
extern const u8 gText_UR_XCheckedTradingBoard[];
extern const u8 gText_UR_RegisterMonAtTradingBoard[];
extern const u8 gText_UR_TradingBoardInfo[];
extern const u8 gText_UR_ChooseRequestedMonType[];
extern const u8 gText_UR_WhichMonWillYouOffer[];
extern const u8 gText_UR_RegistrationCanceled[];
extern const u8 gText_UR_RegistraionCompleted[];
extern const u8 gText_UR_TradeCanceled[];
extern const u8 gText_UR_CancelRegistrationOfMon[];
extern const u8 gText_UR_CancelRegistrationOfEgg[];
extern const u8 gText_UR_RegistrationCanceled2[];
extern const u8 gText_UR_AskTrainerToMakeTrade[];
extern const u8 gText_UR_DontHaveTypeTrainerWants[];
extern const u8 gText_UR_DontHaveEggTrainerWants[];
extern const u8 gText_UR_TradeOfferRejected[];
extern const u8 gText_UR_EggTrade[];
extern const u8 gText_UR_ChooseJoinCancel[];
extern const u8 gText_UR_ChooseTrainer[];
extern const u8 gText_UR_SearchingForWirelessSystemWait[];
extern const u8 *const gTexts_UR_ChooseTrainer[];
extern const u8 gText_UR_AwaitingPlayersResponse[];
extern const u8 gText_UR_PlayerHasBeenAskedToRegisterYouPleaseWait[];
extern const u8 gText_UR_AwaitingResponseFromWirelessSystem[];
extern const u8 *const gTexts_UR_NoWonderShared[];
extern const u8 gText_UR_Battle[];
extern const u8 gText_UR_Chat2[];
extern const u8 gText_UR_Greetings[];
extern const u8 gText_UR_Exit[];
extern const u8 gText_UR_Exit2[];
extern const u8 gText_UR_Info[];
extern const u8 gText_UR_NameWantedOfferLv[];
extern const u8 gText_UR_SingleBattle[];
extern const u8 gText_UR_DoubleBattle[];
extern const u8 gText_UR_MultiBattle[];
extern const u8 gText_UR_PokemonTrades[];
extern const u8 gText_UR_Chat[];
extern const u8 gText_UR_WonderCards[];
extern const u8 gText_UR_WonderNews[];
extern const u8 gText_UR_Cards[];
extern const u8 gText_UR_PokemonJump[];
extern const u8 gText_UR_BerryCrush[];
extern const u8 gText_UR_BerryPicking[];
extern const u8 gText_UR_Search[];
extern const u8 gText_UR_SpinTrade[];
extern const u8 gText_UR_ItemTrade[];
extern const u8 *const gTexts_UR_CardColor[];
extern const u8 gText_UR_TrainerCardInfoPage1[];
extern const u8 gText_UR_TrainerCardInfoPage2[];
extern const u8 *const gTexts_UR_GladToMeetYou[GENDER_COUNT];
extern const u8 gText_UR_FinishedCheckingPlayersTrainerCard[];

#endif //GUARD_UNION_ROOM_MESSAGE_H
