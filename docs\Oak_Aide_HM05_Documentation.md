# Assistente do Professor Oak - HM05 (Flash) - Documentação Técnica

## Visão Geral

Este documento fornece informações técnicas detalhadas sobre o assistente do <PERSON> que entrega o HM05 (Flash) em Pokémon FireRed/LeafGreen quando o jogador captura 10 Pokémon. Esta documentação é destinada a desenvolvedores de ferramentas de ROM hacking e randomizers.

## Informações Básicas

| Propriedade | Valor |
|-------------|-------|
| **Jogo** | Pokémon FireRed/LeafGreen (Gen 3) |
| **Localização** | Route 2 (próximo ao Viridian Forest) |
| **Condição** | 10 Pokémon capturados |
| **Item Entregue** | HM05 (Flash) |
| **Movimento** | Flash (ID: 148) |
| **Tipo de NPC** | Gift Item NPC |

## Constantes e IDs

### Item IDs (Generation 3)
```c
#define ITEM_HM05           343     // HM05 item ID
#define MOVE_FLASH          148     // Flash move ID
#define POKEDEX_CHECK_10    10      // Minimum Pokemon caught
```

### Offsets Importantes

#### Prefix de Verificação do Assistente do Oak
```
frlgOakAideCheckPrefix = "00B5064800880028"
```

**Descrição**: Este padrão hexadecimal identifica o código assembly responsável pela verificação de Pokédex dos assistentes do Oak.

**Localização**: Encontrado através de busca de padrão na ROM

**Função**: Verifica quantos Pokémon foram capturados antes de entregar o item

## Estrutura do Código Assembly

### Verificação Original (Pokédex de Kanto)
```assembly
; Código original que verifica apenas Pokédex de Kanto
00B5064800880028    ; Pattern prefix
; Seguido por verificação de contagem de Pokémon capturados
```

### Patch para Pokédex Nacional
```assembly
; Modificação aplicada pelo randomizer
; Offset + 1: Alteração de instrução BNE para branch incondicional
; Valor: 0xE0 (branch incondicional)
```

## Implementação em Randomizers

### Detecção do Assistente
```c
// Buscar pelo padrão de verificação
int oakAideCheckOffs = find_pattern(rom, "00B5064800880028");
if (oakAideCheckOffs > 0) {
    // Assistente encontrado
    oakAideCheckOffs += 8; // Pular o prefix (8 bytes)
    // Aplicar modificações necessárias
}
```

### Patch para Pokédex Nacional
```c
// Modificar instrução para usar Pokédex Nacional
if (oakAideCheckOffs > 0) {
    // Alterar BNE para branch incondicional
    rom[oakAideCheckOffs + 1] = 0xE0;
}
```

### Proteção contra Randomização
```c
// HMs devem ser protegidos da randomização
bool is_protected_item(int item_id) {
    return (item_id >= ITEM_HM01 && item_id <= ITEM_HM08);
}
```

## Offsets por Versão

### FireRed (BPRE)
| Versão | CRC32 | Offset Base | Notas |
|--------|-------|-------------|-------|
| 1.0 (U) | 3B2056E9 | Busca por padrão | Versão mais comum |
| 1.1 (U) | DD88761C | Busca por padrão | Offsets podem variar |

### LeafGreen (BPGE)
| Versão | CRC32 | Offset Base | Notas |
|--------|-------|-------------|-------|
| 1.0 (U) | 612CA2C9 | Busca por padrão | Versão mais comum |
| 1.1 (U) | 5B5F1F0A | Busca por padrão | Offsets podem variar |

## Estrutura de Dados do NPC

### Formato Geral
```c
typedef struct {
    u16 species;        // Sempre 0 para NPCs de item
    u8  level;          // Sempre 0 para NPCs de item
    u16 item_id;        // ID do item a ser entregue (343 para HM05)
    u8  pokedex_req;    // Número de Pokémon necessários (10)
    u8  flag_given;     // Flag que indica se o item já foi entregue
} OakAideData;
```

## Scripts e Flags

### Flags Relacionadas
```c
#define FLAG_OAK_AIDE_ROUTE2_HM05    0x4XX  // Flag específica (varia por ROM)
#define FLAG_NATIONAL_DEX_OBTAINED   0x82C  // Pokédex Nacional obtida
```

### Verificação de Script
```
; Pseudocódigo do script do assistente
if (pokemon_caught >= 10) {
    if (!flag_is_set(FLAG_OAK_AIDE_ROUTE2_HM05)) {
        give_item(ITEM_HM05);
        set_flag(FLAG_OAK_AIDE_ROUTE2_HM05);
        show_message("Você capturou 10 Pokémon! Aqui está o HM05!");
    } else {
        show_message("Flash ilumina lugares escuros.");
    }
} else {
    show_message("Capture 10 Pokémon e volte aqui!");
}
```

## Considerações para Desenvolvimento

### Compatibilidade com Expansões
- **Pokédex Expandida**: Usar verificação de Pokédex Nacional
- **Novos Pokémon**: Garantir que contagem inclua espécies expandidas
- **ROMs Modificadas**: Verificar CRC32 antes de aplicar patches

### Tratamento de Erros
```c
// Verificação de segurança
if (oakAideCheckOffs <= 0) {
    log_error("Oak Aide pattern not found - ROM may be modified");
    return false;
}

if (oakAideCheckOffs + 8 >= rom_size) {
    log_error("Oak Aide offset exceeds ROM size");
    return false;
}
```

### Backup e Restauração
```c
// Sempre fazer backup antes de modificar
u8 original_byte = rom[oakAideCheckOffs + 1];
rom[oakAideCheckOffs + 1] = 0xE0;  // Aplicar patch

// Para restaurar:
// rom[oakAideCheckOffs + 1] = original_byte;
```

## Outros Assistentes do Oak

### Lista Completa (FireRed/LeafGreen)
| Localização | Pokémon Req. | Item | Offset Pattern |
|-------------|--------------|------|----------------|
| Route 2 | 10 | HM05 (Flash) | 00B5064800880028 |
| Route 11 | 30 | Itemfinder | Similar pattern |
| Route 15 | 50 | Exp. Share | Similar pattern |
| Route 25 | 60 | Amulet Coin | Similar pattern |

**Nota**: Todos os assistentes usam padrões similares, mas com offsets diferentes.

## Ferramentas Recomendadas

### Para Análise
- **Hex Editor**: HxD, Hex Fiend
- **Disassembler**: IDA Pro, Ghidra
- **ROM Tools**: AdvanceMap, XSE

### Para Desenvolvimento
- **Linguagens**: C, C++, Python
- **Libraries**: Úteis para manipulação de ROMs GBA

## Referências

- [Universal Pokemon Randomizer ZX](https://github.com/Ajarmar/universal-pokemon-randomizer-zx)
- [PokeCommunity ROM Hacking Documentation](https://www.pokecommunity.com/)
- [GBA ROM Structure Documentation](https://problemkaputt.de/gbatek.htm)

## Exemplos de Implementação

### Exemplo em C
```c
#include <stdio.h>
#include <stdint.h>
#include <string.h>

// Estrutura para dados do assistente
typedef struct {
    uint32_t offset;
    uint8_t pokemon_required;
    uint16_t item_id;
    uint8_t patched;
} OakAide;

// Função para encontrar o assistente
int find_oak_aide_hm05(uint8_t* rom, size_t rom_size, OakAide* aide) {
    const char pattern[] = "\x00\xB5\x06\x48\x00\x88\x00\x28";

    for (size_t i = 0; i < rom_size - 8; i++) {
        if (memcmp(rom + i, pattern, 8) == 0) {
            aide->offset = i;
            aide->pokemon_required = 10;
            aide->item_id = 343; // HM05
            aide->patched = 0;
            return 1; // Encontrado
        }
    }
    return 0; // Não encontrado
}

// Função para aplicar patch de Pokédex Nacional
int patch_oak_aide_national_dex(uint8_t* rom, OakAide* aide) {
    if (aide->offset == 0) return 0;

    // Backup do byte original
    uint8_t original = rom[aide->offset + 9];

    // Aplicar patch (branch incondicional)
    rom[aide->offset + 9] = 0xE0;

    aide->patched = 1;
    printf("Oak Aide patched at offset 0x%X\n", aide->offset);
    return 1;
}
```

### Exemplo em Python
```python
import struct

class OakAideHM05:
    def __init__(self):
        self.pattern = b'\x00\xB5\x06\x48\x00\x88\x00\x28'
        self.offset = None
        self.pokemon_required = 10
        self.item_id = 343  # HM05

    def find_in_rom(self, rom_data):
        """Encontra o assistente na ROM"""
        offset = rom_data.find(self.pattern)
        if offset != -1:
            self.offset = offset
            return True
        return False

    def patch_national_dex(self, rom_data):
        """Aplica patch para Pokédex Nacional"""
        if self.offset is None:
            return False

        # Modificar instrução no offset + 9
        rom_data[self.offset + 9] = 0xE0
        return True

    def is_hm_protected(self, item_id):
        """Verifica se o item é um HM protegido"""
        return 339 <= item_id <= 346  # HM01-HM08
```

## Detalhes Técnicos Avançados

### Análise do Assembly
```assembly
; Código original do assistente (ARM Thumb)
00B5        push    {lr}           ; Salvar link register
0648        ldr     r0, [pc, #24]  ; Carregar endereço da função
0088        lsls    r0, r0, #2     ; Shift left lógico
0028        cmp     r0, #40        ; Comparar com valor (pode ser contagem)

; Após o patch:
; A instrução de branch condicional é alterada para incondicional
; Isso força o uso da Pokédex Nacional em vez da Kanto
```

### Estrutura de Memória
```
Offset + 0:  00 B5        ; Push {lr}
Offset + 2:  06 48        ; ldr r0, [pc, #24]
Offset + 4:  00 88        ; lsls r0, r0, #2
Offset + 6:  00 28        ; cmp r0, #40
Offset + 8:  XX XX        ; Instrução de branch (modificada pelo patch)
```

### Flags de Estado
```c
// Flags importantes para o assistente
#define FLAG_SYS_POKEDEX_GET        0x0800  // Pokédex obtida
#define FLAG_SYS_NATIONAL_DEX       0x082C  // Pokédex Nacional
#define FLAG_AIDE_ROUTE2_HM05       0x4XXX  // Específico do assistente

// Verificação de estado
bool can_give_hm05(GameState* state) {
    return (state->pokemon_caught >= 10) &&
           !flag_is_set(FLAG_AIDE_ROUTE2_HM05);
}
```

## Troubleshooting

### Problemas Comuns

1. **Padrão não encontrado**
   - ROM pode estar comprimida
   - Versão diferente da documentada
   - ROM já foi modificada

2. **Patch não funciona**
   - Offset incorreto
   - ROM protegida contra escrita
   - Conflito com outras modificações

3. **Assistente não responde**
   - Flag já foi definida
   - Pokédex Nacional não ativada
   - Script corrompido

### Soluções
```c
// Verificação de integridade
bool verify_rom_integrity(uint8_t* rom, size_t size) {
    // Verificar header GBA
    if (size < 0x200) return false;

    // Verificar assinatura Nintendo
    const char nintendo[] = "NINTENDO";
    return memcmp(rom + 0x104, nintendo, 8) == 0;
}

// Backup de segurança
void backup_region(uint8_t* rom, uint32_t offset, uint8_t* backup, size_t size) {
    memcpy(backup, rom + offset, size);
}
```

## Changelog

| Versão | Data | Alterações |
|--------|------|------------|
| 1.0 | 2024-01-XX | Documentação inicial |
| 1.1 | 2024-01-XX | Adicionados exemplos de código e detalhes técnicos |

---

**Nota**: Esta documentação é baseada na análise do Universal Pokemon Randomizer ZX e pode precisar de ajustes para ROMs específicas ou modificadas. Sempre teste em ROMs de backup antes de aplicar modificações.
