# Sistema Avançado de Auto-Inicialização de Flags

Este sistema automaticamente gerencia flags importantes quando o jogador obtém seu primeiro Pokémon, podendo tanto **ATIVAR** quanto **DESATIVAR** flags baseado em uma tabela de configuração flexível, garantindo uma experiência de jogo otimizada.

## Sistema de Configuração Avançado

### **Tabela de Configuração:**
O sistema utiliza uma tabela de configuração que define para cada flag:
- **Flag ID**: Qual flag será afetada
- **Ação**: Se deve ser ATIVADA (TRUE) ou DESATIVADA (FALSE)
- **Condicional**: Se tem condição para ser aplicada
- **Flag de Condição**: Flag que deve estar ativa para aplicar (se condicional)

### **Flags ATIVADAS Automaticamente:**
- FLAG_EXP_SHARE - Sistema Gen 6+ EXP Share
- FLAG_SURF_TURBO_BOOST - Surf rápido
- FLAG_MOVE_RELEARNER_IGNORE_LEVEL - Move relearner completo
- FLAG_DYNAMAX_BATTLE - Sistema Dynamax
- FLAG_FAST_BATTLE_MESSAGES - Mensagens rápidas
- FLAG_UNLOCKED_DEXNAV_HELD_ITEMS - DexNav completo
- FLAG_KEPT_LEVEL_CAP_ON - Controle de level cap
- FLAG_SCALE_TRAINER_LEVELS - Escalonamento de treinadores
- FLAG_HIDDEN_ABILITY - Habilidades ocultas
- FLAG_DOUBLE_BATTLE - Batalhas duplas

### **Sistema de Level Cap Otimizado:**
- FLAG_HARD_LEVEL_CAP - Sistema de level cap (reativado com valores otimizados para hardcore gameplay)

### **Flags Condicionais:**
- FLAG_SYS_DEXNAV - Ativada SE FLAG_SYS_POKEDEX_GET estiver ativa

## Flags Controladas pelo Menu de Opções (Página 2)

As seguintes flags podem ser ativadas/desativadas através do menu de opções:

- **FLAG_SCALE_TRAINER_LEVELS (0x90E)** - Escala os níveis dos Pokémon dos treinadores para o mais alto do seu time
- **FLAG_HIDDEN_ABILITY (0x90F)** - Pokémon selvagens gerados terão suas habilidades ocultas
- **FLAG_DOUBLE_BATTLE (0x907)** - Ativa batalhas duplas automaticamente quando possível (treinadores + selvagens)

## Flags Inicializadas Automaticamente (Não Controláveis)

As seguintes flags são automaticamente ativadas uma única vez durante o gameplay:

- **FLAG_EXP_SHARE (0x906)** - Ativa o sistema de EXP Share da Gen 6+
- **FLAG_SURF_TURBO_BOOST (0x929)** - Surf se move extra rápido
- **FLAG_MOVE_RELEARNER_IGNORE_LEVEL (0x916)** - O relembrador de movimentos mostra todos os movimentos até MAX_LEVEL
- **FLAG_DYNAMAX_BATTLE (0x918)** - Pokémon podem usar Dynamax em batalha
- **FLAG_FAST_BATTLE_MESSAGES (0x925)** - Mensagens de batalha não têm tempo de espera
- **FLAG_UNLOCKED_DEXNAV_HELD_ITEMS (0x92A)** - DexNav mostra possíveis itens segurados
- **FLAG_HARD_LEVEL_CAP (0xA05)** - Sistema de level cap baseado em insígnias (OTIMIZADO para hardcore gameplay)
- **FLAG_KEPT_LEVEL_CAP_ON (0xA04)** - Rastreia que o level cap foi mantido ativo
- **FLAG_SYS_DEXNAV (0x91E)** - Habilita o DexNav (se FLAG_SYS_POKEDEX_GET estiver ativa)
- **FLAG_FOLLOWER_POKEMON (0x4BD)** - Habilita o sistema de Pokémon seguidor (controlado manualmente pelo menu)
- **FLAG_WILD_POKEMON_PREBATTLE_SCREEN (0xA02)** - Habilita estilo de janela para tela pré-batalha (se FLAG_HARD_LEVEL_CAP estiver ativa)
- **FLAG_ENABLE_WILD_PMN_PREBATTLE_SCREEN (0xA03)** - Habilita funcionalidade de tela pré-batalha (se FLAG_HARD_LEVEL_CAP estiver ativa)
- **FLAG_OPTIONS_DEFAULTS_SET (0xA08)** - Rastreia se valores padrão das opções foram aplicados

## Como Funcionam as Batalhas Duplas Selvagens

### **Sistema Original (FLAG_DOUBLE_WILD_BATTLE):**
- Só funciona em tiles especiais com `TILE_FLAG_WILD_DOUBLE`
- Tem apenas 50% de chance de ativar (`WILD_DOUBLE_RANDOM_CHANCE`)
- É temporária (limpa após a batalha)

### **Sistema Novo (FLAG_DOUBLE_BATTLE):**
- **Treinadores**: Funciona em qualquer lugar, sempre que possível
- **Selvagens**: Funciona em **qualquer tile** (não precisa de tiles especiais)
- **Chance**: Ainda respeita `WILD_DOUBLE_RANDOM_CHANCE` (50%) para selvagens
- **Persistente**: Permanece ativa até ser desativada manualmente

### **Verificações de Segurança:**
- Sempre verifica se você tem 2+ Pokémon viáveis
- Se você tem apenas 1 Pokémon, a batalha será normal (single)
- Funciona com tag battles mesmo com 1 Pokémon (parceiro fornece o segundo)

## Sistema de Level Cap Baseado em Insígnias

### **Como Funciona:**
O sistema impede que Pokémon ganhem XP acima de certos níveis até que insígnias específicas sejam obtidas.

### **Níveis Máximos por Insígnia (Distribuição Uniforme):**
- **0 insígnias**: Nível 22 (início generoso)
- **1 insígnia**: Nível 32 (+10 níveis)
- **2 insígnias**: Nível 42 (+10 níveis)
- **3 insígnias**: Nível 52 (+10 níveis)
- **4 insígnias**: Nível 62 (+10 níveis)
- **5 insígnias**: Nível 72 (+10 níveis)
- **6 insígnias**: Nível 82 (+10 níveis)
- **7 insígnias**: Nível 92 (+10 níveis)
- **8 insígnias**: Nível 100 (+8 níveis - gap final mínimo)

### **O que é Afetado:**
- ✅ **Ganho de XP em batalhas** - Para quando atinge o level cap
- ✅ **Rare Candy** - Não funciona acima do level cap
- ✅ **Day Care** - Pokémon não ganham XP acima do cap
- ✅ **Pokémon selvagens** - Não aparecem acima do level cap
- ✅ **DexNav** - Respeita o level cap

### **Flags Relacionadas:**
- **FLAG_HARD_LEVEL_CAP**: Ativa o sistema
- **FLAG_KEPT_LEVEL_CAP_ON**: Rastreia se foi mantido ativo desde o início

## Sistema DexNav

### **Como é Habilitado:**
O DexNav **NÃO** é habilitado por padrão. Requer duas flags específicas:

### **Flags Necessárias:**
- **FLAG_SYS_POKEDEX_GET (0x829)**: Jogador deve ter a Pokédex
- **FLAG_SYS_DEXNAV (0x91E)**: Habilita o DexNav no menu e botão R

### **Auto-Habilitação:**
- ✅ **FLAG_SYS_DEXNAV** é automaticamente ativada **SE** o jogador já tiver a Pokédex
- ❌ **Não ativa automaticamente** se a Pokédex não foi obtida ainda

### **Como Usar:**
1. **Obter a Pokédex** primeiro (via script/evento)
2. **DexNav será habilitado automaticamente** na próxima inicialização
3. **Acessar via Start Menu** → DexNav
4. **Ou usar botão R** (se configurado para DexNav mode)

### **Funcionalidades Desbloqueadas:**
- ✅ **FLAG_UNLOCKED_DEXNAV_HELD_ITEMS**: Mostra itens segurados automaticamente
- ✅ **Search Levels**: Sistema de progressão por espécie
- ✅ **Chain System**: Bônus por encontros consecutivos
- ✅ **Hidden Abilities**: Chance de habilidades ocultas
- ✅ **Egg Moves**: Movimentos especiais
- ✅ **Perfect IVs**: Pokémon com IVs perfeitos

## Sistema de Pokémon Seguidor

### **Como Funciona:**
O sistema permite que um Pokémon do seu time siga você pelo overworld, similar ao sistema de HeartGold/SoulSilver.

### **Flag Principal:**
- **FLAG_FOLLOWER_POKEMON (0x4BD)**: Ativa o sistema de Pokémon seguidor

### **Funcionalidades Suportadas:**
- ✅ **Seguir o jogador**: Pokémon segue automaticamente
- ✅ **Surf**: Pokémon pode surfar junto (sem surf blob)
- ✅ **Bicicleta**: Pokémon pode andar de bike (se configurado)
- ✅ **Escadas/Portas**: Segue através de transições
- ✅ **Interação**: Pode conversar com o Pokémon seguidor
- ✅ **Efeitos visuais**: Sparkle ao aparecer/desaparecer
- ✅ **Batalhas**: Remove antes da batalha, restaura depois

### **Comandos XSE Disponíveis:**
- **showfollowermon**: Mostra o Pokémon seguidor
- **hidefollowermon**: Esconde o Pokémon seguidor
- **followerfaceplayer**: Faz o follower olhar para o jogador
- **storemonid**: Armazena ID do Pokémon seguidor em Var4004

### **Como Ativar:**
1. **Menu de Opções**: Página 2 → Following Pokémon → On/Off
2. **Via Script**: `setflag FLAG_FOLLOWER_POKEMON` + `special 0xD1`

### **Debug Tools Removidas:**
- **NPC Debug no Laboratório**: Removida completamente
- **Fat Guy (Pallet Town)**: Funcionalidades de debug removidas
- **Girl (Pallet Town)**: Funcionalidades de debug removidas

### **Controle Manual:**
- **Não é mais auto-inicializado** - deve ser ativado manualmente
- **Controle total** através do menu de opções
- **Estado preservado** entre sessões de jogo

### **Configurações Avançadas:**
- **FOLLOWER_FLAG_CAN_BIKE**: Pode andar de bicicleta
- **FOLLOWER_FLAG_CAN_SURF**: Pode surfar
- **FOLLOWER_FLAG_CAN_WATERFALL**: Pode usar cachoeira
- **FOLLOWER_FLAG_CAN_DIVE**: Pode mergulhar
- **FOLLOWER_FLAG_CAN_ROCK_CLIMB**: Pode escalar rochas

## Sistema de Wild Screen (Tela Pré-Batalha)

### **Como Funciona:**
O sistema permite escolher se você quer "Ignore" ou "Engage" antes de cada batalha selvagem, similar ao sistema de Let's Go.

### **Dependência do Level Cap:**
- ✅ **Ativado automaticamente** quando jogador tem Pokémon próximos ao level cap
- ❌ **Desativado automaticamente** quando nenhum Pokémon está próximo ao level cap
- 🎯 **Lógica inteligente**: Só aparece quando realmente é útil (Pokémon no nível cap - 2 ou superior)
- 📊 **Verificação dinâmica**: Atualizado após ganhar XP, level up, e periodicamente no overworld

### **Flags Relacionadas:**
- **FLAG_WILD_POKEMON_PREBATTLE_SCREEN (0xA02)**: Controla estilo da janela
- **FLAG_ENABLE_WILD_PMN_PREBATTLE_SCREEN (0xA03)**: Ativa funcionalidade

### **Como Usar:**
1. **Automático**: Ativado quando você tem Pokémon próximos ao level cap
2. **Durante encontros**: Aparece tela de escolha antes da batalha
3. **Opções**: "Ignore" (foge) ou "Engage" (batalha)

### **Exemplos Práticos:**

#### **🎮 Cenário 1: Início do Jogo (0 insígnias)**
- **Level Cap**: 15
- **Seu Pokémon**: Nível 5
- **Wild Screen**: ❌ **DESATIVO** (Pokémon muito abaixo do cap)

#### **🎮 Cenário 2: Próximo ao Level Cap**
- **Level Cap**: 15
- **Seu Pokémon**: Nível 13-15
- **Wild Screen**: ✅ **ATIVO** (Pokémon próximo ao cap)

#### **🎮 Cenário 3: Após Obter Insígnia**
- **Level Cap**: 20 (1ª insígnia)
- **Seu Pokémon**: Nível 15
- **Wild Screen**: ❌ **DESATIVO** (Pokémon longe do novo cap)

#### **🎮 Cenário 4: Novamente Próximo**
- **Level Cap**: 20
- **Seu Pokémon**: Nível 18-20
- **Wild Screen**: ✅ **ATIVO** (Pokémon próximo ao cap novamente)

## Valores Padrão das Opções

### **R Button Mode:**
- **Padrão**: DexNav (OPTIONS_R_BUTTON_MODE_DEXNAV = 0)
- **Aplicado**: Na primeira inicialização do jogo

## Sistema de Reset de Auto-Flags

### **Como Funciona:**
A opção "Reset Auto-Flags" permite remover a flag de controle de auto-inicialização, forçando o sistema a reativar todas as flags automáticas na próxima verificação.

### **Quando Usar:**
- **Save antigo**: Para ativar novas funcionalidades em saves existentes
- **Debugging**: Para testar o sistema de auto-inicialização
- **Correção**: Se alguma flag automática foi desativada manualmente

### **Como Usar:**
1. **Menu de Opções** → Página 2 → Reset Auto-Flags → On
2. **Sair do menu** (as flags são resetadas automaticamente)
3. **A opção volta para "Off"** automaticamente

### **O que Acontece:**
- **FLAG_AUTO_INIT_FEATURES** é removida
- **Próxima verificação** reativa todas as flags automáticas
- **Funcionalidades restauradas** para o estado padrão

### **Auto-Sort Bag:**
- **Padrão**: By Types (valor 2)
- **Opções**: Off (0), By Name (1), By Types (2), By Amount (3)
- **Aplicado**: Na primeira inicialização do jogo

## Como Funciona

### Sistema de Menu de Opções (Página 2):
1. **R Button Mode**: Controla o comportamento do botão R (DexNav, Pokémon, Items)
2. **Battle Music**: Escolhe entre música FRLG ou RSE
3. **Auto-Sort Bag**: Controla ordenação automática da bag (Off, By Name, By Type, By Amount)
4. **Following Pokémon**: Liga/desliga sistema de Pokémon seguidor (controle manual)
5. **Reset Auto-Flags**: Remove flag de auto-inicialização para reativar flags automáticas

### Flags Auto-Inicializadas (Sempre Ativas):
- **Scale Trainer Levels**: Auto-inicializada (sempre ativa)
- **Hidden Ability**: Auto-inicializada (sempre ativa)
- **Double Battle**: Auto-inicializada (sempre ativa)
- **Wild Level Scaling**: Funcionalidade sempre disponível (não precisa de flag)

### Sistema de Auto-Inicialização:
1. **Verificação**: O sistema verifica se o jogador já possui Pokémon (FLAG_SYS_POKEMON_GET)
2. **Inicialização**: Se as flags ainda não foram inicializadas, elas são ativadas automaticamente
3. **Marcação**: Uma flag especial (FLAG_AUTO_INIT_FEATURES) é definida para evitar reinicializações

## Quando é Executado

A verificação e inicialização ocorre:
- Sempre que o jogador muda de mapa (função `RunOnTransitionMapScript`)
- Apenas uma vez por save file para flags auto-inicializadas
- Sempre que o menu de opções é fechado para flags controláveis

## Funções de Debug

Para fins de debug, foram adicionadas opções no menu de debug:

- **Opção 5**: Reset Auto Flag Initialization - Remove a flag de controle, permitindo reinicialização
- **Opção 6**: Force Auto Flag Initialization - Força a inicialização imediata das flags

## Arquivos Modificados

### Sistema de Menu de Opções:
- `src/config.h` - Adicionadas variáveis VAR_EXP_SHARE, VAR_SCALE_TRAINER_LEVELS, VAR_HIDDEN_ABILITY, VAR_DOUBLE_BATTLE
- `src/option_menu.c` - Implementação do sistema de menu
- `strings/option_menu.string` - Strings para as novas opções

### Sistema de Auto-Inicialização:
- `src/config.h` - Adicionada FLAG_AUTO_INIT_FEATURES
- `src/auto_flag_init.c` - Implementação do sistema
- `include/new/auto_flag_init.h` - Declarações das funções
- `src/overworld.c` - Integração com o sistema de mapas
- `src/debug_menu.c` - Opções de debug

## Personalização

### Para Flags Controláveis pelo Menu:
- Edite `InitializeOptionMenuDefaults()` em `src/option_menu.c` para alterar valores padrão
- Modifique `SyncOptionMenuFlagsWithVars()` para adicionar novas flags controláveis

### Para Flags Auto-Inicializadas:
- Edite `InitializeAutoFlags()` em `src/auto_flag_init.c` para modificar quais flags são inicializadas
- Modifique `CheckAndInitializeAutoFlags()` para alterar quando a inicialização ocorre

## Notas Importantes

- As flags são inicializadas apenas uma vez por save file
- O sistema é seguro e não interfere com saves existentes
- As flags podem ser desativadas manualmente após a inicialização, se necessário
- O sistema respeita a memória das preferências do usuário através da flag de controle
