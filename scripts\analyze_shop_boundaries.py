#!/usr/bin/env python3

"""
Shop Boundary Analyzer
Analyzes the original FireRed ROM to determine safe shop modification boundaries.

This script reads the original shop data and determines how many items each shop
can safely hold without overwriting adjacent data (NPCs, scripts, etc.).
"""

import struct
import os

# FireRed U 1.0 Shop Offsets (from .example project)
FIRERED_SHOP_OFFSETS = {
    0: 0x1649B8,   # Trainer Tower Poké Mart
    1: 0x1676E4,   # Two Island Market Stall (Initial)
    2: 0x1676FC,   # Two Island Market Stall (After Saving Lostelle)
    3: 0x167718,   # Two Island Market Stall (After Hall of Fame)
    4: 0x167738,   # Two Island Market Stall (After Ruby/Sapphire Quest)
    5: 0x16A298,   # Viridian Poké Mart
    6: 0x16A708,   # Pewter Poké Mart
    7: 0x16ACD8,   # Cerulean Poké Mart
    8: 0x16B390,   # Lavender Poké Mart
    9: 0x16B68C,   # Vermillion Poké Mart
    10: 0x16BB38,  # Celadon Department 2F South
    11: 0x16BB74,  # Celadon Department 2F North (TMs) *** TARGET ***
    12: 0x16BC30,  # Celadon Department 4F
    13: 0x16BC84,  # Celadon Department 5F South
    14: 0x16BC98,  # Celadon Department 5F North
    15: 0x16C0A8,  # Fuchsia Poké Mart
    16: 0x16C3A0,  # Cinnabar Poké Mart
    17: 0x16C6A8,  # Indigo Plateau Poké Mart
    18: 0x16C9B0,  # Saffron Poké Mart
    19: 0x16CCB8,  # Seven Island Poké Mart
    20: 0x16CFC0,  # Three Island Poké Mart
    21: 0x16D2C8,  # Four Island Poké Mart
    22: 0x16D5D0,  # Six Island Poké Mart
}

SHOP_NAMES = {
    0: "Trainer Tower Poké Mart",
    1: "Two Island Market Stall (Initial)",
    2: "Two Island Market Stall (After Saving Lostelle)",
    3: "Two Island Market Stall (After Hall of Fame)",
    4: "Two Island Market Stall (After Ruby/Sapphire Quest)",
    5: "Viridian Poké Mart",
    6: "Pewter Poké Mart",
    7: "Cerulean Poké Mart",
    8: "Lavender Poké Mart",
    9: "Vermillion Poké Mart",
    10: "Celadon Department 2F South",
    11: "Celadon Department 2F North (TMs)",
    12: "Celadon Department 4F",
    13: "Celadon Department 5F South",
    14: "Celadon Department 5F North",
    15: "Fuchsia Poké Mart",
    16: "Cinnabar Poké Mart",
    17: "Indigo Plateau Poké Mart",
    18: "Saffron Poké Mart",
    19: "Seven Island Poké Mart",
    20: "Three Island Poké Mart",
    21: "Four Island Poké Mart",
    22: "Six Island Poké Mart"
}

def read_shop_items(rom_data, shop_offset):
    """
    Read items from a shop at the specified offset.
    Returns a list of item IDs (excluding terminator).
    """
    items = []
    offset = shop_offset
    
    while True:
        # Read u16 (little endian)
        if offset + 1 >= len(rom_data):
            break
            
        item_id = struct.unpack('<H', rom_data[offset:offset+2])[0]
        
        if item_id == 0x0000:  # Terminator
            break
            
        items.append(item_id)
        offset += 2
        
        # Safety check to prevent infinite loop
        if len(items) > 200:
            print(f"Warning: Shop at offset 0x{shop_offset:06X} has more than 200 items!")
            break
    
    return items

def calculate_safe_boundary(rom_data, shop_offset, next_shop_offset):
    """
    Calculate how many items can safely fit in a shop without overwriting the next shop.
    """
    if next_shop_offset is None:
        # Last shop - use a conservative estimate
        return 50
    
    available_bytes = next_shop_offset - shop_offset
    max_items = (available_bytes // 2) - 1  # -1 for terminator
    
    return max_items

def analyze_shop_boundaries():
    """
    Analyze shop boundaries to determine safe modification limits.
    """
    rom_file = "BPRE0.gba"
    
    if not os.path.exists(rom_file):
        print(f"Error: ROM file '{rom_file}' not found!")
        print("Please ensure the FireRed U 1.0 ROM is in the current directory.")
        return
    
    with open(rom_file, 'rb') as f:
        rom_data = f.read()
    
    print("=" * 80)
    print("🔍 FIRERED SHOP BOUNDARY ANALYSIS")
    print("Analyzing original ROM to determine safe modification limits")
    print("=" * 80)
    
    # Analyze each shop
    shop_ids = sorted(FIRERED_SHOP_OFFSETS.keys())
    
    for i, shop_id in enumerate(shop_ids):
        shop_offset = FIRERED_SHOP_OFFSETS[shop_id]
        shop_name = SHOP_NAMES[shop_id]
        
        # Get next shop offset for boundary calculation
        next_shop_offset = None
        if i + 1 < len(shop_ids):
            next_shop_id = shop_ids[i + 1]
            next_shop_offset = FIRERED_SHOP_OFFSETS[next_shop_id]
        
        # Read original items
        original_items = read_shop_items(rom_data, shop_offset)
        
        # Calculate safe boundary
        max_safe_items = calculate_safe_boundary(rom_data, shop_offset, next_shop_offset)
        
        # Calculate available space
        if next_shop_offset:
            available_bytes = next_shop_offset - shop_offset
            available_items = (available_bytes // 2) - 1
        else:
            available_bytes = "Unknown"
            available_items = "Unknown"
        
        print(f"\n🏪 Shop {shop_id:2d}: {shop_name}")
        print(f"   📍 Offset: 0x{shop_offset:06X}")
        print(f"   📦 Original Items: {len(original_items)}")
        print(f"   📏 Available Space: {available_bytes} bytes" if isinstance(available_bytes, int) else f"   📏 Available Space: {available_bytes}")
        print(f"   ✅ Max Safe Items: {max_safe_items}")
        print(f"   🎯 Current Usage: {len(original_items)}/{max_safe_items} items")
        
        if len(original_items) > max_safe_items:
            print(f"   ⚠️  WARNING: Shop exceeds safe boundary!")
        elif len(original_items) == max_safe_items:
            print(f"   ⚠️  CAUTION: Shop at maximum capacity!")
        else:
            remaining = max_safe_items - len(original_items)
            print(f"   ✅ SAFE: {remaining} items can be added")
        
        # Show original items for reference
        if len(original_items) <= 10:
            print(f"   📋 Original Items: {[hex(item) for item in original_items]}")
        else:
            print(f"   📋 Original Items: {[hex(item) for item in original_items[:5]]} ... (+{len(original_items)-5} more)")

def analyze_celadon_tm_shop():
    """
    Specific analysis for Celadon TM Shop (Shop ID 11).
    """
    rom_file = "BPRE0.gba"
    
    if not os.path.exists(rom_file):
        print(f"Error: ROM file '{rom_file}' not found!")
        return
    
    with open(rom_file, 'rb') as f:
        rom_data = f.read()
    
    print("\n" + "=" * 80)
    print("🎯 CELADON TM SHOP DETAILED ANALYSIS (Shop ID 11)")
    print("=" * 80)
    
    shop_id = 11
    shop_offset = FIRERED_SHOP_OFFSETS[shop_id]
    next_shop_offset = FIRERED_SHOP_OFFSETS[12]  # Shop 12
    
    # Read original items
    original_items = read_shop_items(rom_data, shop_offset)
    
    # Calculate boundaries
    available_bytes = next_shop_offset - shop_offset
    max_possible_items = (available_bytes // 2) - 1  # -1 for terminator
    
    print(f"\n📊 DETAILED ANALYSIS:")
    print(f"   🏪 Shop: {SHOP_NAMES[shop_id]}")
    print(f"   📍 Start Offset: 0x{shop_offset:06X}")
    print(f"   📍 Next Shop Offset: 0x{next_shop_offset:06X}")
    print(f"   📏 Available Bytes: {available_bytes}")
    print(f"   📦 Original Items: {len(original_items)}")
    print(f"   🎯 Max Possible Items: {max_possible_items}")
    print(f"   📋 Original Items: {[hex(item) for item in original_items]}")
    
    print(f"\n🔍 SAFETY ANALYSIS:")
    if max_possible_items >= 120:
        print(f"   ✅ SAFE: Can fit all 120 TMs ({max_possible_items} max)")
    elif max_possible_items >= 50:
        print(f"   ⚠️  PARTIAL: Can fit {max_possible_items} TMs (less than 120)")
    else:
        print(f"   ❌ UNSAFE: Only {max_possible_items} TMs fit (major limitation)")
    
    print(f"\n💡 RECOMMENDATIONS:")
    if max_possible_items >= 120:
        print(f"   • All 120 TMs can be safely added")
        print(f"   • No risk of overwriting adjacent data")
    elif max_possible_items >= 50:
        print(f"   • Use curated TM list ({max_possible_items} items max)")
        print(f"   • Focus on most important TMs")
    else:
        print(f"   • Very limited space - use minimal TM list")
        print(f"   • Consider alternative implementation")

def main():
    """
    Main function - analyzes shop boundaries.
    """
    print("FireRed Shop Boundary Analyzer")
    print("Based on .example project methodology")
    print("=" * 50)
    
    # Analyze all shops
    analyze_shop_boundaries()
    
    # Detailed analysis for Celadon TM shop
    analyze_celadon_tm_shop()
    
    print("\n" + "=" * 80)
    print("📋 SUMMARY")
    print("=" * 80)
    
    print("\n✅ ANALYSIS COMPLETE:")
    print("   • Shop boundaries identified from original ROM")
    print("   • Safe modification limits calculated")
    print("   • Celadon TM Shop analyzed in detail")
    
    print("\n🎯 KEY FINDINGS:")
    print("   • Each shop has a specific size limit")
    print("   • Exceeding limits causes NPC corruption")
    print("   • .example project uses ShopItemSizes for safety")
    
    print("\n🔧 NEXT STEPS:")
    print("   1. Use calculated limits for shop modifications")
    print("   2. Respect shop boundaries to prevent corruption")
    print("   3. Test with smaller item counts first")

if __name__ == "__main__":
    main()
