package com.dabomstew.pkrandom.constants;

/*----------------------------------------------------------------------------*/
/*--  Abilities.java - defines an index number constant for every Ability.  --*/
/*--                                                                        --*/
/*--  Part of "Universal Pokemon Randomizer ZX" by the UPR-ZX team          --*/
/*--  Pokemon and any associated names and the like are                     --*/
/*--  trademark and (C) Nintendo 1996-2020.                                 --*/
/*--                                                                        --*/
/*--  The custom code written here is licensed under the terms of the GPL:  --*/
/*--                                                                        --*/
/*--  This program is free software: you can redistribute it and/or modify  --*/
/*--  it under the terms of the GNU General Public License as published by  --*/
/*--  the Free Software Foundation, either version 3 of the License, or     --*/
/*--  (at your option) any later version.                                   --*/
/*--                                                                        --*/
/*--  This program is distributed in the hope that it will be useful,       --*/
/*--  but WITHOUT ANY WARRANTY; without even the implied warranty of        --*/
/*--  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the          --*/
/*--  GNU General Public License for more details.                          --*/
/*--                                                                        --*/
/*--  You should have received a copy of the GNU General Public License     --*/
/*--  along with this program. If not, see <http://www.gnu.org/licenses/>.  --*/
/*----------------------------------------------------------------------------*/

public class Abilities {
    public static final int stench = 1;
    public static final int drizzle = 2;
    public static final int speedBoost = 3;
    public static final int battleArmor = 4;
    public static final int sturdy = 5;
    public static final int damp = 6;
    public static final int limber = 7;
    public static final int sandVeil = 8;
    public static final int staticTheAbilityNotTheKeyword = 9; // lol
    public static final int voltAbsorb = 10;
    public static final int waterAbsorb = 11;
    public static final int oblivious = 12;
    public static final int cloudNine = 13;
    public static final int compoundEyes = 14;
    public static final int insomnia = 15;
    public static final int colorChange = 16;
    public static final int immunity = 17;
    public static final int flashFire = 18;
    public static final int shieldDust = 19;
    public static final int ownTempo = 20;
    public static final int suctionCups = 21;
    public static final int intimidate = 22;
    public static final int shadowTag = 23;
    public static final int roughSkin = 24;
    public static final int wonderGuard = 25;
    public static final int levitate = 26;
    public static final int effectSpore = 27;
    public static final int synchronize = 28;
    public static final int clearBody = 29;
    public static final int naturalCure = 30;
    public static final int lightningRod = 31;
    public static final int sereneGrace = 32;
    public static final int swiftSwim = 33;
    public static final int chlorophyll = 34;
    public static final int illuminate = 35;
    public static final int trace = 36;
    public static final int hugePower = 37;
    public static final int poisonPoint = 38;
    public static final int innerFocus = 39;
    public static final int magmaArmor = 40;
    public static final int waterVeil = 41;
    public static final int magnetPull = 42;
    public static final int soundproof = 43;
    public static final int rainDish = 44;
    public static final int sandStream = 45;
    public static final int pressure = 46;
    public static final int thickFat = 47;
    public static final int earlyBird = 48;
    public static final int flameBody = 49;
    public static final int runAway = 50;
    public static final int keenEye = 51;
    public static final int hyperCutter = 52;
    public static final int pickup = 53;
    public static final int truant = 54;
    public static final int hustle = 55;
    public static final int cuteCharm = 56;
    public static final int plus = 57;
    public static final int minus = 58;
    public static final int forecast = 59;
    public static final int stickyHold = 60;
    public static final int shedSkin = 61;
    public static final int guts = 62;
    public static final int marvelScale = 63;
    public static final int liquidOoze = 64;
    public static final int overgrow = 65;
    public static final int blaze = 66;
    public static final int torrent = 67;
    public static final int swarm = 68;
    public static final int rockHead = 69;
    public static final int drought = 70;
    public static final int arenaTrap = 71;
    public static final int vitalSpirit = 72;
    public static final int whiteSmoke = 73;
    public static final int purePower = 74;
    public static final int shellArmor = 75;
    public static final int airLock = 76; // Was 77 in Gen 3, since 76 was Cacophony
    public static final int tangledFeet = 77;
    public static final int motorDrive = 78;
    public static final int rivalry = 79;
    public static final int steadfast = 80;
    public static final int snowCloak = 81;
    public static final int gluttony = 82;
    public static final int angerPoint = 83;
    public static final int unburden = 84;
    public static final int heatproof = 85;
    public static final int simple = 86;
    public static final int drySkin = 87;
    public static final int download = 88;
    public static final int ironFist = 89;
    public static final int poisonHeal = 90;
    public static final int adaptability = 91;
    public static final int skillLink = 92;
    public static final int hydration = 93;
    public static final int solarPower = 94;
    public static final int quickFeet = 95;
    public static final int normalize = 96;
    public static final int sniper = 97;
    public static final int magicGuard = 98;
    public static final int noGuard = 99;
    public static final int stall = 100;
    public static final int technician = 101;
    public static final int leafGuard = 102;
    public static final int klutz = 103;
    public static final int moldBreaker = 104;
    public static final int superLuck = 105;
    public static final int aftermath = 106;
    public static final int anticipation = 107;
    public static final int forewarn = 108;
    public static final int unaware = 109;
    public static final int tintedLens = 110;
    public static final int filter = 111;
    public static final int slowStart = 112;
    public static final int scrappy = 113;
    public static final int stormDrain = 114;
    public static final int iceBody = 115;
    public static final int solidRock = 116;
    public static final int snowWarning = 117;
    public static final int honeyGather = 118;
    public static final int frisk = 119;
    public static final int reckless = 120;
    public static final int multitype = 121;
    public static final int flowerGift = 122;
    public static final int badDreams = 123;
    public static final int pickpocket = 124;
    public static final int sheerForce = 125;
    public static final int contrary = 126;
    public static final int unnerve = 127;
    public static final int defiant = 128;
    public static final int defeatist = 129;
    public static final int cursedBody = 130;
    public static final int healer = 131;
    public static final int friendGuard = 132;
    public static final int weakArmor = 133;
    public static final int heavyMetal = 134;
    public static final int lightMetal = 135;
    public static final int multiscale = 136;
    public static final int toxicBoost = 137;
    public static final int flareBoost = 138;
    public static final int harvest = 139;
    public static final int telepathy = 140;
    public static final int moody = 141;
    public static final int overcoat = 142;
    public static final int poisonTouch = 143;
    public static final int regenerator = 144;
    public static final int bigPecks = 145;
    public static final int sandRush = 146;
    public static final int wonderSkin = 147;
    public static final int analytic = 148;
    public static final int illusion = 149;
    public static final int imposter = 150;
    public static final int infiltrator = 151;
    public static final int mummy = 152;
    public static final int moxie = 153;
    public static final int justified = 154;
    public static final int rattled = 155;
    public static final int magicBounce = 156;
    public static final int sapSipper = 157;
    public static final int prankster = 158;
    public static final int sandForce = 159;
    public static final int ironBarbs = 160;
    public static final int zenMode = 161;
    public static final int victoryStar = 162;
    public static final int turboblaze = 163;
    public static final int teravolt = 164;
    public static final int aromaVeil = 165;
    public static final int flowerVeil = 166;
    public static final int cheekPouch = 167;
    public static final int protean = 168;
    public static final int furCoat = 169;
    public static final int magician = 170;
    public static final int bulletproof = 171;
    public static final int competitive = 172;
    public static final int strongJaw = 173;
    public static final int refrigerate = 174;
    public static final int sweetVeil = 175;
    public static final int stanceChange = 176;
    public static final int galeWings = 177;
    public static final int megaLauncher = 178;
    public static final int grassPelt = 179;
    public static final int symbiosis = 180;
    public static final int toughClaws = 181;
    public static final int pixilate = 182;
    public static final int gooey = 183;
    public static final int aerilate = 184;
    public static final int parentalBond = 185;
    public static final int darkAura = 186;
    public static final int fairyAura = 187;
    public static final int auraBreak = 188;
    public static final int primordialSea = 189;
    public static final int desolateLand = 190;
    public static final int deltaStream = 191;
    public static final int stamina = 192;
    public static final int wimpOut = 193;
    public static final int emergencyExit = 194;
    public static final int waterCompaction = 195;
    public static final int merciless = 196;
    public static final int shieldsDown = 197;
    public static final int stakeout = 198;
    public static final int waterBubble = 199;
    public static final int steelworker = 200;
    public static final int berserk = 201;
    public static final int slushRush = 202;
    public static final int longReach = 203;
    public static final int liquidVoice = 204;
    public static final int triage = 205;
    public static final int galvanize = 206;
    public static final int surgeSurfer = 207;
    public static final int schooling = 208;
    public static final int disguise = 209;
    public static final int battleBond = 210;
    public static final int powerConstruct = 211;
    public static final int corrosion = 212;
    public static final int comatose = 213;
    public static final int queenlyMajesty = 214;
    public static final int innardsOut = 215;
    public static final int dancer = 216;
    public static final int battery = 217;
    public static final int fluffy = 218;
    public static final int dazzling = 219;
    public static final int soulHeart = 220;
    public static final int tanglingHair = 221;
    public static final int receiver = 222;
    public static final int powerOfAlchemy = 223;
    public static final int beastBoost = 224;
    public static final int rksSystem = 225;
    public static final int electricSurge = 226;
    public static final int psychicSurge = 227;
    public static final int mistySurge = 228;
    public static final int grassySurge = 229;
    public static final int fullMetalBody = 230;
    public static final int shadowShield = 231;
    public static final int prismArmor = 232;
    public static final int neuroforce = 233;
    public static final int intrepidSword = 234;
    public static final int dauntlessShield = 235;
    public static final int libero = 236;
    public static final int ballFetch = 237;
    public static final int cottonDown = 238;
    public static final int propellerTail = 239;
    public static final int mirrorArmor = 240;
    public static final int gulpMissile = 241;
    public static final int stalwart = 242;
    public static final int steamEngine = 243;
    public static final int punkRock = 244;
    public static final int sandSpit = 245;
    public static final int iceScales = 246;
    public static final int ripen = 247;
    public static final int iceFace = 248;
    public static final int powerSpot = 249;
    public static final int mimicry = 250;
    public static final int screenCleaner = 251;
    public static final int steelySpirit = 252;
    public static final int perishBody = 253;
    public static final int wanderingSpirit = 254;
    public static final int gorillaTactics = 255;
    public static final int neutralizingGas = 256;
    public static final int pastelVeil = 257;
    public static final int hungerSwitch = 258;
    public static final int quickDraw = 259;
    public static final int unseenFist = 260;
    public static final int curiousMedicine = 261;
    public static final int transistor = 262;
    public static final int dragonsMaw = 263;
    public static final int chillingNeigh = 264;
    public static final int grimNeigh = 265;
    public static final int asOneChillingNeigh = 266;
    public static final int asOneGrimNeigh = 267;
}
