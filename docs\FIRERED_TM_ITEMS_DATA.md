# Dados de TMs e Itens Especiais do FireRed

Esta biblioteca complementa os dados de pokemarts com informações específicas sobre TMs e itens especiais extraídos do projeto .example.

## TMs do FireRed

### Lista Completa de TMs (1-50)
```c
// Array com todos os TMs do FireRed e seus moves correspondentes
const u16 gFireRedTmMoves[] = {
    MOVE_FOCUS_PUNCH,     // TM01
    MOVE_DRAGON_CLAW,     // TM02
    MOVE_WATER_PULSE,     // TM03
    MOVE_CALM_MIND,       // TM04
    MOVE_ROAR,            // TM05
    MOVE_TOXIC,           // TM06
    MOVE_HAIL,            // TM07
    MOVE_BULK_UP,         // TM08
    MOVE_BULLET_SEED,     // TM09
    MOVE_HIDDEN_POWER,    // TM10
    MOVE_SUNNY_DAY,       // TM11
    MOVE_TAUNT,           // TM12
    MOVE_ICE_BEAM,        // TM13
    MOVE_BLIZZARD,        // TM14
    MOVE_HYPER_BEAM,      // TM15
    MOVE_LIGHT_SCREEN,    // TM16
    MOVE_PROTECT,         // TM17
    MOVE_RAIN_DANCE,      // TM18
    MOVE_GIGA_DRAIN,      // TM19
    MOVE_SAFEGUARD,       // TM20
    MOVE_FRUSTRATION,     // TM21
    MOVE_SOLAR_BEAM,      // TM22
    MOVE_IRON_TAIL,       // TM23
    MOVE_THUNDERBOLT,     // TM24
    MOVE_THUNDER,         // TM25
    MOVE_EARTHQUAKE,      // TM26
    MOVE_RETURN,          // TM27
    MOVE_DIG,             // TM28
    MOVE_PSYCHIC,         // TM29
    MOVE_SHADOW_BALL,     // TM30
    MOVE_BRICK_BREAK,     // TM31
    MOVE_DOUBLE_TEAM,     // TM32
    MOVE_REFLECT,         // TM33
    MOVE_SHOCK_WAVE,      // TM34
    MOVE_FLAMETHROWER,    // TM35
    MOVE_SLUDGE_BOMB,     // TM36
    MOVE_SANDSTORM,       // TM37
    MOVE_FIRE_BLAST,      // TM38
    MOVE_ROCK_TOMB,       // TM39
    MOVE_AERIAL_ACE,      // TM40
    MOVE_TORMENT,         // TM41
    MOVE_FACADE,          // TM42
    MOVE_SECRET_POWER,    // TM43
    MOVE_REST,            // TM44
    MOVE_ATTRACT,         // TM45
    MOVE_THIEF,           // TM46
    MOVE_STEEL_WING,      // TM47
    MOVE_SKILL_SWAP,      // TM48
    MOVE_SNATCH,          // TM49
    MOVE_OVERHEAT         // TM50
};
```

### HMs do FireRed
```c
// Array com todos os HMs do FireRed
const u16 gFireRedHmMoves[] = {
    MOVE_CUT,             // HM01
    MOVE_FLY,             // HM02
    MOVE_SURF,            // HM03
    MOVE_STRENGTH,        // HM04
    MOVE_FLASH,           // HM05
    MOVE_ROCK_SMASH,      // HM06
    MOVE_WATERFALL        // HM07
};
```

### Move Tutor Moves
```c
// Moves ensinados por Move Tutors no FireRed
const u16 gFireRedMoveTutorMoves[] = {
    MOVE_MEGA_PUNCH,      // Tutor 0
    MOVE_MEGA_KICK,       // Tutor 1
    MOVE_BODY_SLAM,       // Tutor 2
    MOVE_SEISMIC_TOSS,    // Tutor 3
    MOVE_COUNTER,         // Tutor 4
    MOVE_DOUBLE_EDGE,     // Tutor 5
    MOVE_METRONOME,       // Tutor 6
    MOVE_MIMIC,           // Tutor 7
    MOVE_DREAM_EATER,     // Tutor 8
    MOVE_THUNDER_WAVE,    // Tutor 9
    MOVE_EXPLOSION,       // Tutor 10
    MOVE_ROCK_SLIDE,      // Tutor 11
    MOVE_SUBSTITUTE,      // Tutor 12
    MOVE_DYNAMIC_PUNCH,   // Tutor 13
    MOVE_SLEEP_TALK       // Tutor 14
};
```

## Itens Especiais

### Mega Evolution Items (CFRU)
```c
// Itens relacionados a Mega Evolution
const u16 gMegaEvolutionItems[] = {
    ITEM_MEGA_RING,       // 353 (0x161)
    ITEM_MEGA_CUFF,       // 119 (0x77)
    ITEM_MEGA_BONNET,     // 120 (0x78)
    ITEM_MEGA_EARING,     // 527 (0x20F)
    ITEM_MEGA_CHARM,      // 528 (0x210)
    ITEM_MEGA_BRACELET,   // 529 (0x211)
    ITEM_MEGA_ANKLET,     // 530 (0x212)
    ITEM_MEGA_PENDANT     // 531 (0x213)
};
```

### Z-Power Items (CFRU)
```c
// Itens relacionados a Z-Moves
const u16 gZPowerItems[] = {
    ITEM_Z_POWER_RING,    // 267 (0x10B)
    ITEM_NORMALIUM_Z,     // Z-Crystal Normal
    ITEM_FIGHTINIUM_Z,    // Z-Crystal Fighting
    ITEM_FLYINIUM_Z,      // Z-Crystal Flying
    ITEM_POISONIUM_Z,     // Z-Crystal Poison
    ITEM_GROUNDIUM_Z,     // Z-Crystal Ground
    ITEM_ROCKIUM_Z,       // Z-Crystal Rock
    ITEM_BUGINIUM_Z,      // Z-Crystal Bug
    ITEM_GHOSTIUM_Z,      // Z-Crystal Ghost
    ITEM_STEELIUM_Z,      // Z-Crystal Steel
    ITEM_FIRIUM_Z,        // Z-Crystal Fire
    ITEM_WATERIUM_Z,      // Z-Crystal Water
    ITEM_GRASSIUM_Z,      // Z-Crystal Grass
    ITEM_ELECTRIUM_Z,     // Z-Crystal Electric
    ITEM_PSYCHIUM_Z,      // Z-Crystal Psychic
    ITEM_ICIUM_Z,         // Z-Crystal Ice
    ITEM_DRAGONIUM_Z,     // Z-Crystal Dragon
    ITEM_DARKINIUM_Z,     // Z-Crystal Dark
    ITEM_FAIRIUM_Z,       // Z-Crystal Fairy
    ITEM_PIKANIUM_Z       // Pikachu-specific Z-Crystal
};
```

### Dynamax Items (CFRU)
```c
// Itens relacionados a Dynamax
const u16 gDynamaxItems[] = {
    ITEM_DYNAMAX_BAND     // 347 (0x15B)
};
```

### Evolution Stones
```c
// Pedras evolutivas do FireRed
const u16 gEvolutionStones[] = {
    ITEM_FIRE_STONE,      // 82
    ITEM_THUNDER_STONE,   // 83
    ITEM_WATER_STONE,     // 84
    ITEM_LEAF_STONE,      // 85
    ITEM_MOON_STONE,      // 94
    ITEM_SUN_STONE        // 93
};
```

## Dados de Preços

### Preços Padrão de TMs
```c
// Preços sugeridos para TMs em shops
const u32 gTmPrices[] = {
    3000,  // TM01-10 (Básicos)
    4000,  // TM11-20 (Intermediários)
    5000,  // TM21-30 (Avançados)
    6000,  // TM31-40 (Poderosos)
    7000,  // TM41-50 (Raros)
};

// Função para obter preço de TM
u32 GetTmPrice(u8 tmNumber) {
    if (tmNumber >= 1 && tmNumber <= 10) return gTmPrices[0];
    if (tmNumber >= 11 && tmNumber <= 20) return gTmPrices[1];
    if (tmNumber >= 21 && tmNumber <= 30) return gTmPrices[2];
    if (tmNumber >= 31 && tmNumber <= 40) return gTmPrices[3];
    if (tmNumber >= 41 && tmNumber <= 50) return gTmPrices[4];
    return 1000; // Preço padrão
}
```

### Preços de Itens Especiais
```c
// Preços para itens especiais
const struct ItemPrice {
    u16 itemId;
    u32 price;
} gSpecialItemPrices[] = {
    {ITEM_MEGA_RING, 50000},
    {ITEM_Z_POWER_RING, 50000},
    {ITEM_DYNAMAX_BAND, 50000},
    {ITEM_FIRE_STONE, 2100},
    {ITEM_THUNDER_STONE, 2100},
    {ITEM_WATER_STONE, 2100},
    {ITEM_LEAF_STONE, 2100},
    {ITEM_MOON_STONE, 3000},
    {ITEM_SUN_STONE, 3000},
    {ITEM_RARE_CANDY, 10000},
    {ITEM_LUCKY_EGG, 15000}
};
```

## Categorização de TMs

### TMs por Tipo
```c
// TMs organizados por tipo de move
const u8 gFireTypeTMs[] = {35, 38, 50}; // Flamethrower, Fire Blast, Overheat
const u8 gWaterTypeTMs[] = {3, 13, 18}; // Water Pulse, Ice Beam, Rain Dance
const u8 gElectricTypeTMs[] = {24, 25, 34}; // Thunderbolt, Thunder, Shock Wave
const u8 gGrassTypeTMs[] = {9, 19, 22}; // Bullet Seed, Giga Drain, Solar Beam
const u8 gPsychicTypeTMs[] = {4, 29, 48}; // Calm Mind, Psychic, Skill Swap
const u8 gFightingTypeTMs[] = {1, 8, 31}; // Focus Punch, Bulk Up, Brick Break
const u8 gPoisonTypeTMs[] = {6, 36}; // Toxic, Sludge Bomb
const u8 gGroundTypeTMs[] = {26, 28}; // Earthquake, Dig
const u8 gFlyingTypeTMs[] = {40}; // Aerial Ace
const u8 gBugTypeTMs[] = {9}; // Bullet Seed (também Grass)
const u8 gRockTypeTMs[] = {23, 37, 39}; // Iron Tail, Sandstorm, Rock Tomb
const u8 gGhostTypeTMs[] = {30}; // Shadow Ball
const u8 gDarkTypeTMs[] = {12, 41, 46}; // Taunt, Torment, Thief
const u8 gSteelTypeTMs[] = {47}; // Steel Wing
const u8 gIceTypeTMs[] = {7, 14}; // Hail, Blizzard
const u8 gDragonTypeTMs[] = {2}; // Dragon Claw
```

### TMs por Utilidade
```c
// TMs categorizados por função
const u8 gOffensiveTMs[] = {
    1, 2, 3, 13, 14, 15, 19, 22, 23, 24, 25, 26, 28, 29, 30, 31, 35, 36, 38, 39, 40, 46, 47, 50
};

const u8 gDefensiveTMs[] = {
    4, 16, 17, 20, 32, 33, 37, 42, 44
};

const u8 gStatusTMs[] = {
    5, 6, 7, 11, 12, 18, 21, 27, 41, 43, 45, 48, 49
};

const u8 gUtilityTMs[] = {
    8, 9, 10, 34
};
```

## Funções Utilitárias

### Verificação de TM
```c
// Função para verificar se um item é TM
bool8 IsTmItem(u16 itemId) {
    return (itemId >= ITEM_TM01 && itemId <= ITEM_TM50);
}

// Função para verificar se um item é HM
bool8 IsHmItem(u16 itemId) {
    return (itemId >= ITEM_HM01 && itemId <= ITEM_HM07);
}

// Função para obter número do TM
u8 GetTmNumber(u16 itemId) {
    if (IsTmItem(itemId)) {
        return (itemId - ITEM_TM01) + 1;
    }
    return 0;
}

// Função para obter move de um TM
u16 GetTmMove(u16 itemId) {
    u8 tmNumber = GetTmNumber(itemId);
    if (tmNumber >= 1 && tmNumber <= 50) {
        return gFireRedTmMoves[tmNumber - 1];
    }
    return MOVE_NONE;
}
```

### Sistema de Desbloqueio de TMs
```c
// Sistema para desbloquear TMs baseado em badges
const u8 gTmUnlocksByBadge[][10] = {
    // Badge 1 (Brock)
    {39, 0}, // Rock Tomb
    
    // Badge 2 (Misty)  
    {3, 13, 18, 0}, // Water Pulse, Ice Beam, Rain Dance
    
    // Badge 3 (Lt. Surge)
    {24, 25, 34, 0}, // Thunderbolt, Thunder, Shock Wave
    
    // Badge 4 (Erika)
    {9, 19, 22, 0}, // Bullet Seed, Giga Drain, Solar Beam
    
    // Badge 5 (Koga)
    {6, 36, 0}, // Toxic, Sludge Bomb
    
    // Badge 6 (Sabrina)
    {4, 29, 48, 0}, // Calm Mind, Psychic, Skill Swap
    
    // Badge 7 (Blaine)
    {35, 38, 50, 0}, // Flamethrower, Fire Blast, Overheat
    
    // Badge 8 (Giovanni)
    {26, 28, 0} // Earthquake, Dig
};
```

## Constantes de Configuração

```c
#define FIRERED_TM_START_ID         ITEM_TM01
#define FIRERED_TM_END_ID           ITEM_TM50
#define FIRERED_HM_START_ID         ITEM_HM01
#define FIRERED_HM_END_ID           ITEM_HM07

#define TM_PRICE_BASIC              3000
#define TM_PRICE_INTERMEDIATE       4000
#define TM_PRICE_ADVANCED           5000
#define TM_PRICE_POWERFUL           6000
#define TM_PRICE_RARE               7000

#define MEGA_ITEM_PRICE             50000
#define Z_ITEM_PRICE                50000
#define DYNAMAX_ITEM_PRICE          50000
#define EVOLUTION_STONE_PRICE       2100
#define RARE_STONE_PRICE            3000
```

Esta biblioteca de dados complementa perfeitamente a biblioteca principal de pokemarts, fornecendo informações detalhadas sobre TMs, HMs, itens especiais e seus sistemas de preços e desbloqueio.
