#ifndef GUARD_CONSTANTS_BATTLE_MOVE_EFFECTS_H
#define GUARD_CONSTANTS_BATTLE_MOVE_EFFECTS_H

#define EFFECT_HIT 0
#define EFFECT_SLEEP 1
#define EFFECT_POISON_HIT 2
#define EFFECT_ABSORB 3
#define EFFECT_BURN_HIT 4
#define EFFECT_FREEZE_HIT 5
#define EFFECT_PARALYZE_HIT 6
#define EFFECT_EXPLOSION 7
#define EFFECT_DREAM_EATER 8
#define EFFECT_MIRROR_MOVE 9
#define EFFECT_ATTACK_UP 10
#define EFFECT_DEFENSE_UP 11
#define EFFECT_SPEED_UP 12
#define EFFECT_SPECIAL_ATTACK_UP 13
#define EFFECT_SPECIAL_DEFENSE_UP 14
#define EFFECT_ACCURACY_UP 15
#define EFFECT_EVASION_UP 16
#define EFFECT_ALWAYS_HIT 17
#define EFFECT_ATTACK_DOWN 18
#define EFFECT_DEFENSE_DOWN 19
#define EFFECT_SPEED_DOWN 20
#define EFFECT_SPECIAL_ATTACK_DOWN 21  // unused
#define EFFECT_SPECIAL_DEFENSE_DOWN 22  // unused
#define EFFECT_ACCURACY_DOWN 23
#define EFFECT_EVASION_DOWN 24
#define EFFECT_HAZE 25
#define EFFECT_BIDE 26
#define EFFECT_RAMPAGE 27
#define EFFECT_ROAR 28
#define EFFECT_MULTI_HIT 29
#define EFFECT_CONVERSION 30
#define EFFECT_FLINCH_HIT 31
#define EFFECT_RESTORE_HP 32
#define EFFECT_TOXIC 33
#define EFFECT_PAY_DAY 34
#define EFFECT_LIGHT_SCREEN 35
#define EFFECT_TRI_ATTACK 36
#define EFFECT_REST 37
#define EFFECT_OHKO 38
#define EFFECT_RAZOR_WIND 39
#define EFFECT_SUPER_FANG 40
#define EFFECT_DRAGON_RAGE 41
#define EFFECT_TRAP 42
#define EFFECT_HIGH_CRITICAL 43
#define EFFECT_DOUBLE_HIT 44
#define EFFECT_RECOIL_IF_MISS 45
#define EFFECT_MIST 46
#define EFFECT_FOCUS_ENERGY 47
#define EFFECT_RECOIL 48
#define EFFECT_CONFUSE 49
#define EFFECT_ATTACK_UP_2 50
#define EFFECT_DEFENSE_UP_2 51
#define EFFECT_SPEED_UP_2 52
#define EFFECT_SPECIAL_ATTACK_UP_2 53
#define EFFECT_SPECIAL_DEFENSE_UP_2 54
#define EFFECT_ACCURACY_UP_2 55
#define EFFECT_EVASION_UP_2 56
#define EFFECT_TRANSFORM 57
#define EFFECT_ATTACK_DOWN_2 58
#define EFFECT_DEFENSE_DOWN_2 59
#define EFFECT_SPEED_DOWN_2 60
#define EFFECT_SPECIAL_ATTACK_DOWN_2 61
#define EFFECT_SPECIAL_DEFENSE_DOWN_2 62
#define EFFECT_ACCURACY_DOWN_2 63
#define EFFECT_EVASION_DOWN_2 64
#define EFFECT_REFLECT 65
#define EFFECT_POISON 66
#define EFFECT_PARALYZE 67
#define EFFECT_ATTACK_DOWN_HIT 68
#define EFFECT_DEFENSE_DOWN_HIT 69
#define EFFECT_SPEED_DOWN_HIT 70
#define EFFECT_SPECIAL_ATTACK_DOWN_HIT 71
#define EFFECT_SPECIAL_DEFENSE_DOWN_HIT 72
#define EFFECT_ACCURACY_DOWN_HIT 73
#define EFFECT_EVASION_DOWN_HIT 74
#define EFFECT_SKY_ATTACK 75
#define EFFECT_CONFUSE_HIT 76
#define EFFECT_TWINEEDLE 77
#define EFFECT_VITAL_THROW 78
#define EFFECT_SUBSTITUTE 79
#define EFFECT_RECHARGE 80
#define EFFECT_RAGE 81
#define EFFECT_MIMIC 82
#define EFFECT_METRONOME 83
#define EFFECT_LEECH_SEED 84
#define EFFECT_SPLASH 85
#define EFFECT_DISABLE 86
#define EFFECT_LEVEL_DAMAGE 87
#define EFFECT_PSYWAVE 88
#define EFFECT_COUNTER 89
#define EFFECT_ENCORE 90
#define EFFECT_PAIN_SPLIT 91
#define EFFECT_SNORE 92
#define EFFECT_CONVERSION_2 93
#define EFFECT_LOCK_ON 94
#define EFFECT_SKETCH 95
#define EFFECT_UNUSED_60 96  // thaw
#define EFFECT_SLEEP_TALK 97
#define EFFECT_DESTINY_BOND 98
#define EFFECT_FLAIL 99
#define EFFECT_SPITE 100
#define EFFECT_FALSE_SWIPE 101
#define EFFECT_HEAL_BELL 102
#define EFFECT_QUICK_ATTACK 103
#define EFFECT_TRIPLE_KICK 104
#define EFFECT_THIEF 105
#define EFFECT_MEAN_LOOK 106
#define EFFECT_NIGHTMARE 107
#define EFFECT_MINIMIZE 108
#define EFFECT_CURSE 109
#define EFFECT_UNUSED_6E 110
#define EFFECT_PROTECT 111
#define EFFECT_SPIKES 112
#define EFFECT_FORESIGHT 113
#define EFFECT_PERISH_SONG 114
#define EFFECT_SANDSTORM 115
#define EFFECT_ENDURE 116
#define EFFECT_ROLLOUT 117
#define EFFECT_SWAGGER 118
#define EFFECT_FURY_CUTTER 119
#define EFFECT_ATTRACT 120
#define EFFECT_RETURN 121
#define EFFECT_PRESENT 122
#define EFFECT_FRUSTRATION 123
#define EFFECT_SAFEGUARD 124
#define EFFECT_THAW_HIT 125
#define EFFECT_MAGNITUDE 126
#define EFFECT_BATON_PASS 127
#define EFFECT_PURSUIT 128
#define EFFECT_RAPID_SPIN 129
#define EFFECT_SONICBOOM 130
#define EFFECT_UNUSED_83 131
#define EFFECT_MORNING_SUN 132
#define EFFECT_SYNTHESIS 133
#define EFFECT_MOONLIGHT 134
#define EFFECT_HIDDEN_POWER 135
#define EFFECT_RAIN_DANCE 136
#define EFFECT_SUNNY_DAY 137
#define EFFECT_DEFENSE_UP_HIT 138
#define EFFECT_ATTACK_UP_HIT 139
#define EFFECT_ALL_STATS_UP_HIT 140
#define EFFECT_UNUSED_8D 141  // incomplete fake out in gen 2
#define EFFECT_BELLY_DRUM 142
#define EFFECT_PSYCH_UP 143
#define EFFECT_MIRROR_COAT 144
#define EFFECT_SKULL_BASH 145
#define EFFECT_TWISTER 146
#define EFFECT_EARTHQUAKE 147
#define EFFECT_FUTURE_SIGHT 148
#define EFFECT_GUST 149
#define EFFECT_FLINCH_MINIMIZE_HIT 150  // STOMP ASTONISH EXTRASENSORY NEEDLE_ARM
#define EFFECT_SOLAR_BEAM 151
#define EFFECT_THUNDER 152
#define EFFECT_TELEPORT 153
#define EFFECT_BEAT_UP 154
#define EFFECT_SEMI_INVULNERABLE 155
#define EFFECT_DEFENSE_CURL 156
#define EFFECT_SOFTBOILED 157
#define EFFECT_FAKE_OUT 158
#define EFFECT_UPROAR 159
#define EFFECT_STOCKPILE 160
#define EFFECT_SPIT_UP 161
#define EFFECT_SWALLOW 162
#define EFFECT_UNUSED_A3 163
#define EFFECT_HAIL 164
#define EFFECT_TORMENT 165
#define EFFECT_FLATTER 166
#define EFFECT_WILL_O_WISP 167
#define EFFECT_MEMENTO 168
#define EFFECT_FACADE 169
#define EFFECT_FOCUS_PUNCH 170
#define EFFECT_SMELLINGSALT 171
#define EFFECT_FOLLOW_ME 172
#define EFFECT_NATURE_POWER 173
#define EFFECT_CHARGE 174
#define EFFECT_TAUNT 175
#define EFFECT_HELPING_HAND 176
#define EFFECT_TRICK 177
#define EFFECT_ROLE_PLAY 178
#define EFFECT_WISH 179
#define EFFECT_ASSIST 180
#define EFFECT_INGRAIN 181
#define EFFECT_SUPERPOWER 182
#define EFFECT_MAGIC_COAT 183
#define EFFECT_RECYCLE 184
#define EFFECT_REVENGE 185
#define EFFECT_BRICK_BREAK 186
#define EFFECT_YAWN 187
#define EFFECT_KNOCK_OFF 188
#define EFFECT_ENDEAVOR 189
#define EFFECT_ERUPTION 190
#define EFFECT_SKILL_SWAP 191
#define EFFECT_IMPRISON 192
#define EFFECT_REFRESH 193
#define EFFECT_GRUDGE 194
#define EFFECT_SNATCH 195
#define EFFECT_LOW_KICK 196
#define EFFECT_SECRET_POWER 197
#define EFFECT_DOUBLE_EDGE 198
#define EFFECT_TEETER_DANCE 199
#define EFFECT_BLAZE_KICK 200
#define EFFECT_MUD_SPORT 201
#define EFFECT_POISON_FANG 202
#define EFFECT_WEATHER_BALL 203
#define EFFECT_OVERHEAT 204
#define EFFECT_TICKLE 205
#define EFFECT_COSMIC_POWER 206
#define EFFECT_SKY_UPPERCUT 207
#define EFFECT_BULK_UP 208
#define EFFECT_POISON_TAIL 209
#define EFFECT_WATER_SPORT 210
#define EFFECT_CALM_MIND 211
#define EFFECT_DRAGON_DANCE 212
#define EFFECT_CAMOUFLAGE 213

#endif  // GUARD_CONSTANTS_BATTLE_MOVE_EFFECTS_H
