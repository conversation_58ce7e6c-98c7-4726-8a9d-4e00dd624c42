BWXP_ENEMY_LEVEL EQU $CFF2
BWXP_ENEMY_SPECIES EQU $CFE4
BWXP_MAX_LEVEL EQU 100
BWXP_BATTLE_TYPE EQU $D056
BWXP_MULTIPLICAND EQU $FF95
BWXP_MULTIPLIER EQU $FF99
BWXP_DIVIDEND EQU BWXP_MULTIPLICAND
BWXP_DIVISOR EQU BWXP_MULTIPLIER
BWXP_INBUILT_MULTIPLY EQU $38A5 ; $38AC = Multiply
BWXP_INBUILT_DIVIDE EQU $38B2 ; $38B9 = Divide
BWXP_INBUILT_ISITEMINBAG EQU $3422
BWXP_NUM_PARTICIPANTS EQU $D11D
BWXP_SCRATCH5B_1 EQU $CFEC
BWXP_SCRATCH5B_2 EQU $CFF3
BWXP_SCRATCH1B EQU $CFFD
BWXP_MULTIPLIER_STOR EQU $FF9B
BWXP_BIG_MULTIPLICAND EQU $FF94
BWXP_BIG_DIVIDEND EQU BWXP_BIG_MULTIPLICAND
BWXP_LEVEL_OFFSET_IN_PARTYMON EQU $0007
BWXP_TID_OFFSET_IN_PARTYMON EQU $FFF2
BWXP_PLAYER_TID EQU $D358
BWXP_BOOSTED_EXP_FLAG EQU $CF4C
BWXP_RETURN_POINT EQU $5308
BWXP_DIVIDEEXP_RETURN_POINT EQU $5492
