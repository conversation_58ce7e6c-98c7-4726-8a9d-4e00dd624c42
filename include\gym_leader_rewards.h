#pragma once

#include "global.h"
#include "constants/flags.h"
#include "constants/trainers.h"
#include "constants/items.h"

// Structure for gym leader BONUS rewards (COMPLEMENTARY to existing system)
// This system gives EXTRA items WITHOUT replacing the original TMs/badges
struct GymLeaderBonusReward {
    u16 trainerId;      // ID do treinador líder
    u16 bonusItem;      // Item extra a ser dado (além do TM original)
    u16 quantity;       // Quantidade do item
    u16 flagReceived;   // Flag se já recebeu o item bonus
    const u8 *rewardText; // Texto para o item bonus
};

// Gym Leader IDs based on .example/Gen3Constants.java analysis
// FireRed/LeafGreen Gym Leaders:
// tag(trs, 0x19E, "GYM1-LEADER"); // Brock
// tag(trs, 0x19F, "GYM2-LEADER"); // Misty  
// tag(trs, 0x1A0, "GYM3-LEADER"); // Lt. Surge
// tag(trs, 0x1A1, "GYM4-LEADER"); // Erika
// tag(trs, 0x1A2, "GYM5-LEADER"); // Koga
// tag(trs, 0x1A4, "GYM6-LEADER"); // Sabrina
// tag(trs, 0x1A3, "GYM7-LEADER"); // Blaine
// tag(trs, 0x15E, "GYM8-LEADER"); // Giovanni

#define TRAINER_BROCK       0x19E
#define TRAINER_MISTY       0x19F
#define TRAINER_LT_SURGE    0x1A0
#define TRAINER_ERIKA       0x1A1
#define TRAINER_KOGA        0x1A2
#define TRAINER_SABRINA     0x1A4
#define TRAINER_BLAINE      0x1A3
#define TRAINER_GIOVANNI    0x15E

// Function declarations for BONUS reward system
void PostTrainerBattleHook_C(void);
void GiveGymLeaderBonusReward(void);
void GiveGymLeaderMegaReward(void);  // NEW: Enhanced gym leader rewards
void BufferGymLeaderBonusItemName(void);
bool8 HasReceivedGymLeaderBonus(u16 trainerId);
bool8 IsGymLeader(u16 trainerId);
bool8 IsRivalTrainer(u16 trainerId);
u16 GetGymLeaderBonusItem(u16 trainerId);

// Function declarations for ROAMER activation system
void ActivateRoamerForBadge(u16 trainerId);
void CheckAndActivateRoamersForExistingBadges(void);

// Function declarations for RIVAL reward system (SEPARATE)
void GiveRivalReward(void);
bool8 HasReceivedRivalReward(u16 trainerId);
bool8 IsSpecificRivalBattle(u16 trainerId, u8 rivalIndex);
u8 GetRivalBattleIndex(u16 trainerId);

// Function for post-battle message display
void ShowPostBattleRewardMessages(void);

// Function to modify money message for special rewards
void ModifyMoneyMessageForRewards(void);

// External data declaration
extern const struct GymLeaderBonusReward sGymLeaderBonusRewards[];
extern const u8 sGymLeaderBonusRewardsCount;

// BONUS reward flags are already defined in constants/flags.h
// Using existing flags to avoid redefinition warnings

// RIVAL reward flags (COMPLETELY SEPARATE system)
// These flags track rival rewards only, NO interference with any existing systems
#define FLAG_RECEIVED_RIVAL_REWARD_1    0x4F8  // Oak's Lab
#define FLAG_RECEIVED_RIVAL_REWARD_2    0x4F9  // Route 22 weak
#define FLAG_RECEIVED_RIVAL_REWARD_3    0x4FA  // Cerulean
#define FLAG_RECEIVED_RIVAL_REWARD_4    0x4FB  // SS Anne
#define FLAG_RECEIVED_RIVAL_REWARD_5    0x4FC  // Pokemon Tower
#define FLAG_RECEIVED_RIVAL_REWARD_6    0x4FD  // Silph Co
#define FLAG_RECEIVED_RIVAL_REWARD_7    0x4FE  // Route 22 strong
#define FLAG_RECEIVED_RIVAL_REWARD_8    0x4FF  // Champion

// Flags for post-battle message display (temporary flags)
#define FLAG_SHOW_GYM_REWARD_MESSAGE    0x500  // Show gym leader reward message
#define FLAG_SHOW_RIVAL_REWARD_MESSAGE  0x501  // Show rival reward message
