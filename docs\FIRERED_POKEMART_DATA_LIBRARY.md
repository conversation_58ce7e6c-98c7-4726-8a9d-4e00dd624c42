# Biblioteca de Dados de Pokemarts do FireRed

Esta biblioteca contém todos os dados extraídos do projeto .example relacionados aos pokemarts do FireRed.

## Dados Básicos do FireRed

### Informações da ROM
- **Game Code**: BPRE
- **Version**: 0 (FireRed U 1.0)
- **Type**: FRLG
- **CRC32**: DD88761C

### Configurações de Itens
- **ItemCount**: 374
- **ItemEntrySize**: 44
- **FreeSpace**: 0x800000

## Offsets dos Pokemarts

### ShopItemOffsets (FireRed U 1.0)
```c
// Array com todos os offsets dos pokemarts no FireRed
const u32 gFireRedShopOffsets[] = {
    0x1649B8,  // Shop 0  - Trainer Tower Poké Mart
    0x1676E4,  // Shop 1  - Two Island Market Stall (Initial)
    0x1676FC,  // Shop 2  - Two Island Market Stall (After Saving Lostelle)
    0x167718,  // Shop 3  - Two Island Market Stall (After Hall of Fame)
    0x167738,  // Shop 4  - Two Island Market Stall (After Ruby/Sapphire Quest)
    0x16A298,  // Shop 5  - Viridian Poké Mart
    0x16A708,  // Shop 6  - Pewter Poké Mart
    0x16ACD8,  // Shop 7  - Cerulean Poké Mart
    0x16B390,  // Shop 8  - Lavender Poké Mart
    0x16B68C,  // Shop 9  - Vermillion Poké Mart
    0x16BB38,  // Shop 10 - Celadon Department 2F South
    0x16BB74,  // Shop 11 - Celadon Department 2F North (TMs)
    0x16BC30,  // Shop 12 - Celadon Department 4F *** MAIN GAME ***
    0x16BC84,  // Shop 13 - Celadon Department 5F South *** MAIN GAME ***
    0x16BCBC,  // Shop 14 - Celadon Department 5F North *** MAIN GAME ***
    0x16D518,  // Shop 15 - Fuchsia Poké Mart
    0x16EA48,  // Shop 16 - Cinnabar Poké Mart
    0x16EAF4,  // Shop 17 - Indigo Plateau Poké Mart
    0x16EFDC,  // Shop 18 - Saffron Poké Mart
    0x170B58,  // Shop 19 - Seven Island Poké Mart
    0x1718B4,  // Shop 20 - Three Island Poké Mart
    0x171CD4,  // Shop 21 - Four Island Poké Mart
    0x171E8C   // Shop 22 - Six Island Poké Mart
};
```

## Configuração dos Shops

### Main Game Shops
```c
// Shops que fazem parte do jogo principal (apenas 3 shops!)
const u8 gFireRedMainGameShops[] = {12, 13, 14};
```

### Skip Shops
```c
// Shops que devem ser ignorados (não são pokemarts normais ou são especiais)
const u8 gFireRedSkipShops[] = {
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 
    15, 16, 17, 18, 19, 20, 21, 22
};
```

## Nomes dos Pokemarts

### Lista Completa de Nomes (FRLG)
```c
const char* gFireRedShopNames[] = {
    "Trainer Tower Poké Mart",                    // 0  - SKIP
    "Two Island Market Stall (Initial)",         // 1  - SKIP
    "Two Island Market Stall (After Saving Lostelle)", // 2  - SKIP
    "Two Island Market Stall (After Hall of Fame)", // 3  - SKIP
    "Two Island Market Stall (After Ruby/Sapphire Quest)", // 4  - SKIP
    "Viridian Poké Mart",                        // 5  - SKIP
    "Pewter Poké Mart",                          // 6  - SKIP
    "Cerulean Poké Mart",                        // 7  - SKIP
    "Lavender Poké Mart",                        // 8  - SKIP
    "Vermillion Poké Mart",                      // 9  - SKIP
    "Celadon Department 2F South",               // 10 - SKIP
    "Celadon Department 2F North (TMs)",         // 11 - SKIP
    "Celadon Department 4F",                     // 12 - MAIN GAME
    "Celadon Department 5F South",               // 13 - MAIN GAME
    "Celadon Department 5F North",               // 14 - MAIN GAME
    "Fuchsia Poké Mart",                         // 15 - SKIP
    "Cinnabar Poké Mart",                        // 16 - SKIP
    "Indigo Plateau Poké Mart",                  // 17 - SKIP
    "Saffron Poké Mart",                         // 18 - SKIP
    "Seven Island Poké Mart",                    // 19 - SKIP
    "Three Island Poké Mart",                    // 20 - SKIP
    "Four Island Poké Mart",                     // 21 - SKIP
    "Six Island Poké Mart"                       // 22 - SKIP
};
```

## Itens Regulares de Shop

### Lista de Itens Comuns (Gen3Constants.java)
```c
// Itens que aparecem em pokemarts regulares do FireRed
const u16 gRegularShopItems[] = {
    // Pokéballs (Ultra Ball até Poké Ball)
    ITEM_ULTRA_BALL,    // 1
    ITEM_GREAT_BALL,    // 2  
    ITEM_POKE_BALL,     // 4
    
    // Poções (Potion até Revive)
    ITEM_POTION,        // 13
    ITEM_ANTIDOTE,      // 14
    ITEM_BURN_HEAL,     // 15
    ITEM_ICE_HEAL,      // 16
    ITEM_AWAKENING,     // 17
    ITEM_PARALYZE_HEAL, // 18
    ITEM_FULL_RESTORE,  // 19
    ITEM_MAX_POTION,    // 20
    ITEM_HYPER_POTION,  // 21
    ITEM_SUPER_POTION,  // 22
    ITEM_FULL_HEAL,     // 23
    ITEM_REVIVE,        // 24
    
    // Repelentes (Super Repel até Repel)
    ITEM_SUPER_REPEL,   // 79
    ITEM_MAX_REPEL,     // 80
    ITEM_REPEL          // 81
};
```

### Itens OP (Overpowered)
```c
// Itens considerados muito poderosos para shops normais
const u16 gOPShopItems[] = {
    ITEM_RARE_CANDY,    // 68
    
    // Cogumelos (Tiny Mushroom até Big Mushroom)
    ITEM_TINY_MUSHROOM, // 86
    ITEM_BIG_MUSHROOM,  // 87
    
    // Itens de valor (Pearl até Nugget)
    ITEM_PEARL,         // 88
    ITEM_BIG_PEARL,     // 89
    ITEM_STARDUST,      // 90
    ITEM_STAR_PIECE,    // 91
    ITEM_NUGGET,        // 92
    
    ITEM_LUCKY_EGG      // 231
};
```

## Estrutura de Shop

### Definição da Estrutura
```c
struct FireRedShop {
    u8 shopId;
    const char* name;
    bool8 isMainGame;
    bool8 shouldSkip;
    u32 itemOffset;
    const u16* items;
    u8 itemCount;
};
```

### Exemplo de Implementação
```c
// Função para obter dados de um shop específico
struct FireRedShop* GetFireRedShopData(u8 shopId) {
    static struct FireRedShop shopData;
    
    if (shopId >= ARRAY_COUNT(gFireRedShopOffsets))
        return NULL;
        
    shopData.shopId = shopId;
    shopData.name = gFireRedShopNames[shopId];
    shopData.itemOffset = gFireRedShopOffsets[shopId];
    
    // Verificar se é main game shop
    shopData.isMainGame = FALSE;
    for (u32 i = 0; i < ARRAY_COUNT(gFireRedMainGameShops); i++) {
        if (gFireRedMainGameShops[i] == shopId) {
            shopData.isMainGame = TRUE;
            break;
        }
    }
    
    // Verificar se deve ser ignorado
    shopData.shouldSkip = FALSE;
    for (u32 i = 0; i < ARRAY_COUNT(gFireRedSkipShops); i++) {
        if (gFireRedSkipShops[i] == shopId) {
            shopData.shouldSkip = TRUE;
            break;
        }
    }
    
    return &shopData;
}
```

## Funções Utilitárias

### Leitura de Itens de Shop
```c
// Função para ler itens de um shop específico
u8 ReadShopItems(u32 shopOffset, u16* itemBuffer, u8 maxItems) {
    u8 itemCount = 0;
    u16 currentItem;
    
    while (itemCount < maxItems) {
        currentItem = *(u16*)(shopOffset + (itemCount * 2));
        
        if (currentItem == 0x0000) // Fim da lista
            break;
            
        itemBuffer[itemCount] = currentItem;
        itemCount++;
    }
    
    return itemCount;
}
```

### Escrita de Itens de Shop
```c
// Função para escrever itens em um shop específico
void WriteShopItems(u32 shopOffset, const u16* items, u8 itemCount) {
    for (u8 i = 0; i < itemCount; i++) {
        *(u16*)(shopOffset + (i * 2)) = items[i];
    }

    // Terminar lista com 0x0000
    *(u16*)(shopOffset + (itemCount * 2)) = 0x0000;
}
```

## Constantes Importantes

### Definições de Shop Types
```c
#define MART_TYPE_REGULAR   0
#define MART_TYPE_TMHM      1
#define MART_TYPE_DECOR     2
#define MART_TYPE_DECOR2    3
```

### Limites e Tamanhos
```c
#define FIRERED_SHOP_COUNT          23
#define FIRERED_MAIN_GAME_SHOPS     3
#define FIRERED_SKIP_SHOPS          20
#define MAX_SHOP_ITEMS              50
#define SHOP_ITEM_TERMINATOR        0x0000
```

## Dados Adicionais

### Outros Offsets Importantes (FireRed U 1.0)
```c
// Outros offsets relacionados a itens no FireRed
#define FIRERED_ITEM_DATA_OFFSET        0x3D4294
#define FIRERED_TM_MOVES_OFFSET         0x45A5A4
#define FIRERED_MOVE_TUTOR_DATA_OFFSET  0x459B60
#define FIRERED_PICKUP_TABLE_LOCATOR    "8B000F00850019008600230087002D"
#define FIRERED_ITEM_IMAGES_OFFSET      0x3D4294
#define FIRERED_ITEM_BALL_PIC           92
```

### Configurações de TMs
```c
#define FIRERED_TM_COUNT            50
#define FIRERED_HM_COUNT            8
#define FIRERED_MOVE_TUTOR_COUNT    15
#define FIRERED_PICKUP_ITEM_COUNT   16
```

### TM Palettes (FireRed U 1.0)
```c
// Paletas de cores para TMs no FireRed
const u32 gFireRedTmPalettes[] = {
    0xE91E64, 0xE91DC4, 0xE9201C, 0xE91E8C, 0xE91FA4, 0xE91FF4,
    0xE91EDC, 0xE91FCC, 0xE91F54, 0xE91FCC, 0xE91F04, 0xE91E14,
    0xE91EDC, 0xE91F7C, 0xE91E3C, 0xE91EB4, 0xE91DEC, 0xE91F2C
};
```

## Dados de Versões Alternativas

### FireRed U 1.1 (CRC32: 84EE4776)
```c
// Offsets para FireRed U 1.1 (diferenças principais)
const u32 gFireRedShopOffsets_v11[] = {
    0x164A30,  // Shop 0  - Trainer Tower Poké Mart
    0x16775C,  // Shop 1  - Two Island Market Stall (Initial)
    0x167774,  // Shop 2  - Two Island Market Stall (After Saving Lostelle)
    0x167790,  // Shop 3  - Two Island Market Stall (After Hall of Fame)
    0x1677B0,  // Shop 4  - Two Island Market Stall (After Ruby/Sapphire Quest)
    0x16A310,  // Shop 5  - Viridian Poké Mart
    0x16A780,  // Shop 6  - Pewter Poké Mart
    0x16AD50,  // Shop 7  - Cerulean Poké Mart
    0x16B408,  // Shop 8  - Lavender Poké Mart
    0x16B704,  // Shop 9  - Vermillion Poké Mart
    0x16BBB0,  // Shop 10 - Celadon Department 2F South
    0x16BBEC,  // Shop 11 - Celadon Department 2F North (TMs)
    0x16BCA8,  // Shop 12 - Celadon Department 4F *** MAIN GAME ***
    0x16BCFC,  // Shop 13 - Celadon Department 5F South *** MAIN GAME ***
    0x16BD34,  // Shop 14 - Celadon Department 5F North *** MAIN GAME ***
    0x16D590,  // Shop 15 - Fuchsia Poké Mart
    0x16EAC0,  // Shop 16 - Cinnabar Poké Mart
    0x16EB6C,  // Shop 17 - Indigo Plateau Poké Mart
    0x16F054,  // Shop 18 - Saffron Poké Mart
    0x170BD0,  // Shop 19 - Seven Island Poké Mart
    0x17192C,  // Shop 20 - Three Island Poké Mart
    0x171D4C,  // Shop 21 - Four Island Poké Mart
    0x171F04   // Shop 22 - Six Island Poké Mart
};
```

### Leaf Green U 1.0 (CRC32: D69C96CC)
```c
// Offsets para Leaf Green U 1.0 (compatibilidade)
const u32 gLeafGreenShopOffsets[] = {
    0x164994,  // Shop 0  - Trainer Tower Poké Mart
    0x1676C0,  // Shop 1  - Two Island Market Stall (Initial)
    0x1676D8,  // Shop 2  - Two Island Market Stall (After Saving Lostelle)
    0x1676F4,  // Shop 3  - Two Island Market Stall (After Hall of Fame)
    0x167714,  // Shop 4  - Two Island Market Stall (After Ruby/Sapphire Quest)
    0x16A274,  // Shop 5  - Viridian Poké Mart
    0x16A6E4,  // Shop 6  - Pewter Poké Mart
    0x16ACB4,  // Shop 7  - Cerulean Poké Mart
    0x16B36C,  // Shop 8  - Lavender Poké Mart
    0x16B668,  // Shop 9  - Vermillion Poké Mart
    0x16BB14,  // Shop 10 - Celadon Department 2F South
    0x16BB50,  // Shop 11 - Celadon Department 2F North (TMs)
    0x16BC0C,  // Shop 12 - Celadon Department 4F *** MAIN GAME ***
    0x16BC60,  // Shop 13 - Celadon Department 5F South *** MAIN GAME ***
    0x16BC98,  // Shop 14 - Celadon Department 5F North *** MAIN GAME ***
    0x16D4F4,  // Shop 15 - Fuchsia Poké Mart
    0x16EA24,  // Shop 16 - Cinnabar Poké Mart
    0x16EAD0,  // Shop 17 - Indigo Plateau Poké Mart
    0x16EFB8,  // Shop 18 - Saffron Poké Mart
    0x170B34,  // Shop 19 - Seven Island Poké Mart
    0x171890,  // Shop 20 - Three Island Poké Mart
    0x171CB0,  // Shop 21 - Four Island Poké Mart
    0x171E68   // Shop 22 - Six Island Poké Mart
};
```

## Funções de Detecção de Versão

### Detecção Automática de ROM
```c
// Função para detectar versão da ROM baseada no CRC32
enum FireRedVersion {
    FIRERED_U_10,
    FIRERED_U_11,
    LEAFGREEN_U_10,
    LEAFGREEN_U_11,
    UNKNOWN_VERSION
};

enum FireRedVersion DetectRomVersion(u32 crc32) {
    switch (crc32) {
        case 0xDD88761C: return FIRERED_U_10;
        case 0x84EE4776: return FIRERED_U_11;
        case 0xD69C96CC: return LEAFGREEN_U_10;
        case 0xDAFFECEC: return LEAFGREEN_U_11;
        default: return UNKNOWN_VERSION;
    }
}

// Função para obter offsets corretos baseado na versão
const u32* GetShopOffsetsForVersion(enum FireRedVersion version) {
    switch (version) {
        case FIRERED_U_10:
        case LEAFGREEN_U_10:
            return gFireRedShopOffsets;
        case FIRERED_U_11:
            return gFireRedShopOffsets_v11;
        case LEAFGREEN_U_11:
            return gLeafGreenShopOffsets;
        default:
            return NULL;
    }
}
```

## Implementação Prática para CFRU

### Sistema de Shop Progressivo
```c
// Sistema que desbloqueia itens baseado em badges
void UpdateShopInventoryByBadges(u8 shopId) {
    u8 badgeCount = GetOpenWorldBadgeCount();
    u16 newItems[MAX_SHOP_ITEMS];
    u8 itemCount = 0;

    // Itens básicos sempre disponíveis
    newItems[itemCount++] = ITEM_POKE_BALL;
    newItems[itemCount++] = ITEM_POTION;
    newItems[itemCount++] = ITEM_ANTIDOTE;

    // Desbloqueios por badge
    if (badgeCount >= 1) {
        newItems[itemCount++] = ITEM_GREAT_BALL;
        newItems[itemCount++] = ITEM_SUPER_POTION;
    }

    if (badgeCount >= 3) {
        newItems[itemCount++] = ITEM_ULTRA_BALL;
        newItems[itemCount++] = ITEM_HYPER_POTION;
        newItems[itemCount++] = ITEM_REPEL;
    }

    if (badgeCount >= 5) {
        newItems[itemCount++] = ITEM_MAX_POTION;
        newItems[itemCount++] = ITEM_SUPER_REPEL;
        newItems[itemCount++] = ITEM_FULL_HEAL;
    }

    if (badgeCount >= 8) {
        newItems[itemCount++] = ITEM_FULL_RESTORE;
        newItems[itemCount++] = ITEM_MAX_REPEL;
        newItems[itemCount++] = ITEM_REVIVE;
    }

    // Escrever novos itens no shop
    struct FireRedShop* shop = GetFireRedShopData(shopId);
    if (shop && !shop->shouldSkip) {
        WriteShopItems(shop->itemOffset, newItems, itemCount);
    }
}
```

## Notas de Implementação

1. **Formato de Dados**: Todos os itens são armazenados como u16 (2 bytes)
2. **Terminação de Lista**: Listas de itens terminam com 0x0000
3. **Main Game Shops**: Apenas shops 12, 13 e 14 são considerados do jogo principal
4. **Skip Shops**: A maioria dos shops deve ser ignorada por serem especiais
5. **Compatibilidade**: Dados baseados no FireRed U 1.0 (CRC32: DD88761C)
6. **Versões**: Suporte para FireRed/LeafGreen versões 1.0 e 1.1
7. **Offsets**: Todos os offsets são específicos da versão da ROM

Esta biblioteca fornece uma base completa para implementar sistemas de pokemarts dinâmicos no CFRU baseados nos dados originais do FireRed.
