.text
.thumb
.align 2
/*
z_effect_battle_scripts.s
	handles implementing z move effects
*/

.include "../asm_defines.s"
.include "../battle_script_macros.s"

.global BattleScript_StatsResetZMove
.global BattleScript_AllStatsUpZMove
.global BattleScript_BoostCritsZMove
.global BattleScript_FollowMeZMove
.global BattleScript_RecoverHPZMove
.global BattleScript_StatUpZMove
.global BattleScript_SetUpHealReplacementZMove
.global BattleScript_HealReplacementZMove

BattleScript_StatsResetZMove:
	setword BATTLE_STRING_LOADER StatsResetZMoveString
	printstring 0x184
	waitmessage DELAY_1SECOND
	return

BattleScript_AllStatsUpZMove:
	setword BATTLE_STRING_LOADER StatsRaisedZMoveString
	printstring 0x184
	waitmessage DELAY_1SECOND
	return

BattleScript_BoostCritsZMove:
	setword BATTLE_STRING_LOADER CritBoostedZMoveString
	printstring 0x184
	waitmessage DELAY_1SECOND
	return

BattleScript_FollowMeZMove:
	printstring 0xA4
	waitmessage DELAY_1SECOND
	return

BattleScript_RecoverHPZMove:
	orword HIT_MARKER, HITMARKER_IGNORE_SUBSTITUTE
	graphicalhpupdate BANK_SCRIPTING
	datahpupdate BANK_SCRIPTING
	setword BATTLE_STRING_LOADER HPRestoredZMoveString
	printstring 0x184
	waitmessage DELAY_1SECOND
	bicword HIT_MARKER, HITMARKER_IGNORE_SUBSTITUTE
	return
	
BattleScript_StatUpZMove:
	playanimation BANK_SCRIPTING 0x1 0x2023FD4
	setword BATTLE_STRING_LOADER StatRaisedZMoveString
	printstring 0x184
	waitmessage DELAY_1SECOND
	return

BattleScript_SetUpHealReplacementZMove:
	setword BATTLE_STRING_LOADER HPWillRestoreZMoveString
	printstring 0x184
	waitmessage DELAY_1SECOND
	return

BattleScript_HealReplacementZMove:
	orword HIT_MARKER HITMARKER_IGNORE_SUBSTITUTE
	playanimation BANK_SCRIPTING ANIM_HEALING_WISH_HEAL 0x0
	setword BATTLE_STRING_LOADER HPSwitchInRestoredZMoveString
	printstring 0x184
	waitmessage DELAY_1SECOND
	playanimation BANK_SCRIPTING ANIM_HEALING_SPARKLES 0x0
	graphicalhpupdate BANK_SCRIPTING
	datahpupdate BANK_SCRIPTING
	bicword HIT_MARKER, HITMARKER_IGNORE_SUBSTITUTE
	return

.align 2
StatsResetZMoveString: .byte 0xFD, 0x13, 0x00, 0xE6, 0xD9, 0xE8, 0xE9, 0xE6, 0xE2, 0xD9, 0xD8, 0x00, 0xDD, 0xE8, 0xE7, 0xFE, 0xD8, 0xD9, 0xD7, 0xE6, 0xD9, 0xD5, 0xE7, 0xD9, 0xD8, 0x00, 0xE7, 0xE8, 0xD5, 0xE8, 0xE7, 0x00, 0xE8, 0xE3, 0x00, 0xE2, 0xE3, 0xE6, 0xE1, 0xD5, 0xE0, 0x00, 0xE9, 0xE7, 0xDD, 0xE2, 0xDB, 0xFA, 0xDD, 0xE8, 0xE7, 0x00, 0xD4, 0xAE, 0xCA, 0xE3, 0xEB, 0xD9, 0xE6, 0xAB, 0xFF
StatRaisedZMoveString: .byte 0xFD, 0x13, 0x00, 0xD6, 0xE3, 0xE3, 0xE7, 0xE8, 0xD9, 0xD8, 0x00, 0xDD, 0xE8, 0xE7, 0xFE, 0xFD, 0x00, 0x0, 0xFD, 0x01, 0xE9, 0xE7, 0xDD, 0xE2, 0xDB, 0x00, 0xDD, 0xE8, 0xE7, 0x00, 0xD4, 0xAE, 0xCA, 0xE3, 0xEB, 0xD9, 0xE6, 0xAB, 0xFF
StatsRaisedZMoveString: .byte 0xFD, 0x13, 0x00, 0xD6, 0xE3, 0xE3, 0xE7, 0xE8, 0xD9, 0xD8, 0x00, 0xDD, 0xE8, 0xE7, 0xFE, 0xE7, 0xE8, 0xD5, 0xE8, 0xE7, 0x00, 0xE9, 0xE7, 0xDD, 0xE2, 0xDB, 0x00, 0xDD, 0xE8, 0xE7, 0x00, 0xD4, 0xAE, 0xCA, 0xE3, 0xEB, 0xD9, 0xE6, 0xAB, 0xFF
CritBoostedZMoveString: .byte 0xFD, 0x13, 0x00, 0xD6, 0xE3, 0xE3, 0xE7, 0xE8, 0xD9, 0xD8, 0x00, 0xDD, 0xE8, 0xE7, 0xFE, 0xD7, 0xE6, 0xDD, 0xE8, 0xDD, 0xD7, 0xD5, 0xE0, 0xAE, 0xDC, 0xDD, 0xE8, 0x00, 0xE6, 0xD5, 0xE8, 0xDD, 0xE3, 0x00, 0xE9, 0xE7, 0xDD, 0xE2, 0xDB, 0x00, 0xDD, 0xE8, 0xE7, 0x00, 0xD4, 0xAE, 0xCA, 0xE3, 0xEB, 0xD9, 0xE6, 0xAB, 0xFF
HPRestoredZMoveString: .byte 0xFD, 0x13, 0x00, 0xE6, 0xD9, 0xE7, 0xE8, 0xE3, 0xE6, 0xD9, 0xD8, 0x00, 0xDD, 0xE8, 0xE7, 0xFE, 0xC2, 0xCA, 0x00, 0xE9, 0xE7, 0xDD, 0xE2, 0xDB, 0x00, 0xDD, 0xE8, 0xE7, 0x00, 0xD4, 0xAE, 0xCA, 0xE3, 0xEB, 0xD9, 0xE6, 0xAB, 0xFF
HPWillRestoreZMoveString: .byte 0xFD, 0x13, 0x00, 0xEB, 0xDD, 0xE0, 0xE0, 0x00, 0xE6, 0xD9, 0xE7, 0xE8, 0xE3, 0xE6, 0xD9, 0x00, 0xDD, 0xE8, 0xE7, 0xFE, 0xE6, 0xD9, 0xE4, 0xE0, 0xD5, 0xD7, 0xD9, 0xE1, 0xD9, 0xE2, 0xE8, 0xB4, 0xE7, 0x00, 0xC2, 0xCA, 0x00, 0xE9, 0xE7, 0xDD, 0xE2, 0xDB, 0x00, 0xDD, 0xE8, 0xE7, 0x00, 0xD4, 0xAE, 0xCA, 0xE3, 0xEB, 0xD9, 0xE6, 0xAB, 0xFF
HPSwitchInRestoredZMoveString: .byte 0xFD, 0x13, 0xB4, 0xE7, 0x00, 0xC2, 0xCA, 0x00, 0xEB, 0xD5, 0xE7, 0xFE, 0xE6, 0xD9, 0xE7, 0xE8, 0xE3, 0xE6, 0xD9, 0xD8, 0x00, 0xD6, 0xED, 0x00, 0xE8, 0xDC, 0xD9, 0x00, 0xD4, 0xAE, 0xCA, 0xE3, 0xEB, 0xD9, 0xE6, 0xAB, 0xFF
