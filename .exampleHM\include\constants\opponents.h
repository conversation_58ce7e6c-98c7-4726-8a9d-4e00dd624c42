#ifndef GUARD_CONSTANTS_OPPONENTS_H
#define GUARD_CONSTANTS_OPPONENTS_H

#define TRAINER_NONE                               0
// Dummy trainers for all the RS trainer classes
#define TRAINER_AQUA_LEADER                        1
#define TRAINER_AQUA_GRUNT_M                       2
#define TRAINER_AQUA_GRUNT_F                       3
#define TRAINER_RS_AROMA_LADY                      4
#define TRAINER_RS_RUIN_MANIAC                     5
#define TRAINER_INTERVIEWER                        6
#define TRAINER_RS_TUBER_F                         7
#define TRAINER_RS_TUBER_M                         8
#define TRAINER_RS_COOLTRAINER_M                   9
#define TRAINER_RS_COOLTRAINER_F                  10
#define TRAINER_HEX_MANIAC                        11
#define TRAINER_RS_LADY                           12
#define TRAINER_RS_BEAUTY                         13
#define TRAINER_RICH_BOY                          14
#define TRAINER_RS_POKEMANIAC                     15
#define TRAINER_RS_SWIMMER_M                      16
#define TRAINER_RS_BLACK_BELT                     17
#define TRAINER_GUITARIST                         18
#define TRAINER_KINDLER                           19
#define TRAINER_RS_CAMPER                         20
#define TRAINER_BUG_MANIAC                        21
#define TRAINER_RS_PSYCHIC_M                      22
#define TRAINER_RS_PSYCHIC_F                      23
#define TRAINER_RS_GENTLEMAN                      24
#define TRAINER_ELITE_FOUR_SIDNEY                 25
#define TRAINER_ELITE_FOUR_PHOEBE                 26
#define TRAINER_LEADER_ROXANNE                    27
#define TRAINER_LEADER_BRAWLY                     28
#define TRAINER_LEADER_TATE_LIZA                  29
#define TRAINER_SCHOOL_KID_M                      30
#define TRAINER_SCHOOL_KID_F                      31
#define TRAINER_SR_AND_JR                         32
#define TRAINER_POKEFAN_M                         33
#define TRAINER_POKEFAN_F                         34
#define TRAINER_EXPERT_M                          35
#define TRAINER_EXPERT_F                          36
#define TRAINER_RS_YOUNGSTER                      37
#define TRAINER_RS_CHAMPION                       38
#define TRAINER_RS_FISHERMAN                      39
#define TRAINER_CYCLING_TRIATHLETE_M              40
#define TRAINER_CYCLING_TRIATHLETE_F              41
#define TRAINER_RUNNING_TRIATHLETE_M              42
#define TRAINER_RUNNING_TRIATHLETE_F              43
#define TRAINER_SWIMMING_TRIATHLETE_M             44
#define TRAINER_SWIMMING_TRIATHLETE_F             45
#define TRAINER_DRAGON_TAMER                      46
#define TRAINER_RS_BIRD_KEEPER                    47
#define TRAINER_NINJA_BOY                         48
#define TRAINER_BATTLE_GIRL                       49
#define TRAINER_PARASOL_LADY                      50
#define TRAINER_RS_SWIMMER_F                      51
#define TRAINER_RS_PICNICKER                      52
#define TRAINER_RS_TWINS                          53
#define TRAINER_RS_SAILOR                         54
#define TRAINER_BOARDER_M                         55
#define TRAINER_BOARDER_F                         56
#define TRAINER_COLLECTOR                         57
#define TRAINER_WALLY                             58
#define TRAINER_BRENDAN                           59
#define TRAINER_BRENDAN_2                         60
#define TRAINER_BRENDAN_3                         61
#define TRAINER_MAY                               62
#define TRAINER_MAY_2                             63
#define TRAINER_MAY_3                             64
#define TRAINER_RS_PKMN_BREEDER_M                 65
#define TRAINER_RS_PKMN_BREEDER_F                 66
#define TRAINER_RS_PKMN_RANGER_M                  67
#define TRAINER_RS_PKMN_RANGER_F                  68
#define TRAINER_MAGMA_LEADER                      69
#define TRAINER_MAGMA_GRUNT_M                     70
#define TRAINER_MAMGA_GRUNT_F                     71
#define TRAINER_RS_LASS                           72
#define TRAINER_RS_BUG_CATCHER                    73
#define TRAINER_RS_HIKER                          74
#define TRAINER_RS_YOUNG_COUPLE                   75
#define TRAINER_OLD_COUPLE                        76
#define TRAINER_RS_SIS_AND_BRO                    77
#define TRAINER_AQUA_ADMIN_MATT                   78
#define TRAINER_AQUA_ADMIN_SHELLY                 79
#define TRAINER_MAGMA_ADMIN_TABITHA               80
#define TRAINER_MAGMA_ADMIN_COURTNEY              81
#define TRAINER_LEADER_WATTSON                    82
#define TRAINER_LEADER_FLANNERY                   83
#define TRAINER_LEADER_NORMAN                     84
#define TRAINER_LEADER_WINONA                     85
#define TRAINER_LEADER_WALLACE                    86
#define TRAINER_ELITE_FOUR_GLACIA                 87
#define TRAINER_ELITE_FOUR_DRAKE                  88
// Actual FRLG trainers start here
#define TRAINER_YOUNGSTER_BEN                     89
#define TRAINER_YOUNGSTER_CALVIN                  90
#define TRAINER_YOUNGSTER_JOSH                    91
#define TRAINER_YOUNGSTER_TIMMY                   92
#define TRAINER_YOUNGSTER_JOEY                    93
#define TRAINER_YOUNGSTER_DAN                     94
#define TRAINER_YOUNGSTER_CHAD                    95
#define TRAINER_YOUNGSTER_TYLER                   96
#define TRAINER_YOUNGSTER_EDDIE                   97
#define TRAINER_YOUNGSTER_DILLON                  98
#define TRAINER_YOUNGSTER_YASU                    99
#define TRAINER_YOUNGSTER_DAVE                   100
#define TRAINER_YOUNGSTER_BEN_2                  101
#define TRAINER_BUG_CATCHER_RICK                 102
#define TRAINER_BUG_CATCHER_DOUG                 103
#define TRAINER_BUG_CATCHER_SAMMY                104
#define TRAINER_BUG_CATCHER_COLTON               105
#define TRAINER_BUG_CATCHER_GREG                 106
#define TRAINER_BUG_CATCHER_JAMES                107
#define TRAINER_BUG_CATCHER_KENT                 108
#define TRAINER_BUG_CATCHER_ROBBY                109
#define TRAINER_BUG_CATCHER_CALE                 110
#define TRAINER_BUG_CATCHER_KEIGO                111
#define TRAINER_BUG_CATCHER_ELIJAH               112
#define TRAINER_BUG_CATCHER_2                    113
#define TRAINER_BUG_CATCHER_BRENT                114
#define TRAINER_BUG_CATCHER_CONNER               115
#define TRAINER_LASS_JANICE                      116
#define TRAINER_LASS_SALLY                       117
#define TRAINER_LASS_ROBIN                       118
#define TRAINER_LASS_CRISSY                      119
#define TRAINER_LASS_MIRIAM                      120
#define TRAINER_LASS_IRIS                        121
#define TRAINER_LASS_RELI                        122
#define TRAINER_LASS_ALI                         123
#define TRAINER_LASS_2                           124
#define TRAINER_LASS_HALEY                       125
#define TRAINER_LASS_ANN                         126
#define TRAINER_LASS_DAWN                        127
#define TRAINER_LASS_PAIGE                       128
#define TRAINER_LASS_ANDREA                      129
#define TRAINER_LASS_MEGAN                       130
#define TRAINER_LASS_JULIA                       131
#define TRAINER_LASS_KAY                         132
#define TRAINER_LASS_LISA                        133
#define TRAINER_SAILOR_EDMOND                    134
#define TRAINER_SAILOR_TREVOR                    135
#define TRAINER_SAILOR_LEONARD                   136
#define TRAINER_SAILOR_DUNCAN                    137
#define TRAINER_SAILOR_HUEY                      138
#define TRAINER_SAILOR_DYLAN                     139
#define TRAINER_SAILOR_PHILLIP                   140
#define TRAINER_SAILOR_DWAYNE                    141
#define TRAINER_CAMPER_LIAM                      142
#define TRAINER_CAMPER_SHANE                     143
#define TRAINER_CAMPER_ETHAN                     144
#define TRAINER_CAMPER_RICKY                     145
#define TRAINER_CAMPER_JEFF                      146
#define TRAINER_CAMPER_2                         147
#define TRAINER_CAMPER_CHRIS                     148
#define TRAINER_CAMPER_DREW                      149
#define TRAINER_PICNICKER_DIANA                  150
#define TRAINER_PICNICKER_NANCY                  151
#define TRAINER_PICNICKER_ISABELLE               152
#define TRAINER_PICNICKER_KELSEY                 153
#define TRAINER_PICNICKER_ALICIA                 154
#define TRAINER_PICNICKER_CAITLIN                155
#define TRAINER_PICNICKER_HEIDI                  156
#define TRAINER_PICNICKER_CAROL                  157
#define TRAINER_PICNICKER_SOFIA                  158
#define TRAINER_PICNICKER_MARTHA                 159
#define TRAINER_PICNICKER_TINA                   160
#define TRAINER_PICNICKER_HANNAH                 161
#define TRAINER_POKEMANIAC_MARK                  162
#define TRAINER_POKEMANIAC_HERMAN                163
#define TRAINER_POKEMANIAC_COOPER                164
#define TRAINER_POKEMANIAC_STEVE                 165
#define TRAINER_POKEMANIAC_WINSTON               166
#define TRAINER_POKEMANIAC_DAWSON                167
#define TRAINER_POKEMANIAC_ASHTON                168
#define TRAINER_SUPER_NERD_JOVAN                 169
#define TRAINER_SUPER_NERD_MIGUEL                170
#define TRAINER_SUPER_NERD_AIDAN                 171
#define TRAINER_SUPER_NERD_GLENN                 172
#define TRAINER_SUPER_NERD_LESLIE                173
#define TRAINER_SUPER_NERD_1                     174
#define TRAINER_SUPER_NERD_2                     175
#define TRAINER_SUPER_NERD_3                     176
#define TRAINER_SUPER_NERD_ERIK                  177
#define TRAINER_SUPER_NERD_AVERY                 178
#define TRAINER_SUPER_NERD_DEREK                 179
#define TRAINER_SUPER_NERD_ZAC                   180
#define TRAINER_HIKER_MARCOS                     181
#define TRAINER_HIKER_FRANKLIN                   182
#define TRAINER_HIKER_NOB                        183
#define TRAINER_HIKER_WAYNE                      184
#define TRAINER_HIKER_ALAN                       185
#define TRAINER_HIKER_BRICE                      186
#define TRAINER_HIKER_CLARK                      187
#define TRAINER_HIKER_TRENT                      188
#define TRAINER_HIKER_DUDLEY                     189
#define TRAINER_HIKER_ALLEN                      190
#define TRAINER_HIKER_ERIC                       191
#define TRAINER_HIKER_LENNY                      192
#define TRAINER_HIKER_OLIVER                     193
#define TRAINER_HIKER_LUCAS                      194
#define TRAINER_BIKER_JARED                      195
#define TRAINER_BIKER_MALIK                      196
#define TRAINER_BIKER_ERNEST                     197
#define TRAINER_BIKER_ALEX                       198
#define TRAINER_BIKER_LAO                        199
#define TRAINER_BIKER_1                          200
#define TRAINER_BIKER_HIDEO                      201
#define TRAINER_BIKER_RUBEN                      202
#define TRAINER_BIKER_BILLY                      203
#define TRAINER_BIKER_NIKOLAS                    204
#define TRAINER_BIKER_JAXON                      205
#define TRAINER_BIKER_WILLIAM                    206
#define TRAINER_BIKER_LUKAS                      207
#define TRAINER_BIKER_ISAAC                      208
#define TRAINER_BIKER_GERALD                     209
#define TRAINER_BURGLAR_1                        210
#define TRAINER_BURGLAR_2                        211
#define TRAINER_BURGLAR_3                        212
#define TRAINER_BURGLAR_QUINN                    213
#define TRAINER_BURGLAR_RAMON                    214
#define TRAINER_BURGLAR_DUSTY                    215
#define TRAINER_BURGLAR_ARNIE                    216
#define TRAINER_BURGLAR_4                        217
#define TRAINER_BURGLAR_SIMON                    218
#define TRAINER_BURGLAR_LEWIS                    219
#define TRAINER_ENGINEER_BAILY                   220
#define TRAINER_ENGINEER_BRAXTON                 221
#define TRAINER_ENGINEER_BERNIE                  222
#define TRAINER_FISHERMAN_DALE                   223
#define TRAINER_FISHERMAN_BARNY                  224
#define TRAINER_FISHERMAN_NED                    225
#define TRAINER_FISHERMAN_CHIP                   226
#define TRAINER_FISHERMAN_HANK                   227
#define TRAINER_FISHERMAN_ELLIOT                 228
#define TRAINER_FISHERMAN_RONALD                 229
#define TRAINER_FISHERMAN_CLAUDE                 230
#define TRAINER_FISHERMAN_WADE                   231
#define TRAINER_FISHERMAN_NOLAN                  232
#define TRAINER_FISHERMAN_ANDREW                 233
#define TRAINER_SWIMMER_MALE_LUIS                234
#define TRAINER_SWIMMER_MALE_RICHARD             235
#define TRAINER_SWIMMER_MALE_REECE               236
#define TRAINER_SWIMMER_MALE_MATTHEW             237
#define TRAINER_SWIMMER_MALE_DOUGLAS             238
#define TRAINER_SWIMMER_MALE_DAVID               239
#define TRAINER_SWIMMER_MALE_TONY                240
#define TRAINER_SWIMMER_MALE_AXLE                241
#define TRAINER_SWIMMER_MALE_BARRY               242
#define TRAINER_SWIMMER_MALE_DEAN                243
#define TRAINER_SWIMMER_MALE_DARRIN              244
#define TRAINER_SWIMMER_MALE_SPENCER             245
#define TRAINER_SWIMMER_MALE_JACK                246
#define TRAINER_SWIMMER_MALE_JEROME              247
#define TRAINER_SWIMMER_MALE_ROLAND              248
#define TRAINER_CUE_BALL_KOJI                    249
#define TRAINER_CUE_BALL_LUKE                    250
#define TRAINER_CUE_BALL_CAMRON                  251
#define TRAINER_CUE_BALL_RAUL                    252
#define TRAINER_CUE_BALL_ISAIAH                  253
#define TRAINER_CUE_BALL_ZEEK                    254
#define TRAINER_CUE_BALL_JAMAL                   255
#define TRAINER_CUE_BALL_COREY                   256
#define TRAINER_CUE_BALL_CHASE                   257
#define TRAINER_GAMER_HUGO                       258
#define TRAINER_GAMER_JASPER                     259
#define TRAINER_GAMER_DIRK                       260
#define TRAINER_GAMER_DARIAN                     261
#define TRAINER_GAMER_STAN                       262
#define TRAINER_GAMER_1                          263
#define TRAINER_GAMER_RICH                       264
#define TRAINER_BEAUTY_BRIDGET                   265
#define TRAINER_BEAUTY_TAMIA                     266
#define TRAINER_BEAUTY_LORI                      267
#define TRAINER_BEAUTY_LOLA                      268
#define TRAINER_BEAUTY_SHEILA                    269
#define TRAINER_SWIMMER_FEMALE_TIFFANY           270
#define TRAINER_SWIMMER_FEMALE_NORA              271
#define TRAINER_SWIMMER_FEMALE_MELISSA           272
#define TRAINER_BEAUTY_GRACE                     273
#define TRAINER_BEAUTY_OLIVIA                    274
#define TRAINER_BEAUTY_LAUREN                    275
#define TRAINER_SWIMMER_FEMALE_ANYA              276
#define TRAINER_SWIMMER_FEMALE_ALICE             277
#define TRAINER_SWIMMER_FEMALE_CONNIE            278
#define TRAINER_SWIMMER_FEMALE_SHIRLEY           279
#define TRAINER_PSYCHIC_JOHAN                    280
#define TRAINER_PSYCHIC_TYRON                    281
#define TRAINER_PSYCHIC_CAMERON                  282
#define TRAINER_PSYCHIC_PRESTON                  283
#define TRAINER_ROCKER_RANDALL                   284
#define TRAINER_ROCKER_LUCA                      285
#define TRAINER_JUGGLER_DALTON                   286
#define TRAINER_JUGGLER_NELSON                   287
#define TRAINER_JUGGLER_KIRK                     288
#define TRAINER_JUGGLER_SHAWN                    289
#define TRAINER_JUGGLER_GREGORY                  290
#define TRAINER_JUGGLER_EDWARD                   291
#define TRAINER_JUGGLER_KAYDEN                   292
#define TRAINER_JUGGLER_NATE                     293
#define TRAINER_TAMER_PHIL                       294
#define TRAINER_TAMER_EDGAR                      295
#define TRAINER_TAMER_JASON                      296
#define TRAINER_TAMER_COLE                       297
#define TRAINER_TAMER_VINCENT                    298
#define TRAINER_TAMER_JOHN                       299
#define TRAINER_BIRD_KEEPER_SEBASTIAN            300
#define TRAINER_BIRD_KEEPER_PERRY                301
#define TRAINER_BIRD_KEEPER_ROBERT               302
#define TRAINER_BIRD_KEEPER_DONALD               303
#define TRAINER_BIRD_KEEPER_BENNY                304
#define TRAINER_BIRD_KEEPER_EDWIN                305
#define TRAINER_BIRD_KEEPER_CHESTER              306
#define TRAINER_BIRD_KEEPER_WILTON               307
#define TRAINER_BIRD_KEEPER_RAMIRO               308
#define TRAINER_BIRD_KEEPER_JACOB                309
#define TRAINER_BIRD_KEEPER_ROGER                310
#define TRAINER_BIRD_KEEPER_REED                 311
#define TRAINER_BIRD_KEEPER_KEITH                312
#define TRAINER_BIRD_KEEPER_CARTER               313
#define TRAINER_BIRD_KEEPER_MITCH                314
#define TRAINER_BIRD_KEEPER_BECK                 315
#define TRAINER_BIRD_KEEPER_MARLON               316
#define TRAINER_BLACK_BELT_KOICHI                317
#define TRAINER_BLACK_BELT_MIKE                  318
#define TRAINER_BLACK_BELT_HIDEKI                319
#define TRAINER_BLACK_BELT_AARON                 320
#define TRAINER_BLACK_BELT_HITOSHI               321
#define TRAINER_BLACK_BELT_ATSUSHI               322
#define TRAINER_BLACK_BELT_KIYO                  323
#define TRAINER_BLACK_BELT_TAKASHI               324
#define TRAINER_BLACK_BELT_DAISUKE               325
#define TRAINER_RIVAL_OAKS_LAB_SQUIRTLE          326
#define TRAINER_RIVAL_OAKS_LAB_BULBASAUR         327
#define TRAINER_RIVAL_OAKS_LAB_CHARMANDER        328
#define TRAINER_RIVAL_ROUTE22_EARLY_SQUIRTLE     329
#define TRAINER_RIVAL_ROUTE22_EARLY_BULBASAUR    330
#define TRAINER_RIVAL_ROUTE22_EARLY_CHARMANDER   331
#define TRAINER_RIVAL_CERULEAN_SQUIRTLE          332
#define TRAINER_RIVAL_CERULEAN_BULBASAUR         333
#define TRAINER_RIVAL_CERULEAN_CHARMANDER        334
#define TRAINER_SCIENTIST_TED                    335
#define TRAINER_SCIENTIST_CONNOR                 336
#define TRAINER_SCIENTIST_JERRY                  337
#define TRAINER_SCIENTIST_JOSE                   338
#define TRAINER_SCIENTIST_RODNEY                 339
#define TRAINER_SCIENTIST_BEAU                   340
#define TRAINER_SCIENTIST_TAYLOR                 341
#define TRAINER_SCIENTIST_JOSHUA                 342
#define TRAINER_SCIENTIST_PARKER                 343
#define TRAINER_SCIENTIST_ED                     344
#define TRAINER_SCIENTIST_TRAVIS                 345
#define TRAINER_SCIENTIST_BRAYDON                346
#define TRAINER_SCIENTIST_IVAN                   347
#define TRAINER_BOSS_GIOVANNI                    348
#define TRAINER_BOSS_GIOVANNI_2                  349
#define TRAINER_LEADER_GIOVANNI                  350
#define TRAINER_TEAM_ROCKET_GRUNT                351
#define TRAINER_TEAM_ROCKET_GRUNT_2              352
#define TRAINER_TEAM_ROCKET_GRUNT_3              353
#define TRAINER_TEAM_ROCKET_GRUNT_4              354
#define TRAINER_TEAM_ROCKET_GRUNT_5              355
#define TRAINER_TEAM_ROCKET_GRUNT_6              356
#define TRAINER_TEAM_ROCKET_GRUNT_7              357
#define TRAINER_TEAM_ROCKET_GRUNT_8              358
#define TRAINER_TEAM_ROCKET_GRUNT_9              359
#define TRAINER_TEAM_ROCKET_GRUNT_10             360
#define TRAINER_TEAM_ROCKET_GRUNT_11             361
#define TRAINER_TEAM_ROCKET_GRUNT_12             362
#define TRAINER_TEAM_ROCKET_GRUNT_13             363
#define TRAINER_TEAM_ROCKET_GRUNT_14             364
#define TRAINER_TEAM_ROCKET_GRUNT_15             365
#define TRAINER_TEAM_ROCKET_GRUNT_16             366
#define TRAINER_TEAM_ROCKET_GRUNT_17             367
#define TRAINER_TEAM_ROCKET_GRUNT_18             368
#define TRAINER_TEAM_ROCKET_GRUNT_19             369
#define TRAINER_TEAM_ROCKET_GRUNT_20             370
#define TRAINER_TEAM_ROCKET_GRUNT_21             371
#define TRAINER_TEAM_ROCKET_GRUNT_22             372
#define TRAINER_TEAM_ROCKET_GRUNT_23             373
#define TRAINER_TEAM_ROCKET_GRUNT_24             374
#define TRAINER_TEAM_ROCKET_GRUNT_25             375
#define TRAINER_TEAM_ROCKET_GRUNT_26             376
#define TRAINER_TEAM_ROCKET_GRUNT_27             377
#define TRAINER_TEAM_ROCKET_GRUNT_28             378
#define TRAINER_TEAM_ROCKET_GRUNT_29             379
#define TRAINER_TEAM_ROCKET_GRUNT_30             380
#define TRAINER_TEAM_ROCKET_GRUNT_31             381
#define TRAINER_TEAM_ROCKET_GRUNT_32             382
#define TRAINER_TEAM_ROCKET_GRUNT_33             383
#define TRAINER_TEAM_ROCKET_GRUNT_34             384
#define TRAINER_TEAM_ROCKET_GRUNT_35             385
#define TRAINER_TEAM_ROCKET_GRUNT_36             386
#define TRAINER_TEAM_ROCKET_GRUNT_37             387
#define TRAINER_TEAM_ROCKET_GRUNT_38             388
#define TRAINER_TEAM_ROCKET_GRUNT_39             389
#define TRAINER_TEAM_ROCKET_GRUNT_40             390
#define TRAINER_TEAM_ROCKET_GRUNT_41             391
#define TRAINER_COOLTRAINER_SAMUEL               392
#define TRAINER_COOLTRAINER_GEORGE               393
#define TRAINER_COOLTRAINER_COLBY                394
#define TRAINER_COOLTRAINER_PAUL                 395
#define TRAINER_COOLTRAINER_ROLANDO              396
#define TRAINER_COOLTRAINER_GILBERT              397
#define TRAINER_COOLTRAINER_OWEN                 398
#define TRAINER_COOLTRAINER_BERKE                399
#define TRAINER_COOLTRAINER_YUJI                 400
#define TRAINER_COOLTRAINER_WARREN               401
#define TRAINER_COOLTRAINER_MARY                 402
#define TRAINER_COOLTRAINER_CAROLINE             403
#define TRAINER_COOLTRAINER_ALEXA                404
#define TRAINER_COOLTRAINER_SHANNON              405
#define TRAINER_COOLTRAINER_NAOMI                406
#define TRAINER_COOLTRAINER_BROOKE               407
#define TRAINER_COOLTRAINER_AUSTINA              408
#define TRAINER_COOLTRAINER_JULIE                409
#define TRAINER_ELITE_FOUR_LORELEI               410
#define TRAINER_ELITE_FOUR_BRUNO                 411
#define TRAINER_ELITE_FOUR_AGATHA                412
#define TRAINER_ELITE_FOUR_LANCE                 413
#define TRAINER_LEADER_BROCK                     414
#define TRAINER_LEADER_MISTY                     415
#define TRAINER_LEADER_LT_SURGE                  416
#define TRAINER_LEADER_ERIKA                     417
#define TRAINER_LEADER_KOGA                      418
#define TRAINER_LEADER_BLAINE                    419
#define TRAINER_LEADER_SABRINA                   420
#define TRAINER_GENTLEMAN_THOMAS                 421
#define TRAINER_GENTLEMAN_ARTHUR                 422
#define TRAINER_GENTLEMAN_TUCKER                 423
#define TRAINER_GENTLEMAN_NORTON                 424
#define TRAINER_GENTLEMAN_WALTER                 425
#define TRAINER_RIVAL_SS_ANNE_SQUIRTLE           426
#define TRAINER_RIVAL_SS_ANNE_BULBASAUR          427
#define TRAINER_RIVAL_SS_ANNE_CHARMANDER         428
#define TRAINER_RIVAL_POKEMON_TOWER_SQUIRTLE     429
#define TRAINER_RIVAL_POKEMON_TOWER_BULBASAUR    430
#define TRAINER_RIVAL_POKEMON_TOWER_CHARMANDER   431
#define TRAINER_RIVAL_SILPH_SQUIRTLE             432
#define TRAINER_RIVAL_SILPH_BULBASAUR            433
#define TRAINER_RIVAL_SILPH_CHARMANDER           434
#define TRAINER_RIVAL_ROUTE22_LATE_SQUIRTLE      435
#define TRAINER_RIVAL_ROUTE22_LATE_BULBASAUR     436
#define TRAINER_RIVAL_ROUTE22_LATE_CHARMANDER    437
#define TRAINER_CHAMPION_FIRST_SQUIRTLE          438
#define TRAINER_CHAMPION_FIRST_BULBASAUR         439
#define TRAINER_CHAMPION_FIRST_CHARMANDER        440
#define TRAINER_CHANNELER_PATRICIA               441
#define TRAINER_CHANNELER_CARLY                  442
#define TRAINER_CHANNELER_HOPE                   443
#define TRAINER_CHANNELER_PAULA                  444
#define TRAINER_CHANNELER_LAUREL                 445
#define TRAINER_CHANNELER_JODY                   446
#define TRAINER_CHANNELER_TAMMY                  447
#define TRAINER_CHANNELER_RUTH                   448
#define TRAINER_CHANNELER_KARINA                 449
#define TRAINER_CHANNELER_JANAE                  450
#define TRAINER_CHANNELER_ANGELICA               451
#define TRAINER_CHANNELER_EMILIA                 452
#define TRAINER_CHANNELER_JENNIFER               453
#define TRAINER_CHANNELER_1                      454
#define TRAINER_CHANNELER_2                      455
#define TRAINER_CHANNELER_3                      456
#define TRAINER_CHANNELER_4                      457
#define TRAINER_CHANNELER_5                      458
#define TRAINER_CHANNELER_6                      459
#define TRAINER_CHANNELER_7                      460
#define TRAINER_CHANNELER_8                      461
#define TRAINER_CHANNELER_AMANDA                 462
#define TRAINER_CHANNELER_STACY                  463
#define TRAINER_CHANNELER_TASHA                  464
#define TRAINER_HIKER_JEREMY                     465
#define TRAINER_PICNICKER_ALMA                   466
#define TRAINER_PICNICKER_SUSIE                  467
#define TRAINER_PICNICKER_VALERIE                468
#define TRAINER_PICNICKER_GWEN                   469
#define TRAINER_BIKER_VIRGIL                     470
#define TRAINER_CAMPER_FLINT                     471
#define TRAINER_PICNICKER_MISSY                  472
#define TRAINER_PICNICKER_IRENE                  473
#define TRAINER_PICNICKER_DANA                   474
#define TRAINER_PICNICKER_ARIANA                 475
#define TRAINER_PICNICKER_LEAH                   476
#define TRAINER_CAMPER_JUSTIN                    477
#define TRAINER_PICNICKER_YAZMIN                 478
#define TRAINER_PICNICKER_KINDRA                 479
#define TRAINER_PICNICKER_BECKY                  480
#define TRAINER_PICNICKER_CELIA                  481
#define TRAINER_GENTLEMAN_BROOKS                 482
#define TRAINER_GENTLEMAN_LAMAR                  483
#define TRAINER_TWINS_ELI_ANNE                   484
#define TRAINER_COOL_COUPLE_RAY_TYRA             485
#define TRAINER_YOUNG_COUPLE_GIA_JES             486
#define TRAINER_TWINS_KIRI_JAN                   487
#define TRAINER_CRUSH_KIN_RON_MYA                488
#define TRAINER_YOUNG_COUPLE_LEA_JED             489
#define TRAINER_SIS_AND_BRO_LIA_LUC              490
#define TRAINER_SIS_AND_BRO_LIL_IAN              491
#define TRAINER_BUG_CATCHER_3                    492
#define TRAINER_BUG_CATCHER_4                    493
#define TRAINER_BUG_CATCHER_5                    494
#define TRAINER_BUG_CATCHER_6                    495
#define TRAINER_BUG_CATCHER_7                    496
#define TRAINER_BUG_CATCHER_8                    497
#define TRAINER_YOUNGSTER_BEN_3                  498
#define TRAINER_YOUNGSTER_BEN_4                  499
#define TRAINER_YOUNGSTER_CHAD_2                 500
#define TRAINER_LASS_RELI_2                      501
#define TRAINER_LASS_RELI_3                      502
#define TRAINER_YOUNGSTER_TIMMY_2                503
#define TRAINER_YOUNGSTER_TIMMY_3                504
#define TRAINER_YOUNGSTER_TIMMY_4                505
#define TRAINER_YOUNGSTER_CHAD_3                 506
#define TRAINER_LASS_JANICE_2                    507
#define TRAINER_LASS_JANICE_3                    508
#define TRAINER_YOUNGSTER_CHAD_4                 509
#define TRAINER_HIKER_FRANKLIN_2                 510
#define TRAINER_PKMN_PROF_PROF_OAK               511
#define TRAINER_PLAYER_BRENDAN                   512
#define TRAINER_PLAYER_MAY                       513
#define TRAINER_PLAYER_RED                       514
#define TRAINER_PLAYER_LEAF                      515
#define TRAINER_TEAM_ROCKET_GRUNT_42             516
#define TRAINER_PSYCHIC_JACLYN                   517
#define TRAINER_CRUSH_GIRL_SHARON                518
#define TRAINER_TUBER_AMIRA                      519
#define TRAINER_PKMN_BREEDER_ALIZE               520
#define TRAINER_PKMN_RANGER_NICOLAS              521
#define TRAINER_PKMN_RANGER_MADELINE             522
#define TRAINER_AROMA_LADY_NIKKI                 523
#define TRAINER_RUIN_MANIAC_STANLY               524
#define TRAINER_LADY_JACKI                       525
#define TRAINER_PAINTER_DAISY                    526
#define TRAINER_BIKER_GOON                       527
#define TRAINER_BIKER_GOON_2                     528
#define TRAINER_BIKER_GOON_3                     529
#define TRAINER_BIKER_2                          530
#define TRAINER_BUG_CATCHER_ANTHONY              531
#define TRAINER_BUG_CATCHER_CHARLIE              532
#define TRAINER_TWINS_ELI_ANNE_2                 533
#define TRAINER_YOUNGSTER_JOHNSON                534
#define TRAINER_BIKER_RICARDO                    535
#define TRAINER_BIKER_JAREN                      536
#define TRAINER_TEAM_ROCKET_GRUNT_43             537
#define TRAINER_TEAM_ROCKET_GRUNT_44             538
#define TRAINER_TEAM_ROCKET_GRUNT_45             539
#define TRAINER_TEAM_ROCKET_GRUNT_46             540
#define TRAINER_TEAM_ROCKET_GRUNT_47             541
#define TRAINER_TEAM_ROCKET_GRUNT_48             542
#define TRAINER_TEAM_ROCKET_ADMIN                543
#define TRAINER_TEAM_ROCKET_ADMIN_2              544
#define TRAINER_SCIENTIST_GIDEON                 545
#define TRAINER_SWIMMER_FEMALE_AMARA             546
#define TRAINER_SWIMMER_FEMALE_MARIA             547
#define TRAINER_SWIMMER_FEMALE_ABIGAIL           548
#define TRAINER_SWIMMER_MALE_FINN                549
#define TRAINER_SWIMMER_MALE_GARRETT             550
#define TRAINER_FISHERMAN_TOMMY                  551
#define TRAINER_CRUSH_GIRL_TANYA                 552
#define TRAINER_BLACK_BELT_SHEA                  553
#define TRAINER_BLACK_BELT_HUGH                  554
#define TRAINER_CAMPER_BRYCE                     555
#define TRAINER_PICNICKER_CLAIRE                 556
#define TRAINER_CRUSH_KIN_MIK_KIA                557
#define TRAINER_AROMA_LADY_VIOLET                558
#define TRAINER_TUBER_ALEXIS                     559
#define TRAINER_TWINS_JOY_MEG                    560
#define TRAINER_SWIMMER_FEMALE_TISHA             561
#define TRAINER_PAINTER_CELINA                   562
#define TRAINER_PAINTER_RAYNA                    563
#define TRAINER_LADY_GILLIAN                     564
#define TRAINER_YOUNGSTER_DESTIN                 565
#define TRAINER_SWIMMER_MALE_TOBY                566
#define TRAINER_TEAM_ROCKET_GRUNT_49             567
#define TRAINER_TEAM_ROCKET_GRUNT_50             568
#define TRAINER_TEAM_ROCKET_GRUNT_51             569
#define TRAINER_BIRD_KEEPER_MILO                 570
#define TRAINER_BIRD_KEEPER_CHAZ                 571
#define TRAINER_BIRD_KEEPER_HAROLD               572
#define TRAINER_FISHERMAN_TYLOR                  573
#define TRAINER_SWIMMER_MALE_MYMO                574
#define TRAINER_SWIMMER_FEMALE_NICOLE            575
#define TRAINER_SIS_AND_BRO_AVA_GEB              576
#define TRAINER_AROMA_LADY_ROSE                  577
#define TRAINER_SWIMMER_MALE_SAMIR               578
#define TRAINER_SWIMMER_FEMALE_DENISE            579
#define TRAINER_TWINS_MIU_MIA                    580
#define TRAINER_HIKER_EARL                       581
#define TRAINER_RUIN_MANIAC_FOSTER               582
#define TRAINER_RUIN_MANIAC_LARRY                583
#define TRAINER_HIKER_DARYL                      584
#define TRAINER_POKEMANIAC_HECTOR                585
#define TRAINER_PSYCHIC_DARIO                    586
#define TRAINER_PSYCHIC_RODETTE                  587
#define TRAINER_AROMA_LADY_MIAH                  588
#define TRAINER_YOUNG_COUPLE_EVE_JON             589
#define TRAINER_JUGGLER_MASON                    590
#define TRAINER_CRUSH_GIRL_CYNDY                 591
#define TRAINER_CRUSH_GIRL_JOCELYN               592
#define TRAINER_TAMER_EVAN                       593
#define TRAINER_POKEMANIAC_MARK_2                594
#define TRAINER_PKMN_RANGER_LOGAN                595
#define TRAINER_PKMN_RANGER_JACKSON              596
#define TRAINER_PKMN_RANGER_BETH                 597
#define TRAINER_PKMN_RANGER_KATELYN              598
#define TRAINER_COOLTRAINER_LEROY                599
#define TRAINER_COOLTRAINER_MICHELLE             600
#define TRAINER_COOL_COUPLE_LEX_NYA              601
#define TRAINER_RUIN_MANIAC_BRANDON              602
#define TRAINER_RUIN_MANIAC_BENJAMIN             603
#define TRAINER_PAINTER_EDNA                     604
#define TRAINER_GENTLEMAN_CLIFFORD               605
#define TRAINER_LADY_SELPHY                      606
#define TRAINER_RUIN_MANIAC_LAWSON               607
#define TRAINER_PSYCHIC_LAURA                    608
#define TRAINER_PKMN_BREEDER_BETHANY             609
#define TRAINER_PKMN_BREEDER_ALLISON             610
#define TRAINER_BUG_CATCHER_GARRET               611
#define TRAINER_BUG_CATCHER_JONAH                612
#define TRAINER_BUG_CATCHER_VANCE                613
#define TRAINER_YOUNGSTER_NASH                   614
#define TRAINER_YOUNGSTER_CORDELL                615
#define TRAINER_LASS_DALIA                       616
#define TRAINER_LASS_JOANA                       617
#define TRAINER_CAMPER_RILEY                     618
#define TRAINER_PICNICKER_MARCY                  619
#define TRAINER_RUIN_MANIAC_LAYTON               620
#define TRAINER_PICNICKER_KELSEY_2               621
#define TRAINER_PICNICKER_KELSEY_3               622
#define TRAINER_PICNICKER_KELSEY_4               623
#define TRAINER_CAMPER_RICKY_2                   624
#define TRAINER_CAMPER_RICKY_3                   625
#define TRAINER_CAMPER_RICKY_4                   626
#define TRAINER_CAMPER_JEFF_2                    627
#define TRAINER_CAMPER_JEFF_3                    628
#define TRAINER_CAMPER_JEFF_4                    629
#define TRAINER_PICNICKER_ISABELLE_2             630
#define TRAINER_PICNICKER_ISABELLE_3             631
#define TRAINER_PICNICKER_ISABELLE_4             632
#define TRAINER_YOUNGSTER_YASU_2                 633
#define TRAINER_YOUNGSTER_YASU_3                 634
#define TRAINER_ENGINEER_BERNIE_2                635
#define TRAINER_GAMER_DARIAN_2                   636
#define TRAINER_CAMPER_CHRIS_2                   637
#define TRAINER_CAMPER_CHRIS_3                   638
#define TRAINER_CAMPER_CHRIS_4                   639
#define TRAINER_PICNICKER_ALICIA_2               640
#define TRAINER_PICNICKER_ALICIA_3               641
#define TRAINER_PICNICKER_ALICIA_4               642
#define TRAINER_HIKER_JEREMY_2                   643
#define TRAINER_POKEMANIAC_MARK_3                644
#define TRAINER_POKEMANIAC_HERMAN_2              645
#define TRAINER_POKEMANIAC_HERMAN_3              646
#define TRAINER_HIKER_TRENT_2                    647
#define TRAINER_LASS_MEGAN_2                     648
#define TRAINER_LASS_MEGAN_3                     649
#define TRAINER_SUPER_NERD_GLENN_2               650
#define TRAINER_GAMER_RICH_2                     651
#define TRAINER_BIKER_JAREN_2                    652
#define TRAINER_FISHERMAN_ELLIOT_2               653
#define TRAINER_ROCKER_LUCA_2                    654
#define TRAINER_BEAUTY_SHEILA_2                  655
#define TRAINER_BIRD_KEEPER_ROBERT_2             656
#define TRAINER_BIRD_KEEPER_ROBERT_3             657
#define TRAINER_PICNICKER_SUSIE_2                658
#define TRAINER_PICNICKER_SUSIE_3                659
#define TRAINER_PICNICKER_SUSIE_4                660
#define TRAINER_BIKER_LUKAS_2                    661
#define TRAINER_BIRD_KEEPER_BENNY_2              662
#define TRAINER_BIRD_KEEPER_BENNY_3              663
#define TRAINER_BIRD_KEEPER_MARLON_2             664
#define TRAINER_BIRD_KEEPER_MARLON_3             665
#define TRAINER_BEAUTY_GRACE_2                   666
#define TRAINER_BIRD_KEEPER_CHESTER_2            667
#define TRAINER_BIRD_KEEPER_CHESTER_3            668
#define TRAINER_PICNICKER_BECKY_2                669
#define TRAINER_PICNICKER_BECKY_3                670
#define TRAINER_PICNICKER_BECKY_4                671
#define TRAINER_CRUSH_KIN_RON_MYA_2              672
#define TRAINER_CRUSH_KIN_RON_MYA_3              673
#define TRAINER_CRUSH_KIN_RON_MYA_4              674
#define TRAINER_BIKER_RUBEN_2                    675
#define TRAINER_CUE_BALL_CAMRON_2                676
#define TRAINER_BIKER_JAXON_2                    677
#define TRAINER_CUE_BALL_ISAIAH_2                678
#define TRAINER_CUE_BALL_COREY_2                 679
#define TRAINER_BIRD_KEEPER_JACOB_2              680
#define TRAINER_BIRD_KEEPER_JACOB_3              681
#define TRAINER_SWIMMER_FEMALE_ALICE_2           682
#define TRAINER_SWIMMER_MALE_DARRIN_2            683
#define TRAINER_PICNICKER_MISSY_2                684
#define TRAINER_PICNICKER_MISSY_3                685
#define TRAINER_FISHERMAN_WADE_2                 686
#define TRAINER_SWIMMER_MALE_JACK_2              687
#define TRAINER_SIS_AND_BRO_LIL_IAN_2            688
#define TRAINER_SIS_AND_BRO_LIL_IAN_3            689
#define TRAINER_SWIMMER_MALE_FINN_2              690
#define TRAINER_CRUSH_GIRL_SHARON_2              691
#define TRAINER_CRUSH_GIRL_SHARON_3              692
#define TRAINER_CRUSH_GIRL_TANYA_2               693
#define TRAINER_CRUSH_GIRL_TANYA_3               694
#define TRAINER_BLACK_BELT_SHEA_2                695
#define TRAINER_BLACK_BELT_SHEA_3                696
#define TRAINER_BLACK_BELT_HUGH_2                697
#define TRAINER_BLACK_BELT_HUGH_3                698
#define TRAINER_CRUSH_KIN_MIK_KIA_2              699
#define TRAINER_CRUSH_KIN_MIK_KIA_3              700
#define TRAINER_TUBER_AMIRA_2                    701
#define TRAINER_TWINS_JOY_MEG_2                  702
#define TRAINER_PAINTER_RAYNA_2                  703
#define TRAINER_YOUNGSTER_DESTIN_2               704
#define TRAINER_PKMN_BREEDER_ALIZE_2             705
#define TRAINER_YOUNG_COUPLE_GIA_JES_2           706
#define TRAINER_YOUNG_COUPLE_GIA_JES_3           707
#define TRAINER_BIRD_KEEPER_MILO_2               708
#define TRAINER_BIRD_KEEPER_CHAZ_2               709
#define TRAINER_BIRD_KEEPER_HAROLD_2             710
#define TRAINER_SWIMMER_FEMALE_NICOLE_2          711
#define TRAINER_PSYCHIC_JACLYN_2                 712
#define TRAINER_SWIMMER_MALE_SAMIR_2             713
#define TRAINER_HIKER_EARL_2                     714
#define TRAINER_RUIN_MANIAC_LARRY_2              715
#define TRAINER_POKEMANIAC_HECTOR_2              716
#define TRAINER_PSYCHIC_DARIO_2                  717
#define TRAINER_PSYCHIC_RODETTE_2                718
#define TRAINER_JUGGLER_MASON_2                  719
#define TRAINER_PKMN_RANGER_NICOLAS_2            720
#define TRAINER_PKMN_RANGER_MADELINE_2           721
#define TRAINER_CRUSH_GIRL_CYNDY_2               722
#define TRAINER_TAMER_EVAN_2                     723
#define TRAINER_PKMN_RANGER_JACKSON_2            724
#define TRAINER_PKMN_RANGER_KATELYN_2            725
#define TRAINER_COOLTRAINER_LEROY_2              726
#define TRAINER_COOLTRAINER_MICHELLE_2           727
#define TRAINER_COOL_COUPLE_LEX_NYA_2            728
#define TRAINER_BUG_CATCHER_COLTON_2             729
#define TRAINER_BUG_CATCHER_COLTON_3             730
#define TRAINER_BUG_CATCHER_COLTON_4             731
#define TRAINER_SWIMMER_MALE_MATTHEW_2           732
#define TRAINER_SWIMMER_MALE_TONY_2              733
#define TRAINER_SWIMMER_FEMALE_MELISSA_2         734
#define TRAINER_ELITE_FOUR_LORELEI_2             735
#define TRAINER_ELITE_FOUR_BRUNO_2               736
#define TRAINER_ELITE_FOUR_AGATHA_2              737
#define TRAINER_ELITE_FOUR_LANCE_2               738
#define TRAINER_CHAMPION_REMATCH_SQUIRTLE        739
#define TRAINER_CHAMPION_REMATCH_BULBASAUR       740
#define TRAINER_CHAMPION_REMATCH_CHARMANDER      741
#define TRAINER_CUE_BALL_PAXTON                  742

// NOTE: Because each Trainer uses a flag to determine when they are defeated, there is 
//       only space for 25 additional trainers before trainer flag space overflows.
//       MAX_TRAINERS_COUNT can be increased but will take up additional saveblock space

#define NUM_TRAINERS                             743
#define MAX_TRAINERS_COUNT                       768

#endif  // GUARD_CONSTANTS_OPPONENTS_H
