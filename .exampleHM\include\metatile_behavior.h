#ifndef GUARD_METATILE_BEHAVIOR
#define GUARD_METATILE_BEHAVIOR

#include "global.h"

bool8 MetatileBehavior_IsATile(u8 metatileBehavior);
bool8 MetatileBehavior_IsJumpEast(u8 metatileBehavior);
bool8 MetatileBehavior_IsJumpWest(u8 metatileBehavior);
bool8 MetatileBehavior_IsJumpNorth(u8 metatileBehavior);
bool8 MetatileBehavior_IsJumpSouth(u8 metatileBehavior);
bool8 MetatileBehavior_IsPokeGrass(u8 metatileBehavior);
bool8 MetatileBehavior_IsSand(u8 metatileBehavior);
bool8 MetatileBehavior_IsSandOrShallowFlowingWater(u8 metatileBehavior);
bool8 MetatileBehavior_IsDeepSand(u8 metatileBehavior);
bool8 MetatileBehavior_IsReflective(u8 metatileBehavior);
bool8 MetatileBehavior_IsIce(u8 metatileBehavior);
bool8 MetatileBehavior_IsWarpDoor(u8 metatileBehavior);
bool8 MetatileBehavior_IsWarpDoor_2(u8 metatileBehavior);
bool8 MetatileBehavior_IsEscalator(u8 metatileBehavior);
bool8 MetatileBehavior_IsDirectionalUpRightStairWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsDirectionalUpLeftStairWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsDirectionalDownRightStairWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsDirectionalDownLeftStairWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsDirectionalStairWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsLadder(u8 metatileBehavior);
bool8 MetatileBehavior_IsNonAnimDoor(u8 metatileBehavior);
bool8 MetatileBehavior_IsDeepSouthWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsSurfable(u8 metatileBehavior);
bool8 MetatileBehavior_IsFastWater(u8 metatileBehavior);
bool8 MetatileBehavior_IsEastArrowWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsWestArrowWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsNorthArrowWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsSouthArrowWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsForcedMovementTile(u8 metatileBehavior);
bool8 MetatileBehavior_IsIce_2(u8 metatileBehavior);
bool8 MetatileBehavior_IsTrickHouseSlipperyFloor(u8 metatileBehavior);
bool8 MetatileBehavior_IsWalkNorth(u8 metatileBehavior);
bool8 MetatileBehavior_IsWalkSouth(u8 metatileBehavior);
bool8 MetatileBehavior_IsWalkWest(u8 metatileBehavior);
bool8 MetatileBehavior_IsWalkEast(u8 metatileBehavior);
bool8 MetatileBehavior_IsNorthwardCurrent(u8 metatileBehavior);
bool8 MetatileBehavior_IsSouthwardCurrent(u8 metatileBehavior);
bool8 MetatileBehavior_IsWestwardCurrent(u8 metatileBehavior);
bool8 MetatileBehavior_IsEastwardCurrent(u8 metatileBehavior);
bool8 MetatileBehavior_IsSlideNorth(u8 metatileBehavior);
bool8 MetatileBehavior_IsSlideSouth(u8 metatileBehavior);
bool8 MetatileBehavior_IsSlideWest(u8 metatileBehavior);
bool8 MetatileBehavior_IsSlideEast(u8 metatileBehavior);
bool8 MetatileBehavior_IsCounter(u8 metatileBehavior);
bool8 MetatileBehavior_IsPlayerFacingTVScreen(u8 metatileBehavior, u8 playerDirection);
bool8 MetatileBehavior_IsPC(u8 metatileBehavior);
bool8 MetatileBehavior_HasRipples(u8 metatileBehavior);
bool8 MetatileBehavior_IsPuddle(u8 metatileBehavior);
bool8 MetatileBehavior_IsTallGrass(u8 metatileBehavior);
bool8 MetatileBehavior_IsLongGrass(u8 metatileBehavior);
bool8 MetatileBehavior_IsAshGrass(u8 metatileBehavior);
bool8 MetatileBehavior_IsFootprints(u8 metatileBehavior);
bool8 MetatileBehavior_IsBridge(u8 metatileBehavior);
bool8 MetatileBehavior_GetBridgeType(u8 metatileBehavior);
bool8 MetatileBehavior_IsIndoorEncounter(u8 metatileBehavior);
bool8 MetatileBehavior_IsMountain(u8 metatileBehavior);
bool8 MetatileBehavior_IsDiveable(u8 metatileBehavior);
bool8 MetatileBehavior_IsUnableToEmerge(u8 metatileBehavior);
bool8 MetatileBehavior_IsShallowFlowingWater(u8 metatileBehavior);
bool8 MetatileBehavior_IsThinIce(u8 metatileBehavior);
bool8 MetatileBehavior_IsCrackedIce(u8 metatileBehavior);
bool8 MetatileBehavior_IsDeepWaterTerrain(u8 metatileBehavior);
bool8 MetatileBehavior_IsSurfableAndNotWaterfall(u8 metatileBehavior);
bool8 MetatileBehavior_IsEastBlocked(u8 metatileBehavior);
bool8 MetatileBehavior_IsWestBlocked(u8 metatileBehavior);
bool8 MetatileBehavior_IsNorthBlocked(u8 metatileBehavior);
bool8 MetatileBehavior_IsSouthBlocked(u8 metatileBehavior);
bool8 MetatileBehavior_IsShortGrass(u8 metatileBehavior);
bool8 MetatileBehavior_IsHotSprings(u8 metatileBehavior);
bool8 MetatileBehavior_IsWaterfall(u8 metatileBehavior);
bool8 MetatileBehavior_IsFortreeBridge(u8 metatileBehavior);
bool8 MetatileBehavior_IsPacifidlogLog(u8 metatileBehavior);
bool8 MetatileBehavior_IsRegionMap(u8 metatileBehavior);
bool8 MetatileBehavior_IsLavaridgeB1FWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsLavaridge1FWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsWarpPad(u8 metatileBehavior);
bool8 MetatileBehavior_IsUnionRoomWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsWater(u8 metatileBehavior);
bool8 MetatileBehavior_IsFallWarp(u8 metatileBehavior);
bool8 MetatileBehavior_IsCrackedFloor(u8 metatileBehavior);
bool32 MetatileBehavior_IsCyclingRoadPullDownTile(u8 metatileBehavior);
bool8 MetatileBehavior_IsCyclingRoadPullDownTileGrass(u8 metatileBehavior);
bool8 MetatileBehavior_IsBumpySlope(u8 metatileBehavior);
bool8 MetatileBehavior_IsIsolatedVerticalRail(u8 metatileBehavior);
bool8 MetatileBehavior_IsIsolatedHorizontalRail(u8 metatileBehavior);
bool8 MetatileBehavior_IsVerticalRail(u8 metatileBehavior);
bool8 MetatileBehavior_IsHorizontalRail(u8 metatileBehavior);
bool8 MetatileBehavior_IsSeaweed(u8 metatileBehavior);
bool8 MetatileBehavior_IsRunningDisallowed(u8 metatileBehavior);
bool8 MetatileBehavior_IsBookshelf(u8 metatileBehavior);
bool8 MetatileBehavior_IsPokeMartShelf(u8 metatileBehavior);
bool8 MetatileBehavior_IsPlayerFacingPokemonCenterSign(u8 metatileBehavior, u8 playerDirection);
bool8 MetatileBehavior_IsPlayerFacingPokeMartSign(u8 metatileBehavior, u8 playerDirection);
bool8 TestMetatileAttributeBit(u8 attr, u8 bitmask);
bool8 MetatileBehavior_IsSpinRight(u8 metatileBehavior);
bool8 MetatileBehavior_IsSpinLeft(u8 metatileBehavior);
bool8 MetatileBehavior_IsSpinUp(u8 metatileBehavior);
bool8 MetatileBehavior_IsSpinDown(u8 metatileBehavior);
bool8 MetatileBehavior_IsStopSpinning(u8 metatileBehavior);
bool8 MetatileBehavior_IsSpinTile(u8 metatileBehavior);
bool8 MetatileBehavior_IsSignpost(u8 metatileBehavior);
bool8 MetatileBehavior_IsCabinet(u8 metatileBehavior);
bool8 MetatileBehavior_IsKitchen(u8 metatileBehavior);
bool8 MetatileBehavior_IsDresser(u8 metatileBehavior);
bool8 MetatileBehavior_IsSnacks(u8 metatileBehavior);
bool8 MetatileBehavior_IsStrengthButton(u8 metatileBehavior);
bool8 MetatileBehavior_IsPlayerFacingCableClubWirelessMonitor(u8 metatileBehavior, u8 playerDirection);
bool8 MetatileBehavior_IsPlayerFacingBattleRecords(u8 metatileBehavior, u8 playerDirection);
bool8 MetatileBehavior_IsQuestionnaire(u8 metatileBehavior);
bool8 MetatileBehavior_IsIndigoPlateauSign1(u8 metatileBehavior);
bool8 MetatileBehavior_IsIndigoPlateauSign2(u8 metatileBehavior);
bool8 MetatileBehavior_IsFood(u8 metatileBehavior);
bool8 MetatileBehavior_IsRockStairs(u8 metatileBehavior);
bool8 MetatileBehavior_IsBlueprints(u8 metatileBehavior);
bool8 MetatileBehavior_IsPainting(u8 metatileBehavior);
bool8 MetatileBehavior_IsPowerPlantMachine(u8 metatileBehavior);
bool8 MetatileBehavior_IsTelephone(u8 metatileBehavior);
bool8 MetatileBehavior_IsComputer(u8 metatileBehavior);
bool8 MetatileBehavior_IsAdvertisingPoster(u8 metatileBehavior);
bool8 MetatileBehavior_IsTastyFood(u8 metatileBehavior);
bool8 MetatileBehavior_IsTrashBin(u8 metatileBehavior);
bool8 MetatileBehavior_IsCup(u8 metatileBehavior);
bool8 MetatileBehavior_IsPolishedWindow(u8 metatileBehavior);
bool8 MetatileBehavior_IsBeautifulSkyWindow(u8 metatileBehavior);
bool8 MetatileBehavior_IsBlinkingLights(u8 metatileBehavior);
bool8 MetatileBehavior_IsNeatlyLinedUpTools(u8 metatileBehavior);
bool8 MetatileBehavior_IsImpressiveMachine(u8 metatileBehavior);
bool8 MetatileBehavior_IsVideoGame(u8 metatileBehavior);
bool8 MetatileBehavior_IsBurglary(u8 metatileBehavior);
bool8 MetatileBehavior_IsTrainerTowerMonitor(u8 metatileBehavior);
bool8 MetatileBehavior_IsSecretBaseJumpMat(u8 metatileBehavior);
bool8 MetatileBehavior_IsSecretBaseSpinMat(u8 metatileBehavior);

#endif // GUARD_METATILE_BEHAVIOR
