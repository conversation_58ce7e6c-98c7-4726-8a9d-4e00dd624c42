<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="com.dabomstew.pkrandom.newgui.NewGenerationLimitDialog">
  <grid id="27dc6" binding="mainPanel" layout-manager="GridBagLayout">
    <constraints>
      <xy x="20" y="20" width="585" height="400"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <grid id="16c9a" layout-manager="GridBagLayout">
        <constraints>
          <grid row="9" column="3" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <hspacer id="b4439">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
          </hspacer>
          <component id="ee093" class="javax.swing.JButton" binding="cancelButton" default-binding="true">
            <constraints>
              <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <text value="Cancel"/>
            </properties>
          </component>
          <component id="e979" class="javax.swing.JButton" binding="okButton">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
              <gridbag weightx="0.0" weighty="0.0"/>
            </constraints>
            <properties>
              <text value="OK"/>
            </properties>
          </component>
        </children>
      </grid>
      <component id="e29d0" class="javax.swing.JCheckBox" binding="gen1CheckBox">
        <constraints>
          <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text value="Generation 1"/>
        </properties>
      </component>
      <component id="df8a2" class="javax.swing.JCheckBox" binding="gen2CheckBox">
        <constraints>
          <grid row="2" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text value="Generation 2"/>
        </properties>
      </component>
      <component id="c9bd1" class="javax.swing.JCheckBox" binding="gen3CheckBox">
        <constraints>
          <grid row="3" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text value="Generation 3"/>
        </properties>
      </component>
      <component id="6c0c0" class="javax.swing.JCheckBox" binding="gen4CheckBox">
        <constraints>
          <grid row="4" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text value="Generation 4"/>
        </properties>
      </component>
      <component id="9fbe0" class="javax.swing.JCheckBox" binding="gen5CheckBox">
        <constraints>
          <grid row="5" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text value="Generation 5"/>
        </properties>
      </component>
      <component id="88864" class="javax.swing.JCheckBox" binding="gen6CheckBox">
        <constraints>
          <grid row="6" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text value="Generation 6"/>
        </properties>
      </component>
      <component id="8ca76" class="javax.swing.JCheckBox" binding="gen7CheckBox">
        <constraints>
          <grid row="7" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text value="Generation 7"/>
        </properties>
      </component>
      <component id="53cac" class="javax.swing.JLabel" binding="xyWarningLabel">
        <constraints>
          <grid row="11" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="0" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <font style="1"/>
          <text resource-bundle="com/dabomstew/pkrandom/newgui/Bundle" key="GenerationLimitDialog.warningXYLabel.text"/>
        </properties>
      </component>
      <hspacer id="1601f">
        <constraints>
          <grid row="0" column="4" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </hspacer>
      <hspacer id="5ea88">
        <constraints>
          <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </hspacer>
      <vspacer id="efbd">
        <constraints>
          <grid row="10" column="3" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
      </vspacer>
      <component id="4ed0d" class="javax.swing.JLabel">
        <constraints>
          <grid row="0" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <font style="1"/>
          <text value="Include Pokemon from:"/>
        </properties>
      </component>
      <component id="92c12" class="javax.swing.JCheckBox" binding="allowEvolutionaryRelativesCheckBox">
        <constraints>
          <grid row="8" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
          <gridbag weightx="0.0" weighty="0.0"/>
        </constraints>
        <properties>
          <text value="Allow Evolutionary Relatives"/>
        </properties>
      </component>
    </children>
  </grid>
</form>
