#ifndef GUARD_FIELD_WEATHER_EFFECTS_H
#define GUARD_FIELD_WEATHER_EFFECTS_H

bool8 Ash_Finish(void);
bool8 Bubbles_Finish(void);
bool8 Clouds_Finish(void);
bool8 FogHorizontal_Finish(void);
bool8 FogDiagonal_Finish(void);
bool8 Rain_Finish(void);
bool8 Thunderstorm_Finish(void);
bool8 Sandstorm_Finish(void);
bool8 Snow_Finish(void);
bool8 Sunny_Finish(void);
bool8 Drought_Finish(void);
bool8 Shade_Finish(void);
void Ash_InitAll(void);
void Ash_InitVars(void);
void Ash_Main(void);
void Bubbles_InitAll(void);
void Bubbles_InitVars(void);
void Bubbles_Main(void);
void Clouds_InitAll(void);
void Clouds_InitVars(void);
void Clouds_Main(void);
void Drought_InitAll(void);
void Drought_InitVars(void);
void Drought_Main(void);
void FogHorizontal_InitAll(void);
void FogHorizontal_InitVars(void);
void FogHorizontal_Main(void);
void FogDiagonal_InitAll(void);
void FogDiagonal_InitVars(void);
void FogDiagonal_Main(void);
void Rain_InitAll(void);
void Rain_InitVars(void);
void Rain_Main(void);
void Thunderstorm_Main(void);
void Sandstorm_InitAll(void);
void Sandstorm_InitVars(void);
void Sandstorm_Main(void);
void Snow_InitAll(void);
void Snow_InitVars(void);
void Shade_InitAll(void);
void Shade_InitVars(void);
void Sunny_InitAll(void);
void Sunny_InitVars(void);
void Sunny_Main(void);
void Shade_Main(void);
void Snow_Main(void);
void Thunderstorm_InitVars(void);
void Thunderstorm_InitAll(void);
void Downpour_InitVars(void);
void Downpour_InitAll(void);

#endif //GUARD_FIELD_WEATHER_EFFECTS_H
