#ifndef GUARD_MYSTERY_EVENT_MSG_H
#define GUARD_MYSTERY_EVENT_MSG_H

#include "gba/gba.h"

extern const u8 gText_MysteryGiftBerry[];
extern const u8 gText_MysteryGiftBerryTransform[];
extern const u8 gText_MysteryGiftBerryObtained[];
extern const u8 gText_MysteryGiftSpecialRibbon[];
extern const u8 gText_MysteryGiftNationalDex[];
extern const u8 gText_MysteryGiftRareWord[];
extern const u8 gText_MysteryGiftSentOver[];
extern const u8 gText_MysteryGiftFullParty[];
extern const u8 gText_MysteryGiftNewTrainer[];
extern const u8 gText_MysteryGiftNewAdversaryInBattleTower[];
extern const u8 gText_MysteryGiftCantBeUsed[];

#endif // GUARD_MYSTERY_EVENT_MSG_H
