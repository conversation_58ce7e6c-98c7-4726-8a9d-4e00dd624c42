#!/usr/bin/env python3

"""
Shop Modification Script for CFRU
Based on .example project methodology for direct ROM modification.

This script modifies shop inventories directly in the ROM using offsets
extracted from the .example project's gen3_offsets.ini file.
"""

import struct

# FireRed U 1.0 Shop Offsets (from .example project)
FIRERED_SHOP_OFFSETS = {
    0: 0x1649B8,   # Trainer Tower Poké Mart
    1: 0x1676E4,   # Two Island Market Stall (Initial)
    2: 0x1676FC,   # Two Island Market Stall (After Saving Lostelle)
    3: 0x167718,   # Two Island Market Stall (After Hall of Fame)
    4: 0x167738,   # Two Island Market Stall (After Ruby/Sapphire Quest)
    5: 0x16A298,   # Viridian Poké Mart *** TARGET SHOP ***
    6: 0x16A708,   # Pewter Poké Mart
    7: 0x16ACD8,   # Cerulean Poké Mart
    8: 0x16B390,   # Lavender Poké Mart
    9: 0x16B68C,   # Vermillion Poké Mart
    10: 0x16BB38,  # Celadon Department 2F South
    11: 0x16BB74,  # Celadon Department 2F North (TMs)
    12: 0x16BC30,  # Celadon Department 4F *** MAIN GAME ***
    13: 0x16BC84,  # Celadon Department 5F South *** MAIN GAME ***
    14: 0x16BCBC,  # Celadon Department 5F North *** MAIN GAME ***
    15: 0x16D518,  # Fuchsia Poké Mart
    16: 0x16EA48,  # Cinnabar Poké Mart
    17: 0x16EAF4,  # Indigo Plateau Poké Mart
    18: 0x16EFDC,  # Saffron Poké Mart
    19: 0x170B58,  # Seven Island Poké Mart
    20: 0x1718B4,  # Three Island Poké Mart
    21: 0x171CD4,  # Four Island Poké Mart
    22: 0x171E8C   # Six Island Poké Mart
}

# Shop names for reference
SHOP_NAMES = {
    0: "Trainer Tower Poké Mart",
    1: "Two Island Market Stall (Initial)",
    2: "Two Island Market Stall (After Saving Lostelle)",
    3: "Two Island Market Stall (After Hall of Fame)",
    4: "Two Island Market Stall (After Ruby/Sapphire Quest)",
    5: "Viridian Poké Mart",
    6: "Pewter Poké Mart",
    7: "Cerulean Poké Mart",
    8: "Lavender Poké Mart",
    9: "Vermillion Poké Mart",
    10: "Celadon Department 2F South",
    11: "Celadon Department 2F North (TMs)",
    12: "Celadon Department 4F",
    13: "Celadon Department 5F South",
    14: "Celadon Department 5F North",
    15: "Fuchsia Poké Mart",
    16: "Cinnabar Poké Mart",
    17: "Indigo Plateau Poké Mart",
    18: "Saffron Poké Mart",
    19: "Seven Island Poké Mart",
    20: "Three Island Poké Mart",
    21: "Four Island Poké Mart",
    22: "Six Island Poké Mart"
}

# For backward compatibility - use Tier 4 (full items) for simple modification
# Will be defined after VIRIDIAN_TIER4_ITEMS is created

def read_shop_items(rom_data, shop_offset):
    """
    Read items from a shop at the specified offset.
    Returns a list of item IDs (excluding terminator).
    """
    items = []
    offset = shop_offset
    
    while True:
        # Read u16 (little endian)
        item_id = struct.unpack('<H', rom_data[offset:offset+2])[0]
        
        if item_id == 0x0000:  # Terminator
            break
            
        items.append(item_id)
        offset += 2
        
        # Safety check to prevent infinite loop
        if len(items) > 50:
            print(f"Warning: Shop at offset 0x{shop_offset:06X} has more than 50 items!")
            break
    
    return items

def write_shop_items(rom_data, shop_offset, items):
    """
    Write items to a shop at the specified offset.
    Items list should include the 0x0000 terminator.
    """
    offset = shop_offset
    
    for item_id in items:
        # Write u16 (little endian)
        rom_data[offset:offset+2] = struct.pack('<H', item_id)
        offset += 2

def backup_shop(rom_data, shop_id):
    """
    Backup original shop items.
    Returns a list of original item IDs.
    """
    if shop_id not in FIRERED_SHOP_OFFSETS:
        print(f"Error: Invalid shop ID {shop_id}")
        return []
    
    shop_offset = FIRERED_SHOP_OFFSETS[shop_id]
    original_items = read_shop_items(rom_data, shop_offset)
    
    print(f"Backed up {len(original_items)} items from {SHOP_NAMES[shop_id]}")
    return original_items

def modify_shop(rom_data, shop_id, new_items):
    """
    Modify a shop with new items.
    new_items should include the 0x0000 terminator.
    """
    if shop_id not in FIRERED_SHOP_OFFSETS:
        print(f"Error: Invalid shop ID {shop_id}")
        return False
    
    shop_offset = FIRERED_SHOP_OFFSETS[shop_id]
    
    # Backup original items first
    original_items = backup_shop(rom_data, shop_id)
    
    # Write new items
    write_shop_items(rom_data, shop_offset, new_items)
    
    print(f"Modified {SHOP_NAMES[shop_id]} with {len(new_items)-1} new items")
    return True

def generate_bytereplacement_entries(shop_id, items):
    """
    Generate bytereplacement entries for a shop modification.
    Returns a list of strings in the format expected by insert.py.
    """
    if shop_id not in FIRERED_SHOP_OFFSETS:
        print(f"Error: Invalid shop ID {shop_id}")
        return []
    
    shop_offset = FIRERED_SHOP_OFFSETS[shop_id]
    entries = []
    
    # Add header comment
    entries.append(f"# {SHOP_NAMES[shop_id]} - Shop ID {shop_id}")
    entries.append(f"# Offset: 0x{shop_offset:06X}")
    entries.append(f"# Items: {len(items)-1} (excluding terminator)")
    
    # Generate entries for each item
    for i, item_id in enumerate(items):
        offset = shop_offset + (i * 2)
        rom_address = 0x08000000 + offset
        
        # Convert item_id to little endian bytes
        low_byte = item_id & 0xFF
        high_byte = (item_id >> 8) & 0xFF
        
        entries.append(f"{rom_address:08X} {low_byte:02X}")
        entries.append(f"{rom_address+1:08X} {high_byte:02X}")
    
    entries.append("")  # Empty line for separation
    return entries

def modify_viridian_shop_for_testing():
    """
    Generate bytereplacement entries for Viridian shop test modification.
    Uses Tier 4 (full items) for testing purposes.
    """
    print("Generating Viridian shop modification entries...")

    entries = generate_bytereplacement_entries(5, VIRIDIAN_TEST_ITEMS)

    # Write to bytereplacement file
    with open('bytereplacement', 'a') as f:
        f.write("\n# === VIRIDIAN SHOP MODIFICATION (AUTO-GENERATED) ===\n")
        for entry in entries:
            f.write(entry + "\n")

    print(f"Added {len(VIRIDIAN_TEST_ITEMS)-1} items to Viridian shop modification")
    print("Entries written to 'bytereplacement' file")

def generate_progressive_viridian_shop():
    """
    Generate progressive shop modifications for all 4 tiers.
    This creates multiple shop offsets similar to Two Island Market Stall progression.
    """
    print("Generating progressive Viridian shop system...")

    # We'll use unused shop offsets or create new ones
    # For now, let's document the concept and use the main Viridian shop

    tiers = [
        (1, VIRIDIAN_TIER1_ITEMS, "Basic (0 badges)"),
        (2, VIRIDIAN_TIER2_ITEMS, "Intermediate (2+ badges)"),
        (3, VIRIDIAN_TIER3_ITEMS, "Advanced (5+ badges)"),
        (4, VIRIDIAN_TIER4_ITEMS, "Elite (8 badges/Champion)")
    ]

    all_entries = []

    for tier_num, items, description in tiers:
        all_entries.append(f"# === VIRIDIAN SHOP TIER {tier_num}: {description} ===")
        all_entries.append(f"# Items: {len(items)-1} (excluding terminator)")

        # For demonstration, we'll show what the entries would look like
        # In a real implementation, we'd need different offsets for each tier
        entries = generate_bytereplacement_entries(5, items)
        all_entries.extend(entries)
        all_entries.append("")  # Separator

    # Write to template file
    with open('progressive_shop_template.txt', 'w') as f:
        f.write("# Progressive Shop System Template\n")
        f.write("# Based on Two Island Market Stall progression system\n")
        f.write("# Each tier unlocks more items as player progresses\n\n")
        for entry in all_entries:
            f.write(entry + "\n")

    print("Progressive shop template created: 'progressive_shop_template.txt'")
    print("This shows how to implement badge-based progression similar to Two Island Market Stall")

def get_shop_tier_by_badges(badge_count):
    """
    Determine which shop tier should be active based on badge count.
    Mimics the progression system used by Two Island Market Stall.
    """
    if badge_count >= 8:
        return 4, VIRIDIAN_TIER4_ITEMS, "Elite"
    elif badge_count >= 5:
        return 3, VIRIDIAN_TIER3_ITEMS, "Advanced"
    elif badge_count >= 2:
        return 2, VIRIDIAN_TIER2_ITEMS, "Intermediate"
    else:
        return 1, VIRIDIAN_TIER1_ITEMS, "Basic"

def create_shop_modification_template():
    """
    Create a template file showing how to modify different shops.
    """
    template_content = """# Shop Modification Template
# Generated by shop_modifier.py
# 
# This file shows examples of how to modify different shops
# Copy the desired entries to your 'bytereplacement' file

# === VIRIDIAN SHOP MODIFICATION ===
# Adds evolution stones and special items
"""
    
    # Add Viridian shop entries
    viridian_entries = generate_bytereplacement_entries(5, VIRIDIAN_TEST_ITEMS)
    template_content += "\n".join(viridian_entries)
    
    # Add examples for other shops
    template_content += """

# === PEWTER SHOP MODIFICATION (EXAMPLE) ===
# You can modify other shops by changing the shop ID and items
# Example: Add basic evolution stones to Pewter shop
"""
    
    pewter_items = [95, 96, 97, 98, 0x0000]  # Fire, Thunder, Water, Leaf stones + terminator
    pewter_entries = generate_bytereplacement_entries(6, pewter_items)
    template_content += "\n".join(pewter_entries)
    
    with open('shop_modification_template.txt', 'w') as f:
        f.write(template_content)
    
    print("Created 'shop_modification_template.txt' with examples")

def analyze_celadon_shops():
    """
    Analyze Celadon Department Store shops based on .example project data.
    """
    print("\n" + "=" * 60)
    print("CELADON DEPARTMENT STORE ANALYSIS")
    print("Based on .example project for FireRed U 1.0")
    print("=" * 60)

    # Celadon shop data from .example project
    celadon_shops = {
        10: {
            "name": "Celadon Department 2F South",
            "offset": 0x16BB38,
            "purpose": "General Items",
            "description": "Basic healing items and supplies"
        },
        11: {
            "name": "Celadon Department 2F North (TMs)",
            "offset": 0x16BB74,
            "purpose": "Technical Machines",
            "description": "Various TMs for sale"
        },
        12: {
            "name": "Celadon Department 4F *** MAIN GAME ***",
            "offset": 0x16BC30,
            "purpose": "Vitamins",
            "description": "Stat-boosting vitamins (HP Up, Protein, Iron, etc.)"
        },
        13: {
            "name": "Celadon Department 5F South *** MAIN GAME ***",
            "offset": 0x16BC84,
            "purpose": "Special Items",
            "description": "Battle items and special supplies (X items, Guard Spec, etc.)"
        },
        14: {
            "name": "Celadon Department 5F North *** MAIN GAME ***",
            "offset": 0x16BC98,
            "purpose": "Evolution Items",
            "description": "Evolution stones and special evolution items"
        }
    }

    for shop_id, data in celadon_shops.items():
        print(f"\n🏪 Shop ID {shop_id}: {data['name']}")
        print(f"   📍 Offset: 0x{data['offset']:06X}")
        print(f"   🎯 Purpose: {data['purpose']}")
        print(f"   📝 Description: {data['description']}")

        # Show ROM address
        rom_address = 0x08000000 + data['offset']
        print(f"   🔗 ROM Address: 0x{rom_address:08X}")

    print("\n" + "=" * 60)
    print("SHOP SPECIALIZATION ANALYSIS")
    print("=" * 60)

    print("\n🏥 2F South (Shop 10) - HEALING & SUPPLIES")
    print("   Expected items: Potions, Antidotes, Status healers, Repels")
    print("   Category: Basic survival items")

    print("\n💿 2F North (Shop 11) - TM SHOP")
    print("   Expected items: Various Technical Machines")
    print("   Category: Move learning items")

    print("\n💪 4F (Shop 12) - VITAMINS *** MAIN GAME ***")
    print("   Expected items: HP Up, Protein, Iron, Carbos, Calcium, Zinc")
    print("   Category: Stat enhancement items")

    print("\n⚔️ 5F South (Shop 13) - BATTLE ITEMS *** MAIN GAME ***")
    print("   Expected items: X Attack, X Defense, X Speed, Guard Spec, Dire Hit")
    print("   Category: Battle enhancement items")

    print("\n🔮 5F North (Shop 14) - EVOLUTION ITEMS *** MAIN GAME ***")
    print("   Expected items: Fire Stone, Water Stone, Thunder Stone, Leaf Stone")
    print("   Category: Evolution stones and items")

    print("\n" + "=" * 60)
    print("MODIFICATION RECOMMENDATIONS")
    print("=" * 60)

    print("\n📋 For CFRU Enhancement:")
    print("   • Shop 11 (TM): Add new TMs not normally obtainable")
    print("   • Shop 12 (Vitamins): Add new stat items (Bottle Caps, etc.)")
    print("   • Shop 13 (Battle): Add new battle items (Dynamax items, etc.)")
    print("   • Shop 14 (Evolution): Add new evolution stones (Shiny, Dusk, Dawn, Ice)")
    print("   • Shop 10 (General): Add new healing items and supplies")

def generate_celadon_shop_modifications():
    """
    Generate shop modifications for all Celadon Department Store shops.
    """
    print("\n" + "=" * 60)
    print("GENERATING CELADON SHOP MODIFICATIONS")
    print("=" * 60)

    # Define items for each Celadon shop based on their purpose
    celadon_shop_items = {
        10: [  # 2F South - General Items
            # Basic healing
            13,    # ITEM_POTION
            21,    # ITEM_HYPER_POTION
            22,    # ITEM_SUPER_POTION
            20,    # ITEM_MAX_POTION
            19,    # ITEM_FULL_RESTORE
            23,    # ITEM_FULL_HEAL
            24,    # ITEM_REVIVE
            # Status healers
            14,    # ITEM_ANTIDOTE
            15,    # ITEM_BURN_HEAL
            16,    # ITEM_ICE_HEAL
            17,    # ITEM_AWAKENING
            18,    # ITEM_PARALYZE_HEAL
            # Repels
            79,    # ITEM_SUPER_REPEL
            80,    # ITEM_MAX_REPEL
            81,    # ITEM_REPEL
            0x0000 # Terminator
        ],

        11: [  # 2F North - TMs
            # Add new TMs that aren't normally obtainable
            0x18B, # ITEM_TM70 (confirmed working)
            # Add more TMs here as needed
            0x0000 # Terminator
        ],

        12: [  # 4F - Vitamins
            # Original vitamins
            45,    # ITEM_HP_UP
            46,    # ITEM_PROTEIN
            47,    # ITEM_IRON
            48,    # ITEM_CARBOS
            49,    # ITEM_CALCIUM
            50,    # ITEM_ZINC
            # New stat items
            0x2D8, # ITEM_ABILITY_CAPSULE
            0x2D9, # ITEM_ABILITY_PATCH
            0x0000 # Terminator
        ],

        13: [  # 5F South - Battle Items
            # X items
            51,    # ITEM_X_ATTACK
            52,    # ITEM_X_DEFEND
            53,    # ITEM_X_SPEED
            54,    # ITEM_X_ACCURACY
            55,    # ITEM_X_SPECIAL
            56,    # ITEM_GUARD_SPEC
            57,    # ITEM_DIRE_HIT
            # New battle items
            72,    # ITEM_DYNAMAX_CANDY
            0x0000 # Terminator
        ],

        14: [  # 5F North - Evolution Items
            # Original evolution stones
            93,    # ITEM_SUN_STONE
            94,    # ITEM_MOON_STONE
            95,    # ITEM_FIRE_STONE
            96,    # ITEM_THUNDER_STONE
            97,    # ITEM_WATER_STONE
            98,    # ITEM_LEAF_STONE
            # New evolution stones
            99,    # ITEM_SHINY_STONE
            100,   # ITEM_DUSK_STONE
            101,   # ITEM_DAWN_STONE
            102,   # ITEM_ICE_STONE
            # Evolution items
            87,    # ITEM_LINK_CABLE
            88,    # ITEM_PROTECTOR
            89,    # ITEM_ELECTIRIZER
            90,    # ITEM_MAGMARIZER
            91,    # ITEM_DUBIOUS_DISC
            92,    # ITEM_REAPER_CLOTH
            0x0000 # Terminator
        ]
    }

    # Generate bytereplacement entries for each shop
    all_entries = []

    for shop_id, items in celadon_shop_items.items():
        shop_name = SHOP_NAMES[shop_id]
        print(f"\n🏪 Generating {shop_name}")
        print(f"   Items: {len(items)-1} (excluding terminator)")

        entries = generate_bytereplacement_entries(shop_id, items)
        all_entries.extend(entries)
        all_entries.append("")  # Separator

    # Write to bytereplacement file
    with open('bytereplacement', 'a') as f:
        f.write("\n# === CELADON DEPARTMENT STORE MODIFICATIONS (AUTO-GENERATED) ===\n")
        for entry in all_entries:
            f.write(entry + "\n")

    print(f"\n✅ Generated modifications for {len(celadon_shop_items)} Celadon shops")
    print("📝 Entries written to 'bytereplacement' file")

def main():
    """
    Main function - generates shop modifications for bytereplacement.
    """
    print("CFRU Shop Modification Generator")
    print("Based on .example project methodology")
    print("=" * 50)

    # Generate Viridian shop modification (Tier 4 - full items for testing)
    modify_viridian_shop_for_testing()

    # Generate progressive shop system template
    generate_progressive_viridian_shop()

    # Analyze Celadon shops
    analyze_celadon_shops()

    # Generate Celadon shop modifications
    generate_celadon_shop_modifications()

    # Create template file
    create_shop_modification_template()

    print("\n" + "=" * 50)
    print("PROGRESSIVE SHOP SYSTEM ANALYSIS")
    print("=" * 50)

    # Show progression analysis
    for badges in [0, 1, 2, 3, 5, 6, 8]:
        tier, items, tier_name = get_shop_tier_by_badges(badges)
        item_count = len(items) - 1  # Exclude terminator
        print(f"Badges: {badges:2d} | Tier {tier} ({tier_name:12s}) | Items: {item_count:2d}")

    print("\n" + "=" * 50)
    print("IMPLEMENTATION NOTES")
    print("=" * 50)
    print("Current implementation: Single shop with Tier 4 items (testing)")
    print("Progressive system: Requires runtime shop modification based on badges")
    print("Two Island Market Stall uses 4 different shop offsets for progression")
    print("To implement true progression, we need:")
    print("1. Runtime badge checking system")
    print("2. Dynamic shop modification during gameplay")
    print("3. Integration with existing badge flag system")

    print("\nDone! Next steps:")
    print("1. Run 'python scripts/make.py' to compile and insert")
    print("2. The shop modifications will be applied automatically")
    print("3. Test the Viridian Poké Mart in-game")
    print("4. Check 'progressive_shop_template.txt' for progression system details")
    print("5. Test Celadon Department Store shops for new items")

if __name__ == "__main__":
    main()
